# Rekodi App - Appwrite Database Schema

This document outlines the complete database schema for the Rekodi financial management app using Appwrite.

## Project Configuration

- **Project ID**: `683c61460026c3e03330`
- **Database ID**: `rekodi_main_db`
- **Endpoint**: `https://cloud.appwrite.io/v1`

## Collections Overview

### 1. User Accounts Collection (`user_accounts`)

**Purpose**: Store user account information for multi-account support

**Attributes**:
```json
{
  "id": {
    "type": "string",
    "size": 255,
    "required": true,
    "array": false
  },
  "userId": {
    "type": "string",
    "size": 255,
    "required": true,
    "array": false
  },
  "name": {
    "type": "string",
    "size": 255,
    "required": true,
    "array": false
  },
  "email": {
    "type": "string",
    "size": 255,
    "required": true,
    "array": false
  },
  "accountType": {
    "type": "string",
    "size": 50,
    "required": true,
    "array": false
  },
  "isActive": {
    "type": "boolean",
    "required": true,
    "array": false
  },
  "isPremium": {
    "type": "boolean",
    "required": true,
    "array": false
  },
  "profileImageUrl": {
    "type": "string",
    "size": 500,
    "required": false,
    "array": false
  },
  "createdAt": {
    "type": "datetime",
    "required": true,
    "array": false
  },
  "updatedAt": {
    "type": "datetime",
    "required": true,
    "array": false
  }
}
```

**Indexes**:
- `userId` (key)
- `email` (key)
- `isActive` (key)

### 2. Transactions Collection (`transactions`)

**Purpose**: Store all financial transactions (income/expense)

**Attributes**:
```json
{
  "id": {
    "type": "string",
    "size": 255,
    "required": true,
    "array": false
  },
  "accountId": {
    "type": "string",
    "size": 255,
    "required": true,
    "array": false
  },
  "title": {
    "type": "string",
    "size": 255,
    "required": true,
    "array": false
  },
  "description": {
    "type": "string",
    "size": 1000,
    "required": false,
    "array": false
  },
  "amount": {
    "type": "double",
    "required": true,
    "array": false
  },
  "type": {
    "type": "string",
    "size": 50,
    "required": true,
    "array": false
  },
  "category": {
    "type": "string",
    "size": 100,
    "required": true,
    "array": false
  },
  "date": {
    "type": "datetime",
    "required": true,
    "array": false
  },
  "paymentMethod": {
    "type": "string",
    "size": 100,
    "required": false,
    "array": false
  },
  "location": {
    "type": "string",
    "size": 255,
    "required": false,
    "array": false
  },
  "tags": {
    "type": "string",
    "size": 1000,
    "required": false,
    "array": false
  },
  "receiptImageUrl": {
    "type": "string",
    "size": 500,
    "required": false,
    "array": false
  },
  "isRecurring": {
    "type": "boolean",
    "required": true,
    "array": false
  },
  "recurringPattern": {
    "type": "string",
    "size": 100,
    "required": false,
    "array": false
  },
  "nextRecurringDate": {
    "type": "datetime",
    "required": false,
    "array": false
  },
  "isSynced": {
    "type": "boolean",
    "required": true,
    "array": false
  },
  "createdAt": {
    "type": "datetime",
    "required": true,
    "array": false
  },
  "updatedAt": {
    "type": "datetime",
    "required": true,
    "array": false
  }
}
```

**Indexes**:
- `accountId` (key)
- `type` (key)
- `category` (key)
- `date` (key)
- `isRecurring` (key)

### 3. SMS Transactions Collection (`sms_transactions`)

**Purpose**: Store parsed SMS transaction data

**Attributes**:
```json
{
  "id": {
    "type": "string",
    "size": 255,
    "required": true,
    "array": false
  },
  "accountId": {
    "type": "string",
    "size": 255,
    "required": true,
    "array": false
  },
  "rawMessage": {
    "type": "string",
    "size": 2000,
    "required": true,
    "array": false
  },
  "sender": {
    "type": "string",
    "size": 100,
    "required": true,
    "array": false
  },
  "merchantName": {
    "type": "string",
    "size": 255,
    "required": false,
    "array": false
  },
  "amount": {
    "type": "double",
    "required": false,
    "array": false
  },
  "transactionType": {
    "type": "string",
    "size": 50,
    "required": false,
    "array": false
  },
  "category": {
    "type": "string",
    "size": 100,
    "required": false,
    "array": false
  },
  "confidence": {
    "type": "double",
    "required": true,
    "array": false
  },
  "isProcessed": {
    "type": "boolean",
    "required": true,
    "array": false
  },
  "isVerified": {
    "type": "boolean",
    "required": true,
    "array": false
  },
  "linkedTransactionId": {
    "type": "string",
    "size": 255,
    "required": false,
    "array": false
  },
  "receivedAt": {
    "type": "datetime",
    "required": true,
    "array": false
  },
  "processedAt": {
    "type": "datetime",
    "required": false,
    "array": false
  },
  "createdAt": {
    "type": "datetime",
    "required": true,
    "array": false
  }
}
```

**Indexes**:
- `accountId` (key)
- `sender` (key)
- `isProcessed` (key)
- `receivedAt` (key)

### 4. SMS Patterns Collection (`sms_patterns`)

**Purpose**: Store SMS parsing patterns for different banks/services

**Attributes**:
```json
{
  "id": {
    "type": "string",
    "size": 255,
    "required": true,
    "array": false
  },
  "name": {
    "type": "string",
    "size": 255,
    "required": true,
    "array": false
  },
  "sender": {
    "type": "string",
    "size": 100,
    "required": true,
    "array": false
  },
  "pattern": {
    "type": "string",
    "size": 1000,
    "required": true,
    "array": false
  },
  "amountRegex": {
    "type": "string",
    "size": 500,
    "required": true,
    "array": false
  },
  "merchantRegex": {
    "type": "string",
    "size": 500,
    "required": false,
    "array": false
  },
  "typeRegex": {
    "type": "string",
    "size": 500,
    "required": false,
    "array": false
  },
  "isActive": {
    "type": "boolean",
    "required": true,
    "array": false
  },
  "priority": {
    "type": "integer",
    "required": true,
    "array": false
  },
  "successCount": {
    "type": "integer",
    "required": true,
    "array": false
  },
  "totalAttempts": {
    "type": "integer",
    "required": true,
    "array": false
  },
  "createdAt": {
    "type": "datetime",
    "required": true,
    "array": false
  },
  "updatedAt": {
    "type": "datetime",
    "required": true,
    "array": false
  }
}
```

**Indexes**:
- `sender` (key)
- `isActive` (key)
- `priority` (key)

### 5. Budgets Collection (`budgets`)

**Purpose**: Store budget information

**Attributes**:
```json
{
  "id": {
    "type": "string",
    "size": 255,
    "required": true,
    "array": false
  },
  "accountId": {
    "type": "string",
    "size": 255,
    "required": true,
    "array": false
  },
  "name": {
    "type": "string",
    "size": 255,
    "required": true,
    "array": false
  },
  "category": {
    "type": "string",
    "size": 100,
    "required": true,
    "array": false
  },
  "budgetAmount": {
    "type": "double",
    "required": true,
    "array": false
  },
  "spentAmount": {
    "type": "double",
    "required": true,
    "array": false
  },
  "period": {
    "type": "string",
    "size": 50,
    "required": true,
    "array": false
  },
  "startDate": {
    "type": "datetime",
    "required": true,
    "array": false
  },
  "endDate": {
    "type": "datetime",
    "required": true,
    "array": false
  },
  "isActive": {
    "type": "boolean",
    "required": true,
    "array": false
  },
  "alertThreshold": {
    "type": "double",
    "required": false,
    "array": false
  },
  "createdAt": {
    "type": "datetime",
    "required": true,
    "array": false
  },
  "updatedAt": {
    "type": "datetime",
    "required": true,
    "array": false
  }
}
```

**Indexes**:
- `accountId` (key)
- `category` (key)
- `period` (key)
- `isActive` (key)

## Permissions Setup

For each collection, set the following permissions:

### Read Permissions:
- `user:USER_ID` (users can read their own data)

### Write Permissions:
- `user:USER_ID` (users can write their own data)

### Create Permissions:
- `user:USER_ID` (users can create their own data)

### Update Permissions:
- `user:USER_ID` (users can update their own data)

### Delete Permissions:
- `user:USER_ID` (users can delete their own data)

## Security Rules

1. **User Isolation**: All collections should filter by `accountId` or `userId` to ensure users only access their own data
2. **Data Validation**: Use Appwrite's built-in validation for required fields and data types
3. **Rate Limiting**: Implement rate limiting for API calls to prevent abuse

## Next Steps

1. Create the database `rekodi_main_db` in your Appwrite console
2. Create each collection with the specified attributes and indexes
3. Set up the permissions as outlined above
4. Test the connection from the app
5. Implement data sync functionality

## Storage Buckets

Create a storage bucket with ID `rekodi_storage` for:
- Receipt images
- Profile pictures
- Export files

**Bucket Permissions**:
- Read: `user:USER_ID`
- Write: `user:USER_ID`
- Create: `user:USER_ID`
- Update: `user:USER_ID`
- Delete: `user:USER_ID`

**File Size Limits**:
- Maximum file size: 10MB
- Allowed file types: JPEG, PNG, WebP, PDF
