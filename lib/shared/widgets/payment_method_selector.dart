import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../core/constants/payment_methods.dart';
import '../../core/theme/design_system.dart';
import '../../core/theme/typography.dart';

class PaymentMethodSelector extends StatefulWidget {
  final String? selectedPaymentMethod;
  final Function(String?) onPaymentMethodChanged;
  final bool showAllMethods;
  final List<PaymentMethodCategory>? allowedCategories;
  final String? label;
  final String? hint;

  const PaymentMethodSelector({
    super.key,
    this.selectedPaymentMethod,
    required this.onPaymentMethodChanged,
    this.showAllMethods = false,
    this.allowedCategories,
    this.label,
    this.hint,
  });

  @override
  State<PaymentMethodSelector> createState() => _PaymentMethodSelectorState();
}

class _PaymentMethodSelectorState extends State<PaymentMethodSelector> {
  List<PaymentMethodInfo> _getAvailableMethods() {
    if (widget.allowedCategories != null) {
      return PaymentMethods.getAllPaymentMethods()
          .where((method) => widget.allowedCategories!.contains(method.category))
          .toList();
    }
    
    return widget.showAllMethods 
        ? PaymentMethods.getAllPaymentMethods()
        : PaymentMethods.getPopularMethods();
  }

  @override
  Widget build(BuildContext context) {
    final availableMethods = _getAvailableMethods();
    final selectedMethod = widget.selectedPaymentMethod != null
        ? PaymentMethods.getPaymentMethodById(widget.selectedPaymentMethod!)
        : null;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label != null) ...[
          Text(
            widget.label!,
            style: AppTypography.labelMedium.copyWith(
              color: DesignSystem.textPrimary,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
        ],
        Container(
          decoration: BoxDecoration(
            color: DesignSystem.backgroundSecondary,
            borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
            border: Border.all(
              color: DesignSystem.textSecondary.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: widget.selectedPaymentMethod,
              hint: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Text(
                  widget.hint ?? 'Select payment method',
                  style: AppTypography.bodyMedium.copyWith(
                    color: DesignSystem.textSecondary,
                  ),
                ),
              ),
              isExpanded: true,
              icon: Padding(
                padding: const EdgeInsets.only(right: 16),
                child: Icon(
                  Icons.keyboard_arrow_down_rounded,
                  color: DesignSystem.textSecondary,
                ),
              ),
              items: [
                ...availableMethods.map((method) {
                  return DropdownMenuItem<String>(
                    value: method.id,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: method.category.icon == Icons.currency_bitcoin_rounded
                                  ? Colors.orange.withOpacity(0.1)
                                  : method.category.icon == Icons.phone_android_rounded
                                      ? Colors.green.withOpacity(0.1)
                                      : DesignSystem.primaryTeal.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              method.icon,
                              size: 20,
                              color: method.category.icon == Icons.currency_bitcoin_rounded
                                  ? Colors.orange
                                  : method.category.icon == Icons.phone_android_rounded
                                      ? Colors.green
                                      : DesignSystem.primaryTeal,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  method.name,
                                  style: AppTypography.bodyMedium.copyWith(
                                    color: DesignSystem.textPrimary,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                Text(
                                  method.category.displayName,
                                  style: AppTypography.bodySmall.copyWith(
                                    color: DesignSystem.textSecondary,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
                if (widget.showAllMethods) ...[
                  const DropdownMenuItem<String>(
                    value: null,
                    child: Divider(),
                  ),
                  DropdownMenuItem<String>(
                    value: 'show_more',
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Row(
                        children: [
                          Icon(
                            Icons.add_rounded,
                            color: DesignSystem.primaryTeal,
                          ),
                          const SizedBox(width: 12),
                          Text(
                            'Add Custom Method',
                            style: AppTypography.bodyMedium.copyWith(
                              color: DesignSystem.primaryTeal,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ],
              onChanged: (value) {
                if (value == 'show_more') {
                  _showCustomMethodDialog();
                } else {
                  widget.onPaymentMethodChanged(value);
                }
              },
            ),
          ),
        ),
        if (selectedMethod != null) ...[
          const SizedBox(height: 8),
          Text(
            selectedMethod.description,
            style: AppTypography.bodySmall.copyWith(
              color: DesignSystem.textSecondary,
            ),
          ),
        ],
      ],
    );
  }

  void _showCustomMethodDialog() {
    final controller = TextEditingController();
    
    Get.dialog(
      AlertDialog(
        title: const Text('Add Custom Payment Method'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: 'Payment Method Name',
            hintText: 'e.g., PayPal, Stripe, etc.',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              if (controller.text.trim().isNotEmpty) {
                widget.onPaymentMethodChanged(controller.text.trim());
                Get.back();
              }
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }
}

/// Compact payment method selector for smaller spaces
class CompactPaymentMethodSelector extends StatelessWidget {
  final String? selectedPaymentMethod;
  final Function(String?) onPaymentMethodChanged;
  final List<PaymentMethodCategory>? allowedCategories;

  const CompactPaymentMethodSelector({
    super.key,
    this.selectedPaymentMethod,
    required this.onPaymentMethodChanged,
    this.allowedCategories,
  });

  @override
  Widget build(BuildContext context) {
    final methods = allowedCategories != null
        ? PaymentMethods.getAllPaymentMethods()
            .where((method) => allowedCategories!.contains(method.category))
            .toList()
        : PaymentMethods.getPopularMethods();

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: methods.map((method) {
        final isSelected = selectedPaymentMethod == method.id;
        
        return GestureDetector(
          onTap: () => onPaymentMethodChanged(
            isSelected ? null : method.id,
          ),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: isSelected 
                  ? DesignSystem.primaryTeal
                  : DesignSystem.backgroundSecondary,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: isSelected 
                    ? DesignSystem.primaryTeal
                    : DesignSystem.textSecondary.withOpacity(0.3),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  method.icon,
                  size: 16,
                  color: isSelected 
                      ? DesignSystem.white
                      : DesignSystem.textSecondary,
                ),
                const SizedBox(width: 6),
                Text(
                  method.name,
                  style: AppTypography.bodySmall.copyWith(
                    color: isSelected 
                        ? DesignSystem.white
                        : DesignSystem.textPrimary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }
}
