import 'package:flutter/material.dart';
import '../../core/theme/design_system.dart';
import '../../core/theme/typography.dart';

/// Gradient Button Widget
/// Elegant button with gradient background and smooth animations
class GradientButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final LinearGradient? gradient;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final BorderRadius? borderRadius;
  final TextStyle? textStyle;
  final Widget? icon;
  final bool isLoading;
  final bool isEnabled;

  const GradientButton({
    super.key,
    required this.text,
    this.onPressed,
    this.gradient,
    this.width,
    this.height,
    this.padding,
    this.borderRadius,
    this.textStyle,
    this.icon,
    this.isLoading = false,
    this.isEnabled = true,
  });

  @override
  State<GradientButton> createState() => _GradientButtonState();
}

class _GradientButtonState extends State<GradientButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: DesignSystem.animationFast,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: DesignSystem.defaultCurve,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    if (widget.isEnabled && !widget.isLoading) {
      setState(() => _isPressed = true);
      _animationController.forward();
    }
  }

  void _onTapUp(TapUpDetails details) {
    if (widget.isEnabled && !widget.isLoading) {
      setState(() => _isPressed = false);
      _animationController.reverse();
    }
  }

  void _onTapCancel() {
    if (widget.isEnabled && !widget.isLoading) {
      setState(() => _isPressed = false);
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDisabled = !widget.isEnabled || widget.isLoading;
    final gradient = widget.gradient ?? DesignSystem.primaryGradient;
    final borderRadius = widget.borderRadius ?? 
        BorderRadius.circular(DesignSystem.radiusMedium);

    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: _onTapDown,
            onTapUp: _onTapUp,
            onTapCancel: _onTapCancel,
            onTap: isDisabled ? null : widget.onPressed,
            child: AnimatedContainer(
              duration: DesignSystem.animationMedium,
              curve: DesignSystem.defaultCurve,
              width: widget.width,
              height: widget.height ?? 48,
              padding: widget.padding ?? const EdgeInsets.symmetric(
                horizontal: DesignSystem.spaceL,
                vertical: DesignSystem.spaceM,
              ),
              decoration: BoxDecoration(
                gradient: isDisabled 
                    ? LinearGradient(
                        colors: [
                          DesignSystem.textSecondary.withOpacity(0.3),
                          DesignSystem.textSecondary.withOpacity(0.3),
                        ],
                      )
                    : gradient,
                borderRadius: borderRadius,
                boxShadow: isDisabled 
                    ? null 
                    : _isPressed 
                        ? DesignSystem.shadowLow
                        : DesignSystem.shadowMedium,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (widget.isLoading) ...[
                    SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          DesignSystem.textOnPrimary,
                        ),
                      ),
                    ),
                    const SizedBox(width: DesignSystem.spaceS),
                  ] else if (widget.icon != null) ...[
                    widget.icon!,
                    const SizedBox(width: DesignSystem.spaceS),
                  ],
                  Text(
                    widget.text,
                    style: widget.textStyle ?? AppTypography.buttonMedium,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Gradient Outlined Button
/// Elegant outlined button with gradient border
class GradientOutlinedButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final LinearGradient? gradient;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final BorderRadius? borderRadius;
  final TextStyle? textStyle;
  final Widget? icon;
  final bool isLoading;
  final bool isEnabled;
  final double borderWidth;

  const GradientOutlinedButton({
    super.key,
    required this.text,
    this.onPressed,
    this.gradient,
    this.width,
    this.height,
    this.padding,
    this.borderRadius,
    this.textStyle,
    this.icon,
    this.isLoading = false,
    this.isEnabled = true,
    this.borderWidth = 2.0,
  });

  @override
  State<GradientOutlinedButton> createState() => _GradientOutlinedButtonState();
}

class _GradientOutlinedButtonState extends State<GradientOutlinedButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: DesignSystem.animationFast,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: DesignSystem.defaultCurve,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    if (widget.isEnabled && !widget.isLoading) {
      setState(() => _isPressed = true);
      _animationController.forward();
    }
  }

  void _onTapUp(TapUpDetails details) {
    if (widget.isEnabled && !widget.isLoading) {
      setState(() => _isPressed = false);
      _animationController.reverse();
    }
  }

  void _onTapCancel() {
    if (widget.isEnabled && !widget.isLoading) {
      setState(() => _isPressed = false);
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDisabled = !widget.isEnabled || widget.isLoading;
    final gradient = widget.gradient ?? DesignSystem.primaryGradient;
    final borderRadius = widget.borderRadius ?? 
        BorderRadius.circular(DesignSystem.radiusMedium);

    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: _onTapDown,
            onTapUp: _onTapUp,
            onTapCancel: _onTapCancel,
            onTap: isDisabled ? null : widget.onPressed,
            child: AnimatedContainer(
              duration: DesignSystem.animationMedium,
              curve: DesignSystem.defaultCurve,
              width: widget.width,
              height: widget.height ?? 48,
              decoration: BoxDecoration(
                gradient: isDisabled ? null : gradient,
                borderRadius: borderRadius,
              ),
              child: Container(
                margin: EdgeInsets.all(widget.borderWidth),
                padding: widget.padding ?? const EdgeInsets.symmetric(
                  horizontal: DesignSystem.spaceL,
                  vertical: DesignSystem.spaceM,
                ),
                decoration: BoxDecoration(
                  color: DesignSystem.cardBackground,
                  borderRadius: BorderRadius.circular(
                    DesignSystem.radiusMedium - widget.borderWidth,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    if (widget.isLoading) ...[
                      SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            isDisabled 
                                ? DesignSystem.textSecondary
                                : DesignSystem.primaryTeal,
                          ),
                        ),
                      ),
                      const SizedBox(width: DesignSystem.spaceS),
                    ] else if (widget.icon != null) ...[
                      widget.icon!,
                      const SizedBox(width: DesignSystem.spaceS),
                    ],
                    Text(
                      widget.text,
                      style: (widget.textStyle ?? AppTypography.buttonMedium).copyWith(
                        color: isDisabled 
                            ? DesignSystem.textSecondary
                            : DesignSystem.primaryTeal,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
