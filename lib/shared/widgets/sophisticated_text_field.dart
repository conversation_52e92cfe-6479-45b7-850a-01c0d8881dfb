import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../core/theme/design_system.dart';
import '../../core/theme/typography.dart';

/// Sophisticated Text Field Widget
/// Beautiful input field with floating labels, animated borders, and elegant styling
class SophisticatedTextField extends StatefulWidget {
  final TextEditingController controller;
  final String label;
  final String? hint;
  final IconData? prefixIcon;
  final Widget? suffixIcon;
  final bool obscureText;
  final TextInputType keyboardType;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final void Function()? onTap;
  final bool readOnly;
  final int maxLines;
  final TextCapitalization textCapitalization;
  final List<TextInputFormatter>? inputFormatters;
  final bool enabled;
  final FocusNode? focusNode;
  final String? helperText;
  final Color? borderColor;
  final bool showFloatingLabel;

  const SophisticatedTextField({
    super.key,
    required this.controller,
    required this.label,
    this.hint,
    this.prefixIcon,
    this.suffixIcon,
    this.obscureText = false,
    this.keyboardType = TextInputType.text,
    this.validator,
    this.onChanged,
    this.onTap,
    this.readOnly = false,
    this.maxLines = 1,
    this.textCapitalization = TextCapitalization.none,
    this.inputFormatters,
    this.enabled = true,
    this.focusNode,
    this.helperText,
    this.borderColor,
    this.showFloatingLabel = true,
  });

  @override
  State<SophisticatedTextField> createState() => _SophisticatedTextFieldState();
}

class _SophisticatedTextFieldState extends State<SophisticatedTextField>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _borderAnimation;
  late Animation<Color?> _borderColorAnimation;
  late FocusNode _focusNode;
  
  bool _isFocused = false;
  bool _hasError = false;
  String? _errorText;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    
    _animationController = AnimationController(
      duration: DesignSystem.animationMedium,
      vsync: this,
    );

    _borderAnimation = Tween<double>(
      begin: 1.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: DesignSystem.defaultCurve,
    ));

    _borderColorAnimation = ColorTween(
      begin: DesignSystem.textSecondary.withOpacity(0.3),
      end: widget.borderColor ?? DesignSystem.primaryTeal,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: DesignSystem.defaultCurve,
    ));

    _focusNode.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    _animationController.dispose();
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
    });

    if (_isFocused) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  void _validateField() {
    if (widget.validator != null) {
      final error = widget.validator!(widget.controller.text);
      setState(() {
        _hasError = error != null;
        _errorText = error;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
                boxShadow: _isFocused
                    ? [
                        BoxShadow(
                          color: (widget.borderColor ?? DesignSystem.primaryTeal)
                              .withOpacity(0.1),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ]
                    : null,
              ),
              child: TextFormField(
                controller: widget.controller,
                focusNode: _focusNode,
                obscureText: widget.obscureText,
                keyboardType: widget.keyboardType,
                validator: widget.validator,
                onChanged: (value) {
                  widget.onChanged?.call(value);
                  if (_hasError) {
                    _validateField();
                  }
                },
                onTap: widget.onTap,
                readOnly: widget.readOnly,
                enabled: widget.enabled,
                maxLines: widget.maxLines,
                textCapitalization: widget.textCapitalization,
                inputFormatters: widget.inputFormatters,
                style: AppTypography.inputText,
                decoration: InputDecoration(
                  labelText: widget.showFloatingLabel ? widget.label : null,
                  hintText: widget.hint ?? (widget.showFloatingLabel ? null : widget.label),
                  helperText: widget.helperText,
                  errorText: _hasError ? _errorText : null,
                  prefixIcon: widget.prefixIcon != null
                      ? Container(
                          margin: const EdgeInsets.only(
                            left: DesignSystem.spaceM,
                            right: DesignSystem.spaceS,
                          ),
                          child: Icon(
                            widget.prefixIcon,
                            color: _isFocused
                                ? (widget.borderColor ?? DesignSystem.primaryTeal)
                                : DesignSystem.textSecondary,
                            size: 20,
                          ),
                        )
                      : null,
                  suffixIcon: widget.suffixIcon,
                  filled: true,
                  fillColor: DesignSystem.cardBackground,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
                    borderSide: BorderSide(
                      color: DesignSystem.textSecondary.withOpacity(0.3),
                      width: 1.0,
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
                    borderSide: BorderSide(
                      color: _hasError
                          ? DesignSystem.error
                          : DesignSystem.textSecondary.withOpacity(0.3),
                      width: 1.0,
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
                    borderSide: BorderSide(
                      color: _hasError
                          ? DesignSystem.error
                          : _borderColorAnimation.value ?? DesignSystem.primaryTeal,
                      width: _borderAnimation.value,
                    ),
                  ),
                  errorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
                    borderSide: const BorderSide(
                      color: DesignSystem.error,
                      width: 1.0,
                    ),
                  ),
                  focusedErrorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
                    borderSide: const BorderSide(
                      color: DesignSystem.error,
                      width: 2.0,
                    ),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: DesignSystem.spaceM,
                    vertical: DesignSystem.spaceM,
                  ),
                  labelStyle: AppTypography.inputLabel.copyWith(
                    color: _isFocused
                        ? (widget.borderColor ?? DesignSystem.primaryTeal)
                        : DesignSystem.textSecondary,
                  ),
                  floatingLabelStyle: AppTypography.inputLabel.copyWith(
                    color: _isFocused
                        ? (widget.borderColor ?? DesignSystem.primaryTeal)
                        : DesignSystem.textSecondary,
                  ),
                  hintStyle: AppTypography.inputHint,
                  helperStyle: AppTypography.bodySmall.copyWith(
                    color: DesignSystem.textSecondary,
                  ),
                  errorStyle: AppTypography.bodySmall.copyWith(
                    color: DesignSystem.error,
                  ),
                ),
              ),
            );
          },
        ),
      ],
    );
  }
}

/// Password Text Field with visibility toggle
class SophisticatedPasswordField extends StatefulWidget {
  final TextEditingController controller;
  final String label;
  final String? hint;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final bool enabled;
  final FocusNode? focusNode;
  final String? helperText;

  const SophisticatedPasswordField({
    super.key,
    required this.controller,
    required this.label,
    this.hint,
    this.validator,
    this.onChanged,
    this.enabled = true,
    this.focusNode,
    this.helperText,
  });

  @override
  State<SophisticatedPasswordField> createState() => _SophisticatedPasswordFieldState();
}

class _SophisticatedPasswordFieldState extends State<SophisticatedPasswordField> {
  bool _isObscured = true;

  @override
  Widget build(BuildContext context) {
    return SophisticatedTextField(
      controller: widget.controller,
      label: widget.label,
      hint: widget.hint,
      prefixIcon: Icons.lock_outline_rounded,
      suffixIcon: IconButton(
        icon: Icon(
          _isObscured ? Icons.visibility_off_rounded : Icons.visibility_rounded,
          color: DesignSystem.textSecondary,
          size: 20,
        ),
        onPressed: () {
          setState(() {
            _isObscured = !_isObscured;
          });
        },
      ),
      obscureText: _isObscured,
      validator: widget.validator,
      onChanged: widget.onChanged,
      enabled: widget.enabled,
      focusNode: widget.focusNode,
      helperText: widget.helperText,
    );
  }
}
