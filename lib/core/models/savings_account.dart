import 'package:equatable/equatable.dart';

enum SavingsType {
  emergency,
  goal,
  investment,
  retirement,
  vacation,
  education,
  house,
  car,
  other,
}

enum PaymentMethod {
  bank,
  cash,
  crypto,
  mobile_money,
  investment_account,
  other,
}

enum CryptoProvider {
  binance,
  okx,
  metamask,
  coinbase,
  kraken,
  bybit,
  kucoin,
  other,
}

class SavingsAccount extends Equatable {
  final String id;
  final String accountId;
  final String name;
  final String? description;
  final SavingsType type;
  final double targetAmount;
  final double currentAmount;
  final DateTime targetDate;
  final PaymentMethod paymentMethod;
  final String? paymentMethodDescription;
  final CryptoProvider? cryptoProvider;
  final String? cryptoWalletAddress;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const SavingsAccount({
    required this.id,
    required this.accountId,
    required this.name,
    this.description,
    required this.type,
    required this.targetAmount,
    required this.currentAmount,
    required this.targetDate,
    required this.paymentMethod,
    this.paymentMethodDescription,
    this.cryptoProvider,
    this.cryptoWalletAddress,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  double get progressPercentage => targetAmount > 0 ? (currentAmount / targetAmount).clamp(0.0, 1.0) : 0.0;
  double get remainingAmount => (targetAmount - currentAmount).clamp(0.0, double.infinity);
  bool get isCompleted => currentAmount >= targetAmount;
  
  int get daysRemaining {
    final now = DateTime.now();
    if (targetDate.isBefore(now)) return 0;
    return targetDate.difference(now).inDays;
  }

  String get formattedPaymentMethod {
    switch (paymentMethod) {
      case PaymentMethod.bank:
        return 'Bank Account';
      case PaymentMethod.cash:
        return 'Cash';
      case PaymentMethod.crypto:
        return 'Cryptocurrency${cryptoProvider != null ? ' (${_formatCryptoProvider(cryptoProvider!)})' : ''}';
      case PaymentMethod.mobile_money:
        return 'Mobile Money';
      case PaymentMethod.investment_account:
        return 'Investment Account';
      case PaymentMethod.other:
        return paymentMethodDescription ?? 'Other';
    }
  }

  String _formatCryptoProvider(CryptoProvider provider) {
    switch (provider) {
      case CryptoProvider.binance:
        return 'Binance';
      case CryptoProvider.okx:
        return 'OKX';
      case CryptoProvider.metamask:
        return 'MetaMask';
      case CryptoProvider.coinbase:
        return 'Coinbase';
      case CryptoProvider.kraken:
        return 'Kraken';
      case CryptoProvider.bybit:
        return 'Bybit';
      case CryptoProvider.kucoin:
        return 'KuCoin';
      case CryptoProvider.other:
        return 'Other';
    }
  }

  SavingsAccount copyWith({
    String? id,
    String? accountId,
    String? name,
    String? description,
    SavingsType? type,
    double? targetAmount,
    double? currentAmount,
    DateTime? targetDate,
    PaymentMethod? paymentMethod,
    String? paymentMethodDescription,
    CryptoProvider? cryptoProvider,
    String? cryptoWalletAddress,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return SavingsAccount(
      id: id ?? this.id,
      accountId: accountId ?? this.accountId,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      targetAmount: targetAmount ?? this.targetAmount,
      currentAmount: currentAmount ?? this.currentAmount,
      targetDate: targetDate ?? this.targetDate,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      paymentMethodDescription: paymentMethodDescription ?? this.paymentMethodDescription,
      cryptoProvider: cryptoProvider ?? this.cryptoProvider,
      cryptoWalletAddress: cryptoWalletAddress ?? this.cryptoWalletAddress,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'accountId': accountId,
      'name': name,
      'description': description,
      'type': type.name,
      'targetAmount': targetAmount,
      'currentAmount': currentAmount,
      'targetDate': targetDate.toIso8601String(),
      'paymentMethod': paymentMethod.name,
      'paymentMethodDescription': paymentMethodDescription,
      'cryptoProvider': cryptoProvider?.name,
      'cryptoWalletAddress': cryptoWalletAddress,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory SavingsAccount.fromJson(Map<String, dynamic> json) {
    return SavingsAccount(
      id: json['id'],
      accountId: json['accountId'],
      name: json['name'],
      description: json['description'],
      type: SavingsType.values.firstWhere((e) => e.name == json['type']),
      targetAmount: json['targetAmount'].toDouble(),
      currentAmount: json['currentAmount'].toDouble(),
      targetDate: DateTime.parse(json['targetDate']),
      paymentMethod: PaymentMethod.values.firstWhere((e) => e.name == json['paymentMethod']),
      paymentMethodDescription: json['paymentMethodDescription'],
      cryptoProvider: json['cryptoProvider'] != null 
          ? CryptoProvider.values.firstWhere((e) => e.name == json['cryptoProvider'])
          : null,
      cryptoWalletAddress: json['cryptoWalletAddress'],
      isActive: json['isActive'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  @override
  List<Object?> get props => [
        id,
        accountId,
        name,
        description,
        type,
        targetAmount,
        currentAmount,
        targetDate,
        paymentMethod,
        paymentMethodDescription,
        cryptoProvider,
        cryptoWalletAddress,
        isActive,
        createdAt,
        updatedAt,
      ];
}

class SavingsTransaction extends Equatable {
  final String id;
  final String savingsAccountId;
  final double amount;
  final String? description;
  final bool isDeposit; // true for deposit, false for withdrawal
  final DateTime createdAt;

  const SavingsTransaction({
    required this.id,
    required this.savingsAccountId,
    required this.amount,
    this.description,
    required this.isDeposit,
    required this.createdAt,
  });

  SavingsTransaction copyWith({
    String? id,
    String? savingsAccountId,
    double? amount,
    String? description,
    bool? isDeposit,
    DateTime? createdAt,
  }) {
    return SavingsTransaction(
      id: id ?? this.id,
      savingsAccountId: savingsAccountId ?? this.savingsAccountId,
      amount: amount ?? this.amount,
      description: description ?? this.description,
      isDeposit: isDeposit ?? this.isDeposit,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'savingsAccountId': savingsAccountId,
      'amount': amount,
      'description': description,
      'isDeposit': isDeposit,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory SavingsTransaction.fromJson(Map<String, dynamic> json) {
    return SavingsTransaction(
      id: json['id'],
      savingsAccountId: json['savingsAccountId'],
      amount: json['amount'].toDouble(),
      description: json['description'],
      isDeposit: json['isDeposit'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  @override
  List<Object?> get props => [
        id,
        savingsAccountId,
        amount,
        description,
        isDeposit,
        createdAt,
      ];
}

extension SavingsTypeExtension on SavingsType {
  String get displayName {
    switch (this) {
      case SavingsType.emergency:
        return 'Emergency Fund';
      case SavingsType.goal:
        return 'Savings Goal';
      case SavingsType.investment:
        return 'Investment';
      case SavingsType.retirement:
        return 'Retirement';
      case SavingsType.vacation:
        return 'Vacation';
      case SavingsType.education:
        return 'Education';
      case SavingsType.house:
        return 'House';
      case SavingsType.car:
        return 'Car';
      case SavingsType.other:
        return 'Other';
    }
  }

  String get icon {
    switch (this) {
      case SavingsType.emergency:
        return '🚨';
      case SavingsType.goal:
        return '🎯';
      case SavingsType.investment:
        return '📈';
      case SavingsType.retirement:
        return '👴';
      case SavingsType.vacation:
        return '🏖️';
      case SavingsType.education:
        return '🎓';
      case SavingsType.house:
        return '🏠';
      case SavingsType.car:
        return '🚗';
      case SavingsType.other:
        return '💰';
    }
  }
}
