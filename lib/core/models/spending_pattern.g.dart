// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'spending_pattern.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SpendingPattern _$SpendingPatternFromJson(Map<String, dynamic> json) =>
    SpendingPattern(
      category: json['category'] as String,
      trend: json['trend'] as String,
      changePercentage: (json['changePercentage'] as num).toDouble(),
      confidence: (json['confidence'] as num).toDouble(),
      monthlyAverages: (json['monthlyAverages'] as List<dynamic>)
          .map((e) => (e as num).toDouble())
          .toList(),
      analyzedAt: json['analyzedAt'] == null
          ? null
          : DateTime.parse(json['analyzedAt'] as String),
    );

Map<String, dynamic> _$SpendingPatternToJson(SpendingPattern instance) =>
    <String, dynamic>{
      'category': instance.category,
      'trend': instance.trend,
      'changePercentage': instance.changePercentage,
      'confidence': instance.confidence,
      'monthlyAverages': instance.monthlyAverages,
      'analyzedAt': instance.analyzedAt.toIso8601String(),
    };
