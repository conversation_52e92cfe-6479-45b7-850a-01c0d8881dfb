import 'package:json_annotation/json_annotation.dart';

part 'financial_health.g.dart';

@JsonSerializable()
class FinancialHealth {
  final double score; // 0-100
  final String grade; // A+, A, B, C, D, F
  final Map<String, double> factors;
  final List<String> recommendations;
  final DateTime lastCalculated;
  final Map<String, dynamic>? metadata;

  const FinancialHealth({
    required this.score,
    required this.grade,
    required this.factors,
    required this.recommendations,
    required this.lastCalculated,
    this.metadata,
  });

  factory FinancialHealth.fromJson(Map<String, dynamic> json) =>
      _$FinancialHealthFromJson(json);

  Map<String, dynamic> toJson() => _$FinancialHealthToJson(this);

  FinancialHealth copyWith({
    double? score,
    String? grade,
    Map<String, double>? factors,
    List<String>? recommendations,
    DateTime? lastCalculated,
    Map<String, dynamic>? metadata,
  }) {
    return FinancialHealth(
      score: score ?? this.score,
      grade: grade ?? this.grade,
      factors: factors ?? this.factors,
      recommendations: recommendations ?? this.recommendations,
      lastCalculated: lastCalculated ?? this.lastCalculated,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Get health status description
  String get statusDescription {
    if (score >= 90) return 'Excellent';
    if (score >= 80) return 'Very Good';
    if (score >= 70) return 'Good';
    if (score >= 60) return 'Fair';
    if (score >= 50) return 'Poor';
    return 'Critical';
  }

  /// Get health status color
  String get statusColor {
    if (score >= 80) return '#4CAF50'; // Green
    if (score >= 60) return '#FF9800'; // Orange
    return '#F44336'; // Red
  }

  /// Get health status icon
  String get statusIcon {
    if (score >= 90) return '🌟';
    if (score >= 80) return '😊';
    if (score >= 70) return '🙂';
    if (score >= 60) return '😐';
    if (score >= 50) return '😟';
    return '😰';
  }

  /// Get the strongest factor (highest score)
  MapEntry<String, double>? get strongestFactor {
    if (factors.isEmpty) return null;
    return factors.entries.reduce((a, b) => a.value > b.value ? a : b);
  }

  /// Get the weakest factor (lowest score)
  MapEntry<String, double>? get weakestFactor {
    if (factors.isEmpty) return null;
    return factors.entries.reduce((a, b) => a.value < b.value ? a : b);
  }

  /// Get factors that need improvement (score < 15)
  List<MapEntry<String, double>> get factorsNeedingImprovement {
    return factors.entries.where((entry) => entry.value < 15).toList();
  }

  /// Get factors that are performing well (score >= 20)
  List<MapEntry<String, double>> get strongFactors {
    return factors.entries.where((entry) => entry.value >= 20).toList();
  }

  /// Check if health score has improved compared to previous score
  bool hasImproved(double? previousScore) {
    if (previousScore == null) return false;
    return score > previousScore;
  }

  /// Get improvement percentage compared to previous score
  double getImprovementPercentage(double? previousScore) {
    if (previousScore == null || previousScore == 0) return 0.0;
    return ((score - previousScore) / previousScore) * 100;
  }

  /// Get detailed analysis of each factor
  Map<String, String> get factorAnalysis {
    final analysis = <String, String>{};
    
    for (final entry in factors.entries) {
      final factor = entry.key;
      final score = entry.value;
      
      String analysis_text;
      if (score >= 25) {
        analysis_text = 'Excellent performance in $factor';
      } else if (score >= 20) {
        analysis_text = 'Good performance in $factor';
      } else if (score >= 15) {
        analysis_text = 'Average performance in $factor - room for improvement';
      } else if (score >= 10) {
        analysis_text = 'Below average performance in $factor - needs attention';
      } else {
        analysis_text = 'Poor performance in $factor - requires immediate action';
      }
      
      analysis[factor] = analysis_text;
    }
    
    return analysis;
  }

  /// Get priority recommendations (most important first)
  List<String> get priorityRecommendations {
    final prioritized = <String>[];
    
    // Add recommendations for weakest factors first
    final sortedFactors = factors.entries.toList()
      ..sort((a, b) => a.value.compareTo(b.value));
    
    for (final factor in sortedFactors.take(3)) {
      final factorName = factor.key;
      final score = factor.value;
      
      if (score < 15) {
        switch (factorName) {
          case 'Savings Rate':
            prioritized.add('Increase your savings rate to at least 20% of income');
            break;
          case 'Budget Adherence':
            prioritized.add('Improve budget discipline and track expenses more carefully');
            break;
          case 'Income Stability':
            prioritized.add('Work on diversifying and stabilizing income sources');
            break;
          case 'Spending Consistency':
            prioritized.add('Develop more consistent spending habits');
            break;
          case 'Emergency Fund':
            prioritized.add('Build an emergency fund covering 3-6 months of expenses');
            break;
        }
      }
    }
    
    // Add general recommendations
    prioritized.addAll(recommendations);
    
    return prioritized.toSet().toList(); // Remove duplicates
  }

  /// Get next milestone score
  double get nextMilestone {
    if (score < 50) return 50;
    if (score < 60) return 60;
    if (score < 70) return 70;
    if (score < 80) return 80;
    if (score < 90) return 90;
    return 100;
  }

  /// Get points needed to reach next milestone
  double get pointsToNextMilestone {
    return nextMilestone - score;
  }

  /// Check if health data is recent (calculated within last 7 days)
  bool get isRecent {
    return DateTime.now().difference(lastCalculated).inDays < 7;
  }

  /// Get age of health data in days
  int get ageInDays {
    return DateTime.now().difference(lastCalculated).inDays;
  }

  /// Generate a summary report
  String get summaryReport {
    final buffer = StringBuffer();
    
    buffer.writeln('Financial Health Report');
    buffer.writeln('Score: ${score.toStringAsFixed(1)}/100 ($grade)');
    buffer.writeln('Status: $statusDescription');
    buffer.writeln();
    
    buffer.writeln('Factor Breakdown:');
    for (final entry in factors.entries) {
      buffer.writeln('• ${entry.key}: ${entry.value.toStringAsFixed(1)}/30');
    }
    buffer.writeln();
    
    if (recommendations.isNotEmpty) {
      buffer.writeln('Top Recommendations:');
      for (int i = 0; i < recommendations.length && i < 3; i++) {
        buffer.writeln('${i + 1}. ${recommendations[i]}');
      }
    }
    
    return buffer.toString();
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FinancialHealth &&
          runtimeType == other.runtimeType &&
          score == other.score &&
          grade == other.grade;

  @override
  int get hashCode => score.hashCode ^ grade.hashCode;

  @override
  String toString() {
    return 'FinancialHealth{score: ${score.toStringAsFixed(1)}, grade: $grade, status: $statusDescription}';
  }
}
