import 'package:json_annotation/json_annotation.dart';

part 'sync_operation.g.dart';

@JsonSerializable()
class SyncOperation {
  final String id;
  final String type; // 'create', 'update', 'delete'
  final String entityType; // 'transaction', 'budget', 'category', etc.
  final String entityId;
  final Map<String, dynamic> data;
  final DateTime timestamp;
  final String accountId;
  final int retryCount;
  final String? errorMessage;
  final bool isCompleted;

  const SyncOperation({
    required this.id,
    required this.type,
    required this.entityType,
    required this.entityId,
    required this.data,
    required this.timestamp,
    required this.accountId,
    this.retryCount = 0,
    this.errorMessage,
    this.isCompleted = false,
  });

  factory SyncOperation.fromJson(Map<String, dynamic> json) =>
      _$SyncOperationFromJson(json);

  Map<String, dynamic> toJson() => _$SyncOperationToJson(this);

  SyncOperation copyWith({
    String? id,
    String? type,
    String? entityType,
    String? entityId,
    Map<String, dynamic>? data,
    DateTime? timestamp,
    String? accountId,
    int? retryCount,
    String? errorMessage,
    bool? isCompleted,
  }) {
    return SyncOperation(
      id: id ?? this.id,
      type: type ?? this.type,
      entityType: entityType ?? this.entityType,
      entityId: entityId ?? this.entityId,
      data: data ?? this.data,
      timestamp: timestamp ?? this.timestamp,
      accountId: accountId ?? this.accountId,
      retryCount: retryCount ?? this.retryCount,
      errorMessage: errorMessage ?? this.errorMessage,
      isCompleted: isCompleted ?? this.isCompleted,
    );
  }

  /// Create a create operation
  static SyncOperation create({
    required String entityType,
    required String entityId,
    required Map<String, dynamic> data,
    required String accountId,
  }) {
    return SyncOperation(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: 'create',
      entityType: entityType,
      entityId: entityId,
      data: data,
      timestamp: DateTime.now(),
      accountId: accountId,
    );
  }

  /// Create an update operation
  static SyncOperation update({
    required String entityType,
    required String entityId,
    required Map<String, dynamic> data,
    required String accountId,
  }) {
    return SyncOperation(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: 'update',
      entityType: entityType,
      entityId: entityId,
      data: data,
      timestamp: DateTime.now(),
      accountId: accountId,
    );
  }

  /// Create a delete operation
  static SyncOperation delete({
    required String entityType,
    required String entityId,
    required String accountId,
  }) {
    return SyncOperation(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: 'delete',
      entityType: entityType,
      entityId: entityId,
      data: {'id': entityId}, // Minimal data for delete
      timestamp: DateTime.now(),
      accountId: accountId,
    );
  }

  /// Check if operation should be retried
  bool shouldRetry() {
    return retryCount < 3 && !isCompleted;
  }

  /// Get operation priority (lower number = higher priority)
  int get priority {
    switch (type) {
      case 'delete':
        return 1; // Delete operations first
      case 'create':
        return 2; // Create operations second
      case 'update':
        return 3; // Update operations last
      default:
        return 4;
    }
  }

  /// Get human-readable description
  String get description {
    switch (type) {
      case 'create':
        return 'Create ${entityType.toLowerCase()}';
      case 'update':
        return 'Update ${entityType.toLowerCase()}';
      case 'delete':
        return 'Delete ${entityType.toLowerCase()}';
      default:
        return 'Unknown operation';
    }
  }

  /// Get operation age in minutes
  int get ageInMinutes {
    return DateTime.now().difference(timestamp).inMinutes;
  }

  /// Check if operation is stale (older than 24 hours)
  bool get isStale {
    return DateTime.now().difference(timestamp).inHours > 24;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SyncOperation &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'SyncOperation{id: $id, type: $type, entityType: $entityType, entityId: $entityId, timestamp: $timestamp}';
  }
}

/// Sync conflict resolution strategy
enum ConflictResolution {
  serverWins,
  clientWins,
  merge,
  manual,
}

/// Sync conflict model
@JsonSerializable()
class SyncConflict {
  final String id;
  final String entityType;
  final String entityId;
  final Map<String, dynamic> serverData;
  final Map<String, dynamic> clientData;
  final DateTime serverTimestamp;
  final DateTime clientTimestamp;
  final ConflictResolution? resolution;
  final Map<String, dynamic>? resolvedData;
  final DateTime createdAt;

  const SyncConflict({
    required this.id,
    required this.entityType,
    required this.entityId,
    required this.serverData,
    required this.clientData,
    required this.serverTimestamp,
    required this.clientTimestamp,
    this.resolution,
    this.resolvedData,
    required this.createdAt,
  });

  factory SyncConflict.fromJson(Map<String, dynamic> json) =>
      _$SyncConflictFromJson(json);

  Map<String, dynamic> toJson() => _$SyncConflictToJson(this);

  SyncConflict copyWith({
    String? id,
    String? entityType,
    String? entityId,
    Map<String, dynamic>? serverData,
    Map<String, dynamic>? clientData,
    DateTime? serverTimestamp,
    DateTime? clientTimestamp,
    ConflictResolution? resolution,
    Map<String, dynamic>? resolvedData,
    DateTime? createdAt,
  }) {
    return SyncConflict(
      id: id ?? this.id,
      entityType: entityType ?? this.entityType,
      entityId: entityId ?? this.entityId,
      serverData: serverData ?? this.serverData,
      clientData: clientData ?? this.clientData,
      serverTimestamp: serverTimestamp ?? this.serverTimestamp,
      clientTimestamp: clientTimestamp ?? this.clientTimestamp,
      resolution: resolution ?? this.resolution,
      resolvedData: resolvedData ?? this.resolvedData,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  /// Check if conflict is resolved
  bool get isResolved => resolution != null && resolvedData != null;

  /// Get conflict age in hours
  int get ageInHours {
    return DateTime.now().difference(createdAt).inHours;
  }

  /// Determine which data is newer
  bool get serverIsNewer => serverTimestamp.isAfter(clientTimestamp);

  /// Get suggested resolution based on timestamps
  ConflictResolution get suggestedResolution {
    if (serverIsNewer) {
      return ConflictResolution.serverWins;
    } else {
      return ConflictResolution.clientWins;
    }
  }
}

/// Sync statistics model
class SyncStatistics {
  final int totalOperations;
  final int completedOperations;
  final int failedOperations;
  final int pendingOperations;
  final DateTime? lastSyncTime;
  final Duration? averageSyncTime;
  final Map<String, int> operationsByType;
  final List<String> recentErrors;

  const SyncStatistics({
    required this.totalOperations,
    required this.completedOperations,
    required this.failedOperations,
    required this.pendingOperations,
    this.lastSyncTime,
    this.averageSyncTime,
    required this.operationsByType,
    required this.recentErrors,
  });

  /// Calculate success rate as percentage
  double get successRate {
    if (totalOperations == 0) return 100.0;
    return (completedOperations / totalOperations) * 100;
  }

  /// Check if sync is healthy (success rate > 90%)
  bool get isHealthy => successRate > 90.0;

  /// Get status description
  String get statusDescription {
    if (pendingOperations > 0) {
      return '$pendingOperations operations pending';
    } else if (failedOperations > 0) {
      return '$failedOperations operations failed';
    } else {
      return 'All operations synced';
    }
  }
}
