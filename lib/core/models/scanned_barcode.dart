import 'package:flutter/material.dart';
import 'package:google_mlkit_barcode_scanning/google_mlkit_barcode_scanning.dart';

class ScannedBarcode {
  final String id;
  final String rawValue;
  final String displayValue;
  final BarcodeFormat format;
  final BarcodeType type;
  final Rect? boundingBox;
  final List<Offset>? cornerPoints;
  final DateTime scannedAt;
  final Map<String, dynamic>? additionalData;

  ScannedBarcode({
    required this.id,
    required this.rawValue,
    required this.displayValue,
    required this.format,
    required this.type,
    this.boundingBox,
    this.cornerPoints,
    required this.scannedAt,
    this.additionalData,
  });

  factory ScannedBarcode.fromJson(Map<String, dynamic> json) {
    return ScannedBarcode(
      id: json['id'] ?? '',
      rawValue: json['rawValue'] ?? '',
      displayValue: json['displayValue'] ?? '',
      format: _parseFormat(json['format']),
      type: _parseType(json['type']),
      boundingBox: _parseRect(json['boundingBox']),
      cornerPoints: _parseCornerPoints(json['cornerPoints']),
      scannedAt: DateTime.parse(json['scannedAt'] ?? DateTime.now().toIso8601String()),
      additionalData: json['additionalData'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'rawValue': rawValue,
      'displayValue': displayValue,
      'format': format.name,
      'type': type.name,
      'boundingBox': boundingBox != null ? {
        'left': boundingBox!.left,
        'top': boundingBox!.top,
        'right': boundingBox!.right,
        'bottom': boundingBox!.bottom,
      } : null,
      'cornerPoints': cornerPoints?.map((point) => {
        'x': point.dx,
        'y': point.dy,
      }).toList(),
      'scannedAt': scannedAt.toIso8601String(),
      'additionalData': additionalData,
    };
  }

  // Helper methods for parsing
  static BarcodeFormat _parseFormat(dynamic format) {
    if (format is String) {
      switch (format.toLowerCase()) {
        case 'qrcode':
          return BarcodeFormat.qrCode;
        case 'ean13':
          return BarcodeFormat.ean13;
        case 'ean8':
          return BarcodeFormat.ean8;
        case 'code128':
          return BarcodeFormat.code128;
        case 'code39':
          return BarcodeFormat.code39;
        case 'code93':
          return BarcodeFormat.code93;
        case 'codabar':
          return BarcodeFormat.codabar;
        case 'datamatrix':
          return BarcodeFormat.dataMatrix;
        case 'pdf417':
          return BarcodeFormat.pdf417;
        case 'aztec':
          return BarcodeFormat.aztec;
        default:
          return BarcodeFormat.unknown;
      }
    }
    return format as BarcodeFormat? ?? BarcodeFormat.unknown;
  }

  static BarcodeType _parseType(dynamic type) {
    if (type is String) {
      switch (type.toLowerCase()) {
        case 'wifi':
          return BarcodeType.wifi;
        case 'url':
          return BarcodeType.url;
        case 'sms':
          return BarcodeType.sms;
        case 'phone':
          return BarcodeType.phone;
        case 'email':
          return BarcodeType.email;
        case 'contactinfo':
          return BarcodeType.contactInfo;
        case 'calendarevent':
          return BarcodeType.calendarEvent;
        case 'driverlicense':
          return BarcodeType.driverLicense;
        case 'geographiccoordinates':
          return BarcodeType.unknown;
        case 'isbn':
          return BarcodeType.isbn;
        case 'product':
          return BarcodeType.product;
        case 'text':
          return BarcodeType.text;
        default:
          return BarcodeType.unknown;
      }
    }
    return type as BarcodeType? ?? BarcodeType.unknown;
  }

  static Rect? _parseRect(dynamic rect) {
    if (rect is Map<String, dynamic>) {
      return Rect.fromLTRB(
        rect['left']?.toDouble() ?? 0,
        rect['top']?.toDouble() ?? 0,
        rect['right']?.toDouble() ?? 0,
        rect['bottom']?.toDouble() ?? 0,
      );
    }
    return null;
  }

  static List<Offset>? _parseCornerPoints(dynamic points) {
    if (points is List) {
      return points.map((point) {
        if (point is Map<String, dynamic>) {
          return Offset(
            point['x']?.toDouble() ?? 0,
            point['y']?.toDouble() ?? 0,
          );
        }
        return Offset.zero;
      }).toList();
    }
    return null;
  }

  // Utility getters
  String get formatName {
    switch (format) {
      case BarcodeFormat.qrCode:
        return 'QR Code';
      case BarcodeFormat.ean13:
        return 'EAN-13';
      case BarcodeFormat.ean8:
        return 'EAN-8';
      case BarcodeFormat.code128:
        return 'Code 128';
      case BarcodeFormat.code39:
        return 'Code 39';
      case BarcodeFormat.code93:
        return 'Code 93';
      case BarcodeFormat.codabar:
        return 'Codabar';
      case BarcodeFormat.dataMatrix:
        return 'Data Matrix';
      case BarcodeFormat.pdf417:
        return 'PDF417';
      case BarcodeFormat.aztec:
        return 'Aztec';
      default:
        return 'Unknown';
    }
  }

  String get typeName {
    switch (type) {
      case BarcodeType.wifi:
        return 'WiFi';
      case BarcodeType.url:
        return 'URL';
      case BarcodeType.sms:
        return 'SMS';
      case BarcodeType.phone:
        return 'Phone';
      case BarcodeType.email:
        return 'Email';
      case BarcodeType.contactInfo:
        return 'Contact';
      case BarcodeType.calendarEvent:
        return 'Calendar';
      case BarcodeType.driverLicense:
        return 'Driver License';
      case BarcodeType.unknown:
        return 'Location';
      case BarcodeType.isbn:
        return 'ISBN';
      case BarcodeType.product:
        return 'Product';
      case BarcodeType.text:
        return 'Text';
      default:
        return 'Unknown';
    }
  }

  bool get isProduct => type == BarcodeType.product || 
                       format == BarcodeFormat.ean13 || 
                       format == BarcodeFormat.ean8;

  bool get isUrl => type == BarcodeType.url || rawValue.startsWith('http');

  bool get isWifi => type == BarcodeType.wifi;

  bool get isContact => type == BarcodeType.contactInfo;

  bool get isLocation => type == BarcodeType.unknown;

  // Get icon for barcode type
  IconData get icon {
    switch (type) {
      case BarcodeType.wifi:
        return Icons.wifi;
      case BarcodeType.url:
        return Icons.link;
      case BarcodeType.sms:
        return Icons.sms;
      case BarcodeType.phone:
        return Icons.phone;
      case BarcodeType.email:
        return Icons.email;
      case BarcodeType.contactInfo:
        return Icons.contact_page;
      case BarcodeType.calendarEvent:
        return Icons.event;
      case BarcodeType.driverLicense:
        return Icons.badge;
      case BarcodeType.unknown:
        return Icons.location_on;
      case BarcodeType.isbn:
        return Icons.book;
      case BarcodeType.product:
        return Icons.shopping_cart;
      case BarcodeType.text:
        return Icons.text_fields;
      default:
        return Icons.qr_code;
    }
  }

  // Get color for barcode type
  Color get color {
    switch (type) {
      case BarcodeType.wifi:
        return Colors.blue;
      case BarcodeType.url:
        return Colors.green;
      case BarcodeType.sms:
        return Colors.orange;
      case BarcodeType.phone:
        return Colors.purple;
      case BarcodeType.email:
        return Colors.red;
      case BarcodeType.contactInfo:
        return Colors.indigo;
      case BarcodeType.calendarEvent:
        return Colors.teal;
      case BarcodeType.driverLicense:
        return Colors.brown;
      case BarcodeType.unknown:
        return Colors.pink;
      case BarcodeType.isbn:
        return Colors.deepOrange;
      case BarcodeType.product:
        return Colors.cyan;
      case BarcodeType.text:
        return Colors.grey;
      default:
        return Colors.black;
    }
  }

  // Check if barcode is valid
  bool get isValid => rawValue.isNotEmpty && format != BarcodeFormat.unknown;

  // Get formatted display text
  String get formattedValue {
    if (displayValue.isNotEmpty && displayValue != rawValue) {
      return displayValue;
    }
    return rawValue;
  }

  // Get short description
  String get description {
    switch (type) {
      case BarcodeType.wifi:
        return 'WiFi Network Configuration';
      case BarcodeType.url:
        return 'Website Link';
      case BarcodeType.sms:
        return 'SMS Message';
      case BarcodeType.phone:
        return 'Phone Number';
      case BarcodeType.email:
        return 'Email Address';
      case BarcodeType.contactInfo:
        return 'Contact Information';
      case BarcodeType.calendarEvent:
        return 'Calendar Event';
      case BarcodeType.driverLicense:
        return 'Driver License';
      case BarcodeType.unknown:
        return 'Geographic Location';
      case BarcodeType.isbn:
        return 'Book ISBN';
      case BarcodeType.product:
        return 'Product Barcode';
      case BarcodeType.text:
        return 'Text Content';
      default:
        return 'Barcode Data';
    }
  }

  @override
  String toString() {
    return 'ScannedBarcode(id: $id, format: $formatName, type: $typeName, value: $rawValue)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ScannedBarcode &&
        other.id == id &&
        other.rawValue == rawValue &&
        other.format == format &&
        other.type == type;
  }

  @override
  int get hashCode {
    return Object.hash(id, rawValue, format, type);
  }
}
