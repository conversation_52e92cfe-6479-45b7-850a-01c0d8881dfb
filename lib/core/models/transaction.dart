import 'package:flutter/material.dart';

enum TransactionType { income, expense }

enum TransactionCategory {
  // Income Categories
  salary,
  business,
  investment_income,
  dividend_income,
  capital_gains,
  interest_income,
  freelance,
  rental,
  bonus,
  gift,
  other_income,

  // Expense Categories
  food,
  transportation,
  housing,
  utilities,
  healthcare,
  entertainment,
  shopping,
  education,
  insurance,
  debt,
  savings,
  charity,
  travel,
  business_expense,
  transaction_charges,
  bank_fees,
  service_charges,
  other_expense,

  // Savings Categories
  emergency_savings,
  goal_savings,
  retirement_savings,
  education_savings,
  vacation_savings,
  house_savings,
  car_savings,

  // Investment Categories
  stock_investment,
  bond_investment,
  crypto_investment,
  real_estate_investment,
  mutual_fund_investment,
  etf_investment,
  commodity_investment,
  forex_investment,

  // General
  other,
}

extension TransactionCategoryExtension on TransactionCategory {
  String get displayName {
    switch (this) {
      // Income
      case TransactionCategory.salary:
        return 'Salary';
      case TransactionCategory.business:
        return 'Business';
      case TransactionCategory.investment_income:
        return 'Investment Income';
      case TransactionCategory.dividend_income:
        return 'Dividend Income';
      case TransactionCategory.capital_gains:
        return 'Capital Gains';
      case TransactionCategory.interest_income:
        return 'Interest Income';
      case TransactionCategory.freelance:
        return 'Freelance';
      case TransactionCategory.rental:
        return 'Rental Income';
      case TransactionCategory.bonus:
        return 'Bonus';
      case TransactionCategory.gift:
        return 'Gift';
      case TransactionCategory.other_income:
        return 'Other Income';

      // Expenses
      case TransactionCategory.food:
        return 'Food & Dining';
      case TransactionCategory.transportation:
        return 'Transportation';
      case TransactionCategory.housing:
        return 'Housing';
      case TransactionCategory.utilities:
        return 'Utilities';
      case TransactionCategory.healthcare:
        return 'Healthcare';
      case TransactionCategory.entertainment:
        return 'Entertainment';
      case TransactionCategory.shopping:
        return 'Shopping';
      case TransactionCategory.education:
        return 'Education';
      case TransactionCategory.insurance:
        return 'Insurance';
      case TransactionCategory.debt:
        return 'Debt Payment';
      case TransactionCategory.savings:
        return 'Savings';
      case TransactionCategory.charity:
        return 'Charity';
      case TransactionCategory.travel:
        return 'Travel';
      case TransactionCategory.business_expense:
        return 'Business Expense';
      case TransactionCategory.transaction_charges:
        return 'Transaction Charges';
      case TransactionCategory.bank_fees:
        return 'Bank Fees';
      case TransactionCategory.service_charges:
        return 'Service Charges';
      case TransactionCategory.other_expense:
        return 'Other Expense';

      // Savings
      case TransactionCategory.emergency_savings:
        return 'Emergency Savings';
      case TransactionCategory.goal_savings:
        return 'Goal Savings';
      case TransactionCategory.retirement_savings:
        return 'Retirement Savings';
      case TransactionCategory.education_savings:
        return 'Education Savings';
      case TransactionCategory.vacation_savings:
        return 'Vacation Savings';
      case TransactionCategory.house_savings:
        return 'House Savings';
      case TransactionCategory.car_savings:
        return 'Car Savings';

      // Investments
      case TransactionCategory.stock_investment:
        return 'Stock Investment';
      case TransactionCategory.bond_investment:
        return 'Bond Investment';
      case TransactionCategory.crypto_investment:
        return 'Crypto Investment';
      case TransactionCategory.real_estate_investment:
        return 'Real Estate Investment';
      case TransactionCategory.mutual_fund_investment:
        return 'Mutual Fund Investment';
      case TransactionCategory.etf_investment:
        return 'ETF Investment';
      case TransactionCategory.commodity_investment:
        return 'Commodity Investment';
      case TransactionCategory.forex_investment:
        return 'Forex Investment';

      case TransactionCategory.other:
        return 'Other';
    }
  }

  IconData get icon {
    switch (this) {
      // Income
      case TransactionCategory.salary:
        return Icons.work_rounded;
      case TransactionCategory.business:
        return Icons.business_rounded;
      case TransactionCategory.investment_income:
        return Icons.trending_up_rounded;
      case TransactionCategory.dividend_income:
        return Icons.account_balance_rounded;
      case TransactionCategory.capital_gains:
        return Icons.show_chart_rounded;
      case TransactionCategory.interest_income:
        return Icons.percent_rounded;
      case TransactionCategory.freelance:
        return Icons.laptop_rounded;
      case TransactionCategory.rental:
        return Icons.home_rounded;
      case TransactionCategory.bonus:
        return Icons.card_giftcard_rounded;
      case TransactionCategory.gift:
        return Icons.redeem_rounded;
      case TransactionCategory.other_income:
        return Icons.attach_money_rounded;

      // Expenses
      case TransactionCategory.food:
        return Icons.restaurant_rounded;
      case TransactionCategory.transportation:
        return Icons.directions_car_rounded;
      case TransactionCategory.housing:
        return Icons.home_rounded;
      case TransactionCategory.utilities:
        return Icons.electrical_services_rounded;
      case TransactionCategory.healthcare:
        return Icons.local_hospital_rounded;
      case TransactionCategory.entertainment:
        return Icons.movie_rounded;
      case TransactionCategory.shopping:
        return Icons.shopping_bag_rounded;
      case TransactionCategory.education:
        return Icons.school_rounded;
      case TransactionCategory.insurance:
        return Icons.security_rounded;
      case TransactionCategory.debt:
        return Icons.credit_card_rounded;
      case TransactionCategory.savings:
        return Icons.savings_rounded;
      case TransactionCategory.charity:
        return Icons.volunteer_activism_rounded;
      case TransactionCategory.travel:
        return Icons.flight_rounded;
      case TransactionCategory.business_expense:
        return Icons.business_center_rounded;
      case TransactionCategory.other_expense:
        return Icons.more_horiz_rounded;

      // Savings
      case TransactionCategory.emergency_savings:
        return Icons.emergency_rounded;
      case TransactionCategory.goal_savings:
        return Icons.flag_rounded;
      case TransactionCategory.retirement_savings:
        return Icons.elderly_rounded;
      case TransactionCategory.education_savings:
        return Icons.school_rounded;
      case TransactionCategory.vacation_savings:
        return Icons.beach_access_rounded;
      case TransactionCategory.house_savings:
        return Icons.house_rounded;
      case TransactionCategory.car_savings:
        return Icons.directions_car_rounded;

      // Investments
      case TransactionCategory.stock_investment:
        return Icons.trending_up_rounded;
      case TransactionCategory.bond_investment:
        return Icons.account_balance_rounded;
      case TransactionCategory.crypto_investment:
        return Icons.currency_bitcoin_rounded;
      case TransactionCategory.real_estate_investment:
        return Icons.apartment_rounded;
      case TransactionCategory.mutual_fund_investment:
        return Icons.pie_chart_rounded;
      case TransactionCategory.etf_investment:
        return Icons.bar_chart_rounded;
      case TransactionCategory.commodity_investment:
        return Icons.grain_rounded;
      case TransactionCategory.forex_investment:
        return Icons.currency_exchange_rounded;

      case TransactionCategory.other:
        return Icons.category_rounded;
      case TransactionCategory.transaction_charges:
        return Icons.warning_amber_rounded;
      case TransactionCategory.bank_fees:
        return Icons.warning_amber_rounded;
      case TransactionCategory.service_charges:
        return Icons.warning_amber_rounded;
    }
  }

  Color get color {
    switch (this) {
      // Income categories - green shades
      case TransactionCategory.salary:
      case TransactionCategory.business:
      case TransactionCategory.investment_income:
      case TransactionCategory.dividend_income:
      case TransactionCategory.capital_gains:
      case TransactionCategory.interest_income:
      case TransactionCategory.freelance:
      case TransactionCategory.rental:
      case TransactionCategory.bonus:
      case TransactionCategory.gift:
      case TransactionCategory.other_income:
        return Colors.green;

      // Expense categories - various colors
      case TransactionCategory.food:
        return Colors.orange;
      case TransactionCategory.transportation:
        return Colors.blue;
      case TransactionCategory.housing:
        return Colors.brown;
      case TransactionCategory.utilities:
        return Colors.yellow;
      case TransactionCategory.healthcare:
        return Colors.red;
      case TransactionCategory.entertainment:
        return Colors.purple;
      case TransactionCategory.shopping:
        return Colors.pink;
      case TransactionCategory.education:
        return Colors.indigo;
      case TransactionCategory.insurance:
        return Colors.teal;
      case TransactionCategory.debt:
        return Colors.deepOrange;
      case TransactionCategory.savings:
        return Colors.green;
      case TransactionCategory.charity:
        return Colors.lightBlue;
      case TransactionCategory.travel:
        return Colors.cyan;
      case TransactionCategory.business_expense:
        return Colors.grey;
      case TransactionCategory.transaction_charges:
        return Colors.amber;
      case TransactionCategory.bank_fees:
        return Colors.deepOrange;
      case TransactionCategory.service_charges:
        return Colors.orange;
      case TransactionCategory.other_expense:
        return Colors.blueGrey;

      // Savings categories - blue shades
      case TransactionCategory.emergency_savings:
      case TransactionCategory.goal_savings:
      case TransactionCategory.retirement_savings:
      case TransactionCategory.education_savings:
      case TransactionCategory.vacation_savings:
      case TransactionCategory.house_savings:
      case TransactionCategory.car_savings:
        return Colors.blue;

      // Investment categories - purple shades
      case TransactionCategory.stock_investment:
      case TransactionCategory.bond_investment:
      case TransactionCategory.crypto_investment:
      case TransactionCategory.real_estate_investment:
      case TransactionCategory.mutual_fund_investment:
      case TransactionCategory.etf_investment:
      case TransactionCategory.commodity_investment:
      case TransactionCategory.forex_investment:
        return Colors.purple;

      case TransactionCategory.other:
        return Colors.grey.shade600;
    }
  }

  TransactionType get type {
    switch (this) {
      case TransactionCategory.salary:
      case TransactionCategory.business:
      case TransactionCategory.investment_income:
      case TransactionCategory.dividend_income:
      case TransactionCategory.capital_gains:
      case TransactionCategory.interest_income:
      case TransactionCategory.freelance:
      case TransactionCategory.rental:
      case TransactionCategory.bonus:
      case TransactionCategory.gift:
      case TransactionCategory.other_income:
        return TransactionType.income;

      case TransactionCategory.food:
      case TransactionCategory.transportation:
      case TransactionCategory.housing:
      case TransactionCategory.utilities:
      case TransactionCategory.healthcare:
      case TransactionCategory.entertainment:
      case TransactionCategory.shopping:
      case TransactionCategory.education:
      case TransactionCategory.insurance:
      case TransactionCategory.debt:
      case TransactionCategory.savings:
      case TransactionCategory.charity:
      case TransactionCategory.travel:
      case TransactionCategory.business_expense:
      case TransactionCategory.transaction_charges:
      case TransactionCategory.bank_fees:
      case TransactionCategory.service_charges:
      case TransactionCategory.other_expense:
      case TransactionCategory.other:
        return TransactionType.expense;

      // Savings are treated as expenses (money going out)
      case TransactionCategory.emergency_savings:
      case TransactionCategory.goal_savings:
      case TransactionCategory.retirement_savings:
      case TransactionCategory.education_savings:
      case TransactionCategory.vacation_savings:
      case TransactionCategory.house_savings:
      case TransactionCategory.car_savings:
      // Investments are also treated as expenses (money going out)
      case TransactionCategory.stock_investment:
      case TransactionCategory.bond_investment:
      case TransactionCategory.crypto_investment:
      case TransactionCategory.real_estate_investment:
      case TransactionCategory.mutual_fund_investment:
      case TransactionCategory.etf_investment:
      case TransactionCategory.commodity_investment:
      case TransactionCategory.forex_investment:
        return TransactionType.expense;
    }
  }
}

class Transaction {
  final String id;
  final String? accountId;
  final String title;
  final String? description;
  final double amount;
  final TransactionType type;
  final TransactionCategory category;
  final DateTime date;
  final String? paymentMethod;
  final String? location;
  final List<String>? tags;
  final DateTime createdAt;
  final DateTime? updatedAt;

  Transaction({
    required this.id,
    this.accountId,
    required this.title,
    this.description,
    required this.amount,
    required this.type,
    required this.category,
    required this.date,
    this.paymentMethod,
    this.location,
    this.tags,
    required this.createdAt,
    this.updatedAt,
  });

  Transaction copyWith({
    String? id,
    String? title,
    String? description,
    double? amount,
    TransactionType? type,
    TransactionCategory? category,
    DateTime? date,
    String? paymentMethod,
    String? location,
    List<String>? tags,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Transaction(
      id: id ?? this.id,
      accountId: accountId,
      title: title ?? this.title,
      description: description ?? this.description,
      amount: amount ?? this.amount,
      type: type ?? this.type,
      category: category ?? this.category,
      date: date ?? this.date,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      location: location ?? this.location,
      tags: tags ?? this.tags,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'amount': amount,
      'type': type.name,
      'category': category.name,
      'date': date.toIso8601String(),
      'paymentMethod': paymentMethod,
      'location': location,
      'tags': tags,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  factory Transaction.fromJson(Map<String, dynamic> json) {
    return Transaction(
      id: json['id'],
      accountId: json['accountId'],
      title: json['title'],
      description: json['description'],
      amount: json['amount'].toDouble(),
      type: TransactionType.values.firstWhere((e) => e.name == json['type']),
      category: TransactionCategory.values
          .firstWhere((e) => e.name == json['category']),
      date: DateTime.parse(json['date']),
      paymentMethod: json['paymentMethod'],
      location: json['location'],
      tags: json['tags'] != null ? List<String>.from(json['tags']) : null,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt:
          json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
    );
  }
}
