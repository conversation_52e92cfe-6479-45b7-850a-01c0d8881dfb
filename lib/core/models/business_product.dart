import 'package:equatable/equatable.dart';

enum ProductType {
  product,
  service,
}

enum ProductStatus {
  active,
  inactive,
  outOfStock,
}

class BusinessProduct extends Equatable {
  final String id;
  final String accountId;
  final String name;
  final String? description;
  final ProductType type;
  final double price;
  final String? currency;
  final int? quantity; // null for services
  final int? minStockLevel; // null for services
  final String? sku; // Stock Keeping Unit
  final String? barcode;
  final ProductStatus status;
  final String? category;
  final String? imageUrl;
  final Map<String, dynamic>? metadata; // Additional custom fields
  final DateTime createdAt;
  final DateTime updatedAt;

  const BusinessProduct({
    required this.id,
    required this.accountId,
    required this.name,
    this.description,
    required this.type,
    required this.price,
    this.currency,
    this.quantity,
    this.minStockLevel,
    this.sku,
    this.barcode,
    required this.status,
    this.category,
    this.imageUrl,
    this.metadata,
    required this.createdAt,
    required this.updatedAt,
  });

  BusinessProduct copyWith({
    String? id,
    String? accountId,
    String? name,
    String? description,
    ProductType? type,
    double? price,
    String? currency,
    int? quantity,
    int? minStockLevel,
    String? sku,
    String? barcode,
    ProductStatus? status,
    String? category,
    String? imageUrl,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return BusinessProduct(
      id: id ?? this.id,
      accountId: accountId ?? this.accountId,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      price: price ?? this.price,
      currency: currency ?? this.currency,
      quantity: quantity ?? this.quantity,
      minStockLevel: minStockLevel ?? this.minStockLevel,
      sku: sku ?? this.sku,
      barcode: barcode ?? this.barcode,
      status: status ?? this.status,
      category: category ?? this.category,
      imageUrl: imageUrl ?? this.imageUrl,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'accountId': accountId,
      'name': name,
      'description': description,
      'type': type.name,
      'price': price,
      'currency': currency,
      'quantity': quantity,
      'minStockLevel': minStockLevel,
      'sku': sku,
      'barcode': barcode,
      'status': status.name,
      'category': category,
      'imageUrl': imageUrl,
      'metadata': metadata,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory BusinessProduct.fromJson(Map<String, dynamic> json) {
    return BusinessProduct(
      id: json['id'],
      accountId: json['accountId'],
      name: json['name'],
      description: json['description'],
      type: ProductType.values.firstWhere((e) => e.name == json['type']),
      price: json['price'].toDouble(),
      currency: json['currency'],
      quantity: json['quantity'],
      minStockLevel: json['minStockLevel'],
      sku: json['sku'],
      barcode: json['barcode'],
      status: ProductStatus.values.firstWhere((e) => e.name == json['status']),
      category: json['category'],
      imageUrl: json['imageUrl'],
      metadata: json['metadata'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  bool get isProduct => type == ProductType.product;
  bool get isService => type == ProductType.service;
  bool get isActive => status == ProductStatus.active;
  bool get isOutOfStock => status == ProductStatus.outOfStock;
  bool get isLowStock => quantity != null && minStockLevel != null && quantity! <= minStockLevel!;

  @override
  List<Object?> get props => [
        id,
        accountId,
        name,
        description,
        type,
        price,
        currency,
        quantity,
        minStockLevel,
        sku,
        barcode,
        status,
        category,
        imageUrl,
        metadata,
        createdAt,
        updatedAt,
      ];
}

// Business Transaction for sales/purchases
class BusinessTransaction extends Equatable {
  final String id;
  final String accountId;
  final String productId;
  final BusinessProduct product;
  final int quantity;
  final double unitPrice;
  final double totalAmount;
  final BusinessTransactionType type;
  final String? customerName;
  final String? customerContact;
  final String? notes;
  final DateTime transactionDate;
  final DateTime createdAt;
  final DateTime updatedAt;

  const BusinessTransaction({
    required this.id,
    required this.accountId,
    required this.productId,
    required this.product,
    required this.quantity,
    required this.unitPrice,
    required this.totalAmount,
    required this.type,
    this.customerName,
    this.customerContact,
    this.notes,
    required this.transactionDate,
    required this.createdAt,
    required this.updatedAt,
  });

  BusinessTransaction copyWith({
    String? id,
    String? accountId,
    String? productId,
    BusinessProduct? product,
    int? quantity,
    double? unitPrice,
    double? totalAmount,
    BusinessTransactionType? type,
    String? customerName,
    String? customerContact,
    String? notes,
    DateTime? transactionDate,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return BusinessTransaction(
      id: id ?? this.id,
      accountId: accountId ?? this.accountId,
      productId: productId ?? this.productId,
      product: product ?? this.product,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      totalAmount: totalAmount ?? this.totalAmount,
      type: type ?? this.type,
      customerName: customerName ?? this.customerName,
      customerContact: customerContact ?? this.customerContact,
      notes: notes ?? this.notes,
      transactionDate: transactionDate ?? this.transactionDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'accountId': accountId,
      'productId': productId,
      'quantity': quantity,
      'unitPrice': unitPrice,
      'totalAmount': totalAmount,
      'type': type.name,
      'customerName': customerName,
      'customerContact': customerContact,
      'notes': notes,
      'transactionDate': transactionDate.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory BusinessTransaction.fromJson(Map<String, dynamic> json, BusinessProduct product) {
    return BusinessTransaction(
      id: json['id'],
      accountId: json['accountId'],
      productId: json['productId'],
      product: product,
      quantity: json['quantity'],
      unitPrice: json['unitPrice'].toDouble(),
      totalAmount: json['totalAmount'].toDouble(),
      type: BusinessTransactionType.values.firstWhere((e) => e.name == json['type']),
      customerName: json['customerName'],
      customerContact: json['customerContact'],
      notes: json['notes'],
      transactionDate: DateTime.parse(json['transactionDate']),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  bool get isSale => type == BusinessTransactionType.sale;
  bool get isPurchase => type == BusinessTransactionType.purchase;

  @override
  List<Object?> get props => [
        id,
        accountId,
        productId,
        product,
        quantity,
        unitPrice,
        totalAmount,
        type,
        customerName,
        customerContact,
        notes,
        transactionDate,
        createdAt,
        updatedAt,
      ];
}

enum BusinessTransactionType {
  sale,
  purchase,
}
