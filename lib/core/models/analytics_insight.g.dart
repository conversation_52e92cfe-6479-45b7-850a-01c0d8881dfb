// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'analytics_insight.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AnalyticsInsight _$AnalyticsInsightFromJson(Map<String, dynamic> json) =>
    AnalyticsInsight(
      id: json['id'] as String,
      type: $enumDecode(_$InsightTypeEnumMap, json['type']),
      title: json['title'] as String,
      description: json['description'] as String,
      value: (json['value'] as num).toDouble(),
      priority: (json['priority'] as num).toInt(),
      actionable: json['actionable'] as bool? ?? false,
      action: json['action'] as String?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$AnalyticsInsightToJson(AnalyticsInsight instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': _$InsightTypeEnumMap[instance.type]!,
      'title': instance.title,
      'description': instance.description,
      'value': instance.value,
      'priority': instance.priority,
      'actionable': instance.actionable,
      'action': instance.action,
      'createdAt': instance.createdAt.toIso8601String(),
      'metadata': instance.metadata,
    };

const _$InsightTypeEnumMap = {
  InsightType.positive: 'positive',
  InsightType.warning: 'warning',
  InsightType.critical: 'critical',
  InsightType.info: 'info',
};
