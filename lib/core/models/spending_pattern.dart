import 'package:json_annotation/json_annotation.dart';
import 'dart:math' as math;

part 'spending_pattern.g.dart';

@JsonSerializable()
class SpendingPattern {
  final String category;
  final String trend; // 'increasing', 'decreasing', 'stable'
  final double changePercentage;
  final double confidence; // 0.0 to 1.0
  final List<double> monthlyAverages;
  final DateTime analyzedAt;

  SpendingPattern({
    required this.category,
    required this.trend,
    required this.changePercentage,
    required this.confidence,
    required this.monthlyAverages,
    DateTime? analyzedAt,
  }) : analyzedAt = analyzedAt ?? DateTime.now();

  factory SpendingPattern.fromJson(Map<String, dynamic> json) =>
      _$SpendingPatternFromJson(json);

  Map<String, dynamic> toJson() => _$SpendingPatternToJson(this);

  SpendingPattern copyWith({
    String? category,
    String? trend,
    double? changePercentage,
    double? confidence,
    List<double>? monthlyAverages,
    DateTime? analyzedAt,
  }) {
    return SpendingPattern(
      category: category ?? this.category,
      trend: trend ?? this.trend,
      changePercentage: changePercentage ?? this.changePercentage,
      confidence: confidence ?? this.confidence,
      monthlyAverages: monthlyAverages ?? this.monthlyAverages,
      analyzedAt: analyzedAt ?? this.analyzedAt,
    );
  }

  /// Get trend description
  String get trendDescription {
    switch (trend) {
      case 'increasing':
        return 'Spending is increasing by ${changePercentage.toStringAsFixed(1)}% per month';
      case 'decreasing':
        return 'Spending is decreasing by ${changePercentage.toStringAsFixed(1)}% per month';
      case 'stable':
        return 'Spending is relatively stable';
      default:
        return 'Unknown trend';
    }
  }

  /// Get confidence level description
  String get confidenceLevel {
    if (confidence >= 0.9) return 'Very High';
    if (confidence >= 0.7) return 'High';
    if (confidence >= 0.5) return 'Medium';
    if (confidence >= 0.3) return 'Low';
    return 'Very Low';
  }

  /// Get trend icon
  String get trendIcon {
    switch (trend) {
      case 'increasing':
        return '📈';
      case 'decreasing':
        return '📉';
      case 'stable':
        return '➡️';
      default:
        return '❓';
    }
  }

  /// Get trend color
  String get trendColor {
    switch (trend) {
      case 'increasing':
        return '#F44336'; // Red
      case 'decreasing':
        return '#4CAF50'; // Green
      case 'stable':
        return '#2196F3'; // Blue
      default:
        return '#9E9E9E'; // Grey
    }
  }

  /// Calculate average monthly spending
  double get averageMonthlySpending {
    if (monthlyAverages.isEmpty) return 0.0;
    return monthlyAverages.reduce((a, b) => a + b) / monthlyAverages.length;
  }

  /// Get highest monthly spending
  double get highestMonthlySpending {
    if (monthlyAverages.isEmpty) return 0.0;
    return monthlyAverages.reduce((a, b) => a > b ? a : b);
  }

  /// Get lowest monthly spending
  double get lowestMonthlySpending {
    if (monthlyAverages.isEmpty) return 0.0;
    return monthlyAverages.reduce((a, b) => a < b ? a : b);
  }

  /// Calculate volatility (standard deviation / mean)
  double get volatility {
    if (monthlyAverages.length < 2) return 0.0;
    
    final mean = averageMonthlySpending;
    final variance = monthlyAverages
        .map((value) => (value - mean) * (value - mean))
        .reduce((a, b) => a + b) / monthlyAverages.length;
    
    final standardDeviation = variance > 0 ? math.sqrt(variance) : 0.0;
    return mean > 0 ? standardDeviation / mean : 0.0;
  }

  /// Check if pattern is concerning (high volatility or increasing trend)
  bool get isConcerning {
    return (trend == 'increasing' && changePercentage > 10) || volatility > 0.5;
  }

  /// Get recommendation based on pattern
  String get recommendation {
    if (trend == 'increasing' && changePercentage > 15) {
      return 'Consider setting a budget limit for this category to control spending growth';
    } else if (trend == 'increasing' && changePercentage > 5) {
      return 'Monitor this category closely as spending is trending upward';
    } else if (trend == 'decreasing') {
      return 'Great job reducing spending in this category!';
    } else if (volatility > 0.5) {
      return 'Try to maintain more consistent spending in this category';
    } else {
      return 'Spending pattern looks healthy for this category';
    }
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SpendingPattern &&
          runtimeType == other.runtimeType &&
          category == other.category;

  @override
  int get hashCode => category.hashCode;

  @override
  String toString() {
    return 'SpendingPattern{category: $category, trend: $trend, change: ${changePercentage.toStringAsFixed(1)}%, confidence: ${(confidence * 100).toStringAsFixed(1)}%}';
  }
}
