import 'package:flutter/material.dart';

class CountryFlag {
  final String code;
  final String name;
  final String emoji;
  final List<Color> colors;
  final Color primary;
  final Color secondary;
  final Color accent;

  const CountryFlag({
    required this.code,
    required this.name,
    required this.emoji,
    required this.colors,
    required this.primary,
    required this.secondary,
    required this.accent,
  });

  static const List<CountryFlag> flags = [
    // United States
    CountryFlag(
      code: 'US',
      name: 'United States',
      emoji: '🇺🇸',
      colors: [Color(0xFFB22234), Color(0xFFFFFFFF), Color(0xFF3C3B6E)],
      primary: Color(0xFF3C3B6E),
      secondary: Color(0xFFB22234),
      accent: Color(0xFF1976D2),
    ),
    
    // United Kingdom
    CountryFlag(
      code: 'GB',
      name: 'United Kingdom',
      emoji: '🇬🇧',
      colors: [Color(0xFF012169), Color(0xFFFFFFFF), Color(0xFFC8102E)],
      primary: Color(0xFF012169),
      secondary: Color(0xFFC8102E),
      accent: Color(0xFF1565C0),
    ),
    
    // Canada
    CountryFlag(
      code: 'CA',
      name: 'Canada',
      emoji: '🇨🇦',
      colors: [Color(0xFFFF0000), Color(0xFFFFFFFF)],
      primary: Color(0xFFFF0000),
      secondary: Color(0xFFD32F2F),
      accent: Color(0xFFE53935),
    ),
    
    // Germany
    CountryFlag(
      code: 'DE',
      name: 'Germany',
      emoji: '🇩🇪',
      colors: [Color(0xFF000000), Color(0xFFDD0000), Color(0xFFFFCE00)],
      primary: Color(0xFF000000),
      secondary: Color(0xFFDD0000),
      accent: Color(0xFFFFCE00),
    ),
    
    // France
    CountryFlag(
      code: 'FR',
      name: 'France',
      emoji: '🇫🇷',
      colors: [Color(0xFF0055A4), Color(0xFFFFFFFF), Color(0xFFEF4135)],
      primary: Color(0xFF0055A4),
      secondary: Color(0xFFEF4135),
      accent: Color(0xFF1976D2),
    ),
    
    // Japan
    CountryFlag(
      code: 'JP',
      name: 'Japan',
      emoji: '🇯🇵',
      colors: [Color(0xFFBC002D), Color(0xFFFFFFFF)],
      primary: Color(0xFFBC002D),
      secondary: Color(0xFFD32F2F),
      accent: Color(0xFFE57373),
    ),
    
    // Australia
    CountryFlag(
      code: 'AU',
      name: 'Australia',
      emoji: '🇦🇺',
      colors: [Color(0xFF012169), Color(0xFFFFFFFF), Color(0xFFC8102E)],
      primary: Color(0xFF012169),
      secondary: Color(0xFFC8102E),
      accent: Color(0xFF1565C0),
    ),
    
    // Brazil
    CountryFlag(
      code: 'BR',
      name: 'Brazil',
      emoji: '🇧🇷',
      colors: [Color(0xFF009739), Color(0xFFFEDF00), Color(0xFF002776)],
      primary: Color(0xFF009739),
      secondary: Color(0xFFFEDF00),
      accent: Color(0xFF002776),
    ),
    
    // India
    CountryFlag(
      code: 'IN',
      name: 'India',
      emoji: '🇮🇳',
      colors: [Color(0xFFFF9933), Color(0xFFFFFFFF), Color(0xFF138808)],
      primary: Color(0xFF138808),
      secondary: Color(0xFFFF9933),
      accent: Color(0xFF4CAF50),
    ),
    
    // China
    CountryFlag(
      code: 'CN',
      name: 'China',
      emoji: '🇨🇳',
      colors: [Color(0xFFDE2910), Color(0xFFFFDE00)],
      primary: Color(0xFFDE2910),
      secondary: Color(0xFFFFDE00),
      accent: Color(0xFFD32F2F),
    ),
    
    // South Korea
    CountryFlag(
      code: 'KR',
      name: 'South Korea',
      emoji: '🇰🇷',
      colors: [Color(0xFFCD2E3A), Color(0xFF0047A0), Color(0xFFFFFFFF)],
      primary: Color(0xFF0047A0),
      secondary: Color(0xFFCD2E3A),
      accent: Color(0xFF1976D2),
    ),
    
    // Mexico
    CountryFlag(
      code: 'MX',
      name: 'Mexico',
      emoji: '🇲🇽',
      colors: [Color(0xFF006847), Color(0xFFFFFFFF), Color(0xFFCE1126)],
      primary: Color(0xFF006847),
      secondary: Color(0xFFCE1126),
      accent: Color(0xFF4CAF50),
    ),
    
    // Italy
    CountryFlag(
      code: 'IT',
      name: 'Italy',
      emoji: '🇮🇹',
      colors: [Color(0xFF009246), Color(0xFFFFFFFF), Color(0xFFCE2B37)],
      primary: Color(0xFF009246),
      secondary: Color(0xFFCE2B37),
      accent: Color(0xFF4CAF50),
    ),
    
    // Spain
    CountryFlag(
      code: 'ES',
      name: 'Spain',
      emoji: '🇪🇸',
      colors: [Color(0xFFAA151B), Color(0xFFF1BF00)],
      primary: Color(0xFFAA151B),
      secondary: Color(0xFFF1BF00),
      accent: Color(0xFFD32F2F),
    ),
    
    // Netherlands
    CountryFlag(
      code: 'NL',
      name: 'Netherlands',
      emoji: '🇳🇱',
      colors: [Color(0xFFAE1C28), Color(0xFFFFFFFF), Color(0xFF21468B)],
      primary: Color(0xFF21468B),
      secondary: Color(0xFFAE1C28),
      accent: Color(0xFF1976D2),
    ),
    
    // Sweden
    CountryFlag(
      code: 'SE',
      name: 'Sweden',
      emoji: '🇸🇪',
      colors: [Color(0xFF006AA7), Color(0xFFFECC00)],
      primary: Color(0xFF006AA7),
      secondary: Color(0xFFFECC00),
      accent: Color(0xFF1976D2),
    ),
    
    // Norway
    CountryFlag(
      code: 'NO',
      name: 'Norway',
      emoji: '🇳🇴',
      colors: [Color(0xFFEF2B2D), Color(0xFFFFFFFF), Color(0xFF002868)],
      primary: Color(0xFF002868),
      secondary: Color(0xFFEF2B2D),
      accent: Color(0xFF1976D2),
    ),
    
    // Switzerland
    CountryFlag(
      code: 'CH',
      name: 'Switzerland',
      emoji: '🇨🇭',
      colors: [Color(0xFFDA020E), Color(0xFFFFFFFF)],
      primary: Color(0xFFDA020E),
      secondary: Color(0xFFD32F2F),
      accent: Color(0xFFE57373),
    ),
    
    // South Africa
    CountryFlag(
      code: 'ZA',
      name: 'South Africa',
      emoji: '🇿🇦',
      colors: [Color(0xFF007A4D), Color(0xFFFFB612), Color(0xFFDE3831)],
      primary: Color(0xFF007A4D),
      secondary: Color(0xFFDE3831),
      accent: Color(0xFFFFB612),
    ),
    
    // Nigeria
    CountryFlag(
      code: 'NG',
      name: 'Nigeria',
      emoji: '🇳🇬',
      colors: [Color(0xFF008751), Color(0xFFFFFFFF)],
      primary: Color(0xFF008751),
      secondary: Color(0xFF4CAF50),
      accent: Color(0xFF66BB6A),
    ),
    
    // Kenya
    CountryFlag(
      code: 'KE',
      name: 'Kenya',
      emoji: '🇰🇪',
      colors: [Color(0xFF000000), Color(0xFFFF0000), Color(0xFF00A04A)],
      primary: Color(0xFF00A04A),
      secondary: Color(0xFFFF0000),
      accent: Color(0xFF4CAF50),
    ),
  ];

  static CountryFlag getByCode(String code) {
    return flags.firstWhere(
      (flag) => flag.code == code,
      orElse: () => flags.first, // Default to US
    );
  }

  static CountryFlag get defaultFlag => flags.first;
}
