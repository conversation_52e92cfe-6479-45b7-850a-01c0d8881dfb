import 'package:json_annotation/json_annotation.dart';

part 'analytics_insight.g.dart';

enum InsightType {
  positive,
  warning,
  critical,
  info,
}

@JsonSerializable()
class AnalyticsInsight {
  final String id;
  final InsightType type;
  final String title;
  final String description;
  final double value;
  final int priority; // 1-10, higher = more important
  final bool actionable;
  final String? action;
  final DateTime createdAt;
  final Map<String, dynamic>? metadata;

  AnalyticsInsight({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.value,
    required this.priority,
    this.actionable = false,
    this.action,
    DateTime? createdAt,
    this.metadata,
  }) : createdAt = createdAt ?? DateTime.now();

  factory AnalyticsInsight.fromJson(Map<String, dynamic> json) =>
      _$AnalyticsInsightFromJson(json);

  Map<String, dynamic> toJson() => _$AnalyticsInsightToJson(this);

  AnalyticsInsight copyWith({
    String? id,
    InsightType? type,
    String? title,
    String? description,
    double? value,
    int? priority,
    bool? actionable,
    String? action,
    DateTime? createdAt,
    Map<String, dynamic>? metadata,
  }) {
    return AnalyticsInsight(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      description: description ?? this.description,
      value: value ?? this.value,
      priority: priority ?? this.priority,
      actionable: actionable ?? this.actionable,
      action: action ?? this.action,
      createdAt: createdAt ?? this.createdAt,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Get icon for insight type
  String get icon {
    switch (type) {
      case InsightType.positive:
        return '✅';
      case InsightType.warning:
        return '⚠️';
      case InsightType.critical:
        return '🚨';
      case InsightType.info:
        return 'ℹ️';
    }
  }

  /// Get color for insight type
  String get colorHex {
    switch (type) {
      case InsightType.positive:
        return '#4CAF50';
      case InsightType.warning:
        return '#FF9800';
      case InsightType.critical:
        return '#F44336';
      case InsightType.info:
        return '#2196F3';
    }
  }

  /// Check if insight is recent (created within last 24 hours)
  bool get isRecent {
    return DateTime.now().difference(createdAt).inHours < 24;
  }

  /// Get formatted value based on insight type
  String get formattedValue {
    if (id.contains('percentage') || id.contains('rate')) {
      return '${value.toStringAsFixed(1)}%';
    } else if (id.contains('amount') || id.contains('spending')) {
      return 'KES ${value.toStringAsFixed(2)}';
    } else {
      return value.toStringAsFixed(1);
    }
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AnalyticsInsight &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'AnalyticsInsight{id: $id, type: $type, title: $title, priority: $priority}';
  }
}
