// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'export_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ExportData _$ExportDataFromJson(Map<String, dynamic> json) => ExportData(
      transactions: (json['transactions'] as List<dynamic>)
          .map((e) => Transaction.fromJson(e as Map<String, dynamic>))
          .toList(),
      budgets: (json['budgets'] as List<dynamic>)
          .map((e) => Budget.fromJson(e as Map<String, dynamic>))
          .toList(),
      accountId: json['accountId'] as String?,
      exportDate: DateTime.parse(json['exportDate'] as String),
      version: json['version'] as String,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$ExportDataToJson(ExportData instance) =>
    <String, dynamic>{
      'transactions': instance.transactions,
      'budgets': instance.budgets,
      'accountId': instance.accountId,
      'exportDate': instance.exportDate.toIso8601String(),
      'version': instance.version,
      'metadata': instance.metadata,
    };
