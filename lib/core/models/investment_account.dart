import 'package:equatable/equatable.dart';

enum InvestmentType {
  stocks,
  bonds,
  crypto,
  realEstate,
  mutualFunds,
  etf,
  commodities,
  forex,
  other,
}

enum InvestmentPlatform {
  broker,
  bank,
  crypto_exchange,
  robo_advisor,
  direct,
  other,
}

enum RiskLevel {
  low,
  medium,
  high,
  veryHigh,
}

class InvestmentAccount extends Equatable {
  final String id;
  final String accountId;
  final String name;
  final String? description;
  final InvestmentType type;
  final InvestmentPlatform platform;
  final String? platformName;
  final double initialAmount;
  final double currentValue;
  final double totalInvested;
  final double totalWithdrawn;
  final RiskLevel riskLevel;
  final String? currency;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const InvestmentAccount({
    required this.id,
    required this.accountId,
    required this.name,
    this.description,
    required this.type,
    required this.platform,
    this.platformName,
    required this.initialAmount,
    required this.currentValue,
    required this.totalInvested,
    this.totalWithdrawn = 0.0,
    required this.riskLevel,
    this.currency,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  InvestmentAccount copyWith({
    String? id,
    String? accountId,
    String? name,
    String? description,
    InvestmentType? type,
    InvestmentPlatform? platform,
    String? platformName,
    double? initialAmount,
    double? currentValue,
    double? totalInvested,
    double? totalWithdrawn,
    RiskLevel? riskLevel,
    String? currency,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return InvestmentAccount(
      id: id ?? this.id,
      accountId: accountId ?? this.accountId,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      platform: platform ?? this.platform,
      platformName: platformName ?? this.platformName,
      initialAmount: initialAmount ?? this.initialAmount,
      currentValue: currentValue ?? this.currentValue,
      totalInvested: totalInvested ?? this.totalInvested,
      totalWithdrawn: totalWithdrawn ?? this.totalWithdrawn,
      riskLevel: riskLevel ?? this.riskLevel,
      currency: currency ?? this.currency,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'accountId': accountId,
      'name': name,
      'description': description,
      'type': type.name,
      'platform': platform.name,
      'platformName': platformName,
      'initialAmount': initialAmount,
      'currentValue': currentValue,
      'totalInvested': totalInvested,
      'totalWithdrawn': totalWithdrawn,
      'riskLevel': riskLevel.name,
      'currency': currency,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory InvestmentAccount.fromJson(Map<String, dynamic> json) {
    return InvestmentAccount(
      id: json['id'],
      accountId: json['accountId'],
      name: json['name'],
      description: json['description'],
      type: InvestmentType.values.firstWhere((e) => e.name == json['type']),
      platform: InvestmentPlatform.values.firstWhere((e) => e.name == json['platform']),
      platformName: json['platformName'],
      initialAmount: json['initialAmount'].toDouble(),
      currentValue: json['currentValue'].toDouble(),
      totalInvested: json['totalInvested'].toDouble(),
      totalWithdrawn: json['totalWithdrawn']?.toDouble() ?? 0.0,
      riskLevel: RiskLevel.values.firstWhere((e) => e.name == json['riskLevel']),
      currency: json['currency'],
      isActive: json['isActive'] ?? true,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  // Helper methods
  double get totalReturn => currentValue - totalInvested;
  double get returnPercentage => totalInvested > 0 ? (totalReturn / totalInvested) * 100 : 0.0;
  double get netInvested => totalInvested - totalWithdrawn;
  bool get isProfit => totalReturn > 0;

  String get formattedReturn {
    final sign = isProfit ? '+' : '';
    return '$sign${totalReturn.toStringAsFixed(2)}';
  }

  String get formattedReturnPercentage {
    final sign = isProfit ? '+' : '';
    return '$sign${returnPercentage.toStringAsFixed(2)}%';
  }

  @override
  List<Object?> get props => [
        id,
        accountId,
        name,
        description,
        type,
        platform,
        platformName,
        initialAmount,
        currentValue,
        totalInvested,
        totalWithdrawn,
        riskLevel,
        currency,
        isActive,
        createdAt,
        updatedAt,
      ];
}

class InvestmentTransaction extends Equatable {
  final String id;
  final String investmentAccountId;
  final String type; // 'buy', 'sell', 'dividend', 'fee'
  final double amount;
  final double? quantity;
  final double? price;
  final String? description;
  final DateTime transactionDate;
  final DateTime createdAt;

  const InvestmentTransaction({
    required this.id,
    required this.investmentAccountId,
    required this.type,
    required this.amount,
    this.quantity,
    this.price,
    this.description,
    required this.transactionDate,
    required this.createdAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'investmentAccountId': investmentAccountId,
      'type': type,
      'amount': amount,
      'quantity': quantity,
      'price': price,
      'description': description,
      'transactionDate': transactionDate.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory InvestmentTransaction.fromJson(Map<String, dynamic> json) {
    return InvestmentTransaction(
      id: json['id'],
      investmentAccountId: json['investmentAccountId'],
      type: json['type'],
      amount: json['amount'].toDouble(),
      quantity: json['quantity']?.toDouble(),
      price: json['price']?.toDouble(),
      description: json['description'],
      transactionDate: DateTime.parse(json['transactionDate']),
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  @override
  List<Object?> get props => [
        id,
        investmentAccountId,
        type,
        amount,
        quantity,
        price,
        description,
        transactionDate,
        createdAt,
      ];
}

// Extension methods for better display
extension InvestmentTypeExtension on InvestmentType {
  String get displayName {
    switch (this) {
      case InvestmentType.stocks:
        return 'Stocks';
      case InvestmentType.bonds:
        return 'Bonds';
      case InvestmentType.crypto:
        return 'Cryptocurrency';
      case InvestmentType.realEstate:
        return 'Real Estate';
      case InvestmentType.mutualFunds:
        return 'Mutual Funds';
      case InvestmentType.etf:
        return 'ETF';
      case InvestmentType.commodities:
        return 'Commodities';
      case InvestmentType.forex:
        return 'Forex';
      case InvestmentType.other:
        return 'Other';
    }
  }
}

extension RiskLevelExtension on RiskLevel {
  String get displayName {
    switch (this) {
      case RiskLevel.low:
        return 'Low Risk';
      case RiskLevel.medium:
        return 'Medium Risk';
      case RiskLevel.high:
        return 'High Risk';
      case RiskLevel.veryHigh:
        return 'Very High Risk';
    }
  }
}
