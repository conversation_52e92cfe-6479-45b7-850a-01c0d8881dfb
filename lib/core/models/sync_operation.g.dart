// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sync_operation.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SyncOperation _$SyncOperationFromJson(Map<String, dynamic> json) =>
    SyncOperation(
      id: json['id'] as String,
      type: json['type'] as String,
      entityType: json['entityType'] as String,
      entityId: json['entityId'] as String,
      data: json['data'] as Map<String, dynamic>,
      timestamp: DateTime.parse(json['timestamp'] as String),
      accountId: json['accountId'] as String,
      retryCount: (json['retryCount'] as num?)?.toInt() ?? 0,
      errorMessage: json['errorMessage'] as String?,
      isCompleted: json['isCompleted'] as bool? ?? false,
    );

Map<String, dynamic> _$SyncOperationToJson(SyncOperation instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': instance.type,
      'entityType': instance.entityType,
      'entityId': instance.entityId,
      'data': instance.data,
      'timestamp': instance.timestamp.toIso8601String(),
      'accountId': instance.accountId,
      'retryCount': instance.retryCount,
      'errorMessage': instance.errorMessage,
      'isCompleted': instance.isCompleted,
    };

SyncConflict _$SyncConflictFromJson(Map<String, dynamic> json) => SyncConflict(
      id: json['id'] as String,
      entityType: json['entityType'] as String,
      entityId: json['entityId'] as String,
      serverData: json['serverData'] as Map<String, dynamic>,
      clientData: json['clientData'] as Map<String, dynamic>,
      serverTimestamp: DateTime.parse(json['serverTimestamp'] as String),
      clientTimestamp: DateTime.parse(json['clientTimestamp'] as String),
      resolution:
          $enumDecodeNullable(_$ConflictResolutionEnumMap, json['resolution']),
      resolvedData: json['resolvedData'] as Map<String, dynamic>?,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$SyncConflictToJson(SyncConflict instance) =>
    <String, dynamic>{
      'id': instance.id,
      'entityType': instance.entityType,
      'entityId': instance.entityId,
      'serverData': instance.serverData,
      'clientData': instance.clientData,
      'serverTimestamp': instance.serverTimestamp.toIso8601String(),
      'clientTimestamp': instance.clientTimestamp.toIso8601String(),
      'resolution': _$ConflictResolutionEnumMap[instance.resolution],
      'resolvedData': instance.resolvedData,
      'createdAt': instance.createdAt.toIso8601String(),
    };

const _$ConflictResolutionEnumMap = {
  ConflictResolution.serverWins: 'serverWins',
  ConflictResolution.clientWins: 'clientWins',
  ConflictResolution.merge: 'merge',
  ConflictResolution.manual: 'manual',
};
