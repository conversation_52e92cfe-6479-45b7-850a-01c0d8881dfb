enum RecurrenceType {
  daily,
  weekly,
  biweekly,
  monthly,
  quarterly,
  yearly;

  String get displayName {
    switch (this) {
      case RecurrenceType.daily:
        return 'Daily';
      case RecurrenceType.weekly:
        return 'Weekly';
      case RecurrenceType.biweekly:
        return 'Bi-weekly';
      case RecurrenceType.monthly:
        return 'Monthly';
      case RecurrenceType.quarterly:
        return 'Quarterly';
      case RecurrenceType.yearly:
        return 'Yearly';
    }
  }

  String get description {
    switch (this) {
      case RecurrenceType.daily:
        return 'Repeats every day';
      case RecurrenceType.weekly:
        return 'Repeats every week';
      case RecurrenceType.biweekly:
        return 'Repeats every 2 weeks';
      case RecurrenceType.monthly:
        return 'Repeats every month';
      case RecurrenceType.quarterly:
        return 'Repeats every 3 months';
      case RecurrenceType.yearly:
        return 'Repeats every year';
    }
  }

  Duration get duration {
    switch (this) {
      case RecurrenceType.daily:
        return const Duration(days: 1);
      case RecurrenceType.weekly:
        return const Duration(days: 7);
      case RecurrenceType.biweekly:
        return const Duration(days: 14);
      case RecurrenceType.monthly:
        return const Duration(days: 30); // Approximate
      case RecurrenceType.quarterly:
        return const Duration(days: 90); // Approximate
      case RecurrenceType.yearly:
        return const Duration(days: 365); // Approximate
    }
  }

  DateTime getNextDate(DateTime currentDate) {
    switch (this) {
      case RecurrenceType.daily:
        return DateTime(currentDate.year, currentDate.month, currentDate.day + 1);
      case RecurrenceType.weekly:
        return DateTime(currentDate.year, currentDate.month, currentDate.day + 7);
      case RecurrenceType.biweekly:
        return DateTime(currentDate.year, currentDate.month, currentDate.day + 14);
      case RecurrenceType.monthly:
        return DateTime(currentDate.year, currentDate.month + 1, currentDate.day);
      case RecurrenceType.quarterly:
        return DateTime(currentDate.year, currentDate.month + 3, currentDate.day);
      case RecurrenceType.yearly:
        return DateTime(currentDate.year + 1, currentDate.month, currentDate.day);
    }
  }
}

class RecurringTransaction {
  final String id;
  final String accountId;
  final String title;
  final String? description;
  final double amount;
  final String type; // 'income', 'expense', 'sale', 'purchase'
  final String category;
  final RecurrenceType recurrenceType;
  final DateTime startDate;
  final DateTime? endDate;
  final DateTime nextDueDate;
  final bool isActive;
  final String? paymentMethod;
  final String? location;
  final List<String>? tags;
  final int? dayOfMonth; // For monthly recurrence
  final int? dayOfWeek; // For weekly recurrence (1=Monday, 7=Sunday)
  final String? notes;
  final DateTime createdAt;
  final DateTime? updatedAt;

  RecurringTransaction({
    required this.id,
    required this.accountId,
    required this.title,
    this.description,
    required this.amount,
    required this.type,
    required this.category,
    required this.recurrenceType,
    required this.startDate,
    this.endDate,
    required this.nextDueDate,
    this.isActive = true,
    this.paymentMethod,
    this.location,
    this.tags,
    this.dayOfMonth,
    this.dayOfWeek,
    this.notes,
    required this.createdAt,
    this.updatedAt,
  });

  RecurringTransaction copyWith({
    String? id,
    String? accountId,
    String? title,
    String? description,
    double? amount,
    String? type,
    String? category,
    RecurrenceType? recurrenceType,
    DateTime? startDate,
    DateTime? endDate,
    DateTime? nextDueDate,
    bool? isActive,
    String? paymentMethod,
    String? location,
    List<String>? tags,
    int? dayOfMonth,
    int? dayOfWeek,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return RecurringTransaction(
      id: id ?? this.id,
      accountId: accountId ?? this.accountId,
      title: title ?? this.title,
      description: description ?? this.description,
      amount: amount ?? this.amount,
      type: type ?? this.type,
      category: category ?? this.category,
      recurrenceType: recurrenceType ?? this.recurrenceType,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      nextDueDate: nextDueDate ?? this.nextDueDate,
      isActive: isActive ?? this.isActive,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      location: location ?? this.location,
      tags: tags ?? this.tags,
      dayOfMonth: dayOfMonth ?? this.dayOfMonth,
      dayOfWeek: dayOfWeek ?? this.dayOfWeek,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'accountId': accountId,
      'title': title,
      'description': description,
      'amount': amount,
      'type': type,
      'category': category,
      'recurrenceType': recurrenceType.name,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'nextDueDate': nextDueDate.toIso8601String(),
      'isActive': isActive,
      'paymentMethod': paymentMethod,
      'location': location,
      'tags': tags,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  factory RecurringTransaction.fromJson(Map<String, dynamic> json) {
    return RecurringTransaction(
      id: json['id'],
      accountId: json['accountId'],
      title: json['title'],
      description: json['description'],
      amount: json['amount'].toDouble(),
      type: json['type'],
      category: json['category'],
      recurrenceType: RecurrenceType.values.firstWhere((e) => e.name == json['recurrenceType']),
      startDate: DateTime.parse(json['startDate']),
      endDate: json['endDate'] != null ? DateTime.parse(json['endDate']) : null,
      nextDueDate: DateTime.parse(json['nextDueDate']),
      isActive: json['isActive'] ?? true,
      paymentMethod: json['paymentMethod'],
      location: json['location'],
      tags: json['tags'] != null ? List<String>.from(json['tags']) : null,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
    );
  }

  /// Check if this recurring transaction is due
  bool isDue() {
    return DateTime.now().isAfter(nextDueDate) || 
           DateTime.now().isAtSameMomentAs(nextDueDate);
  }

  /// Get the next due date after processing
  DateTime getNextDueDate() {
    return recurrenceType.getNextDate(nextDueDate);
  }

  /// Check if this recurring transaction should still be active
  bool shouldBeActive() {
    if (!isActive) return false;
    if (endDate == null) return true;
    return DateTime.now().isBefore(endDate!);
  }
}

/// Predefined recurring transaction templates
class RecurringTransactionTemplate {
  final String name;
  final String type;
  final String category;
  final RecurrenceType recurrenceType;
  final String description;

  const RecurringTransactionTemplate({
    required this.name,
    required this.type,
    required this.category,
    required this.recurrenceType,
    required this.description,
  });

  static const List<RecurringTransactionTemplate> personalTemplates = [
    // Income templates
    RecurringTransactionTemplate(
      name: 'Salary',
      type: 'income',
      category: 'salary',
      recurrenceType: RecurrenceType.monthly,
      description: 'Monthly salary payment',
    ),
    RecurringTransactionTemplate(
      name: 'Freelance Payment',
      type: 'income',
      category: 'freelance',
      recurrenceType: RecurrenceType.monthly,
      description: 'Regular freelance client payment',
    ),
    RecurringTransactionTemplate(
      name: 'Investment Dividends',
      type: 'income',
      category: 'investment',
      recurrenceType: RecurrenceType.quarterly,
      description: 'Quarterly dividend payments',
    ),
    
    // Expense templates
    RecurringTransactionTemplate(
      name: 'Rent',
      type: 'expense',
      category: 'housing',
      recurrenceType: RecurrenceType.monthly,
      description: 'Monthly rent payment',
    ),
    RecurringTransactionTemplate(
      name: 'Utilities',
      type: 'expense',
      category: 'utilities',
      recurrenceType: RecurrenceType.monthly,
      description: 'Monthly utility bills',
    ),
    RecurringTransactionTemplate(
      name: 'Insurance',
      type: 'expense',
      category: 'insurance',
      recurrenceType: RecurrenceType.monthly,
      description: 'Monthly insurance premium',
    ),
    RecurringTransactionTemplate(
      name: 'Subscription Services',
      type: 'expense',
      category: 'entertainment',
      recurrenceType: RecurrenceType.monthly,
      description: 'Monthly subscription services',
    ),
  ];

  static const List<RecurringTransactionTemplate> businessTemplates = [
    // Sales templates
    RecurringTransactionTemplate(
      name: 'Service Contract',
      type: 'sale',
      category: 'services',
      recurrenceType: RecurrenceType.monthly,
      description: 'Monthly service contract payment',
    ),
    RecurringTransactionTemplate(
      name: 'Subscription Revenue',
      type: 'sale',
      category: 'subscription',
      recurrenceType: RecurrenceType.monthly,
      description: 'Monthly subscription revenue',
    ),
    
    // Purchase templates
    RecurringTransactionTemplate(
      name: 'Office Rent',
      type: 'purchase',
      category: 'rent',
      recurrenceType: RecurrenceType.monthly,
      description: 'Monthly office rent',
    ),
    RecurringTransactionTemplate(
      name: 'Software Licenses',
      type: 'purchase',
      category: 'software',
      recurrenceType: RecurrenceType.monthly,
      description: 'Monthly software license fees',
    ),
    RecurringTransactionTemplate(
      name: 'Inventory Restocking',
      type: 'purchase',
      category: 'inventory',
      recurrenceType: RecurrenceType.weekly,
      description: 'Weekly inventory restocking',
    ),
  ];
}
