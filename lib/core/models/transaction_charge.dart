import 'package:equatable/equatable.dart';

enum ChargeType {
  transactionFee,
  bankFee,
  serviceFee,
  processingFee,
  withdrawalFee,
  transferFee,
  convenienceFee,
  other,
}

enum ChargeFrequency {
  perTransaction,
  monthly,
  quarterly,
  yearly,
  oneTime,
}

class TransactionCharge extends Equatable {
  final String id;
  final String accountId;
  final String? parentTransactionId; // Link to the main transaction
  final String name;
  final String? description;
  final double amount;
  final ChargeType type;
  final ChargeFrequency frequency;
  final String? provider; // Bank, service provider, etc.
  final String? paymentMethod;
  final DateTime chargeDate;
  final bool isRecurring;
  final String? recurringPattern;
  final DateTime? nextChargeDate;
  final bool isActive;
  final Map<String, dynamic>? metadata;
  final DateTime createdAt;
  final DateTime? updatedAt;

  const TransactionCharge({
    required this.id,
    required this.accountId,
    this.parentTransactionId,
    required this.name,
    this.description,
    required this.amount,
    required this.type,
    required this.frequency,
    this.provider,
    this.paymentMethod,
    required this.chargeDate,
    this.isRecurring = false,
    this.recurringPattern,
    this.nextChargeDate,
    this.isActive = true,
    this.metadata,
    required this.createdAt,
    this.updatedAt,
  });

  TransactionCharge copyWith({
    String? id,
    String? accountId,
    String? parentTransactionId,
    String? name,
    String? description,
    double? amount,
    ChargeType? type,
    ChargeFrequency? frequency,
    String? provider,
    String? paymentMethod,
    DateTime? chargeDate,
    bool? isRecurring,
    String? recurringPattern,
    DateTime? nextChargeDate,
    bool? isActive,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return TransactionCharge(
      id: id ?? this.id,
      accountId: accountId ?? this.accountId,
      parentTransactionId: parentTransactionId ?? this.parentTransactionId,
      name: name ?? this.name,
      description: description ?? this.description,
      amount: amount ?? this.amount,
      type: type ?? this.type,
      frequency: frequency ?? this.frequency,
      provider: provider ?? this.provider,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      chargeDate: chargeDate ?? this.chargeDate,
      isRecurring: isRecurring ?? this.isRecurring,
      recurringPattern: recurringPattern ?? this.recurringPattern,
      nextChargeDate: nextChargeDate ?? this.nextChargeDate,
      isActive: isActive ?? this.isActive,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'accountId': accountId,
      'parentTransactionId': parentTransactionId,
      'name': name,
      'description': description,
      'amount': amount,
      'type': type.name,
      'frequency': frequency.name,
      'provider': provider,
      'paymentMethod': paymentMethod,
      'chargeDate': chargeDate.toIso8601String(),
      'isRecurring': isRecurring,
      'recurringPattern': recurringPattern,
      'nextChargeDate': nextChargeDate?.toIso8601String(),
      'isActive': isActive,
      'metadata': metadata,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  factory TransactionCharge.fromJson(Map<String, dynamic> json) {
    return TransactionCharge(
      id: json['id'],
      accountId: json['accountId'],
      parentTransactionId: json['parentTransactionId'],
      name: json['name'],
      description: json['description'],
      amount: json['amount'].toDouble(),
      type: ChargeType.values.firstWhere((e) => e.name == json['type']),
      frequency: ChargeFrequency.values.firstWhere((e) => e.name == json['frequency']),
      provider: json['provider'],
      paymentMethod: json['paymentMethod'],
      chargeDate: DateTime.parse(json['chargeDate']),
      isRecurring: json['isRecurring'] ?? false,
      recurringPattern: json['recurringPattern'],
      nextChargeDate: json['nextChargeDate'] != null 
          ? DateTime.parse(json['nextChargeDate']) 
          : null,
      isActive: json['isActive'] ?? true,
      metadata: json['metadata'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt']) 
          : null,
    );
  }

  // Helper methods
  double get monthlyAmount {
    switch (frequency) {
      case ChargeFrequency.perTransaction:
        return 0.0; // Cannot calculate without transaction count
      case ChargeFrequency.monthly:
        return amount;
      case ChargeFrequency.quarterly:
        return amount / 3;
      case ChargeFrequency.yearly:
        return amount / 12;
      case ChargeFrequency.oneTime:
        return 0.0;
    }
  }

  double get yearlyAmount {
    switch (frequency) {
      case ChargeFrequency.perTransaction:
        return 0.0; // Cannot calculate without transaction count
      case ChargeFrequency.monthly:
        return amount * 12;
      case ChargeFrequency.quarterly:
        return amount * 4;
      case ChargeFrequency.yearly:
        return amount;
      case ChargeFrequency.oneTime:
        return amount;
    }
  }

  bool get isDue {
    if (!isRecurring || nextChargeDate == null) return false;
    return DateTime.now().isAfter(nextChargeDate!);
  }

  @override
  List<Object?> get props => [
        id,
        accountId,
        parentTransactionId,
        name,
        description,
        amount,
        type,
        frequency,
        provider,
        paymentMethod,
        chargeDate,
        isRecurring,
        recurringPattern,
        nextChargeDate,
        isActive,
        metadata,
        createdAt,
        updatedAt,
      ];
}

// Extension methods for better display
extension ChargeTypeExtension on ChargeType {
  String get displayName {
    switch (this) {
      case ChargeType.transactionFee:
        return 'Transaction Fee';
      case ChargeType.bankFee:
        return 'Bank Fee';
      case ChargeType.serviceFee:
        return 'Service Fee';
      case ChargeType.processingFee:
        return 'Processing Fee';
      case ChargeType.withdrawalFee:
        return 'Withdrawal Fee';
      case ChargeType.transferFee:
        return 'Transfer Fee';
      case ChargeType.convenienceFee:
        return 'Convenience Fee';
      case ChargeType.other:
        return 'Other Fee';
    }
  }
}

extension ChargeFrequencyExtension on ChargeFrequency {
  String get displayName {
    switch (this) {
      case ChargeFrequency.perTransaction:
        return 'Per Transaction';
      case ChargeFrequency.monthly:
        return 'Monthly';
      case ChargeFrequency.quarterly:
        return 'Quarterly';
      case ChargeFrequency.yearly:
        return 'Yearly';
      case ChargeFrequency.oneTime:
        return 'One Time';
    }
  }
}

// Common charge templates
class ChargeTemplate {
  final String name;
  final ChargeType type;
  final ChargeFrequency frequency;
  final String? provider;
  final String description;

  const ChargeTemplate({
    required this.name,
    required this.type,
    required this.frequency,
    this.provider,
    required this.description,
  });

  static const List<ChargeTemplate> commonCharges = [
    // M-Pesa charges
    ChargeTemplate(
      name: 'M-Pesa Transaction Fee',
      type: ChargeType.transactionFee,
      frequency: ChargeFrequency.perTransaction,
      provider: 'Safaricom',
      description: 'Fee charged per M-Pesa transaction',
    ),
    ChargeTemplate(
      name: 'M-Pesa Withdrawal Fee',
      type: ChargeType.withdrawalFee,
      frequency: ChargeFrequency.perTransaction,
      provider: 'Safaricom',
      description: 'Fee for withdrawing cash from M-Pesa',
    ),
    
    // Bank charges
    ChargeTemplate(
      name: 'Monthly Account Maintenance',
      type: ChargeType.bankFee,
      frequency: ChargeFrequency.monthly,
      description: 'Monthly bank account maintenance fee',
    ),
    ChargeTemplate(
      name: 'ATM Withdrawal Fee',
      type: ChargeType.withdrawalFee,
      frequency: ChargeFrequency.perTransaction,
      description: 'Fee for ATM withdrawals',
    ),
    ChargeTemplate(
      name: 'Inter-bank Transfer Fee',
      type: ChargeType.transferFee,
      frequency: ChargeFrequency.perTransaction,
      description: 'Fee for transferring money between banks',
    ),
    
    // Service charges
    ChargeTemplate(
      name: 'SMS Banking Fee',
      type: ChargeType.serviceFee,
      frequency: ChargeFrequency.monthly,
      description: 'Monthly SMS banking service fee',
    ),
    ChargeTemplate(
      name: 'Online Banking Fee',
      type: ChargeType.serviceFee,
      frequency: ChargeFrequency.monthly,
      description: 'Monthly online banking service fee',
    ),
  ];
}
