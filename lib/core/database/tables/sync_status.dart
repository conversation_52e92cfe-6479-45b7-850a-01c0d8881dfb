import 'package:drift/drift.dart';

@DataClassName('SyncStatu')
class SyncStatus extends Table {
  TextColumn get accountId => text()(); // Foreign key to UserAccounts
  DateTimeColumn get lastSyncAt => dateTime().nullable()();
  BoolColumn get hasUnsyncedChanges => boolean().withDefault(const Constant(false))();
  TextColumn get syncError => text().nullable()();
  DateTimeColumn get updatedAt => dateTime()();

  @override
  Set<Column> get primaryKey => {accountId};
}
