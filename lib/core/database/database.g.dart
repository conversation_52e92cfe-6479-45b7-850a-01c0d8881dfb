// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'database.dart';

// ignore_for_file: type=lint
class $UserAccountsTable extends UserAccounts
    with TableInfo<$UserAccountsTable, UserAccount> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $UserAccountsTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _nameMeta = const VerificationMeta('name');
  @override
  late final GeneratedColumn<String> name = GeneratedColumn<String>(
      'name', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _emailMeta = const VerificationMeta('email');
  @override
  late final GeneratedColumn<String> email = GeneratedColumn<String>(
      'email', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _accountTypeMeta =
      const VerificationMeta('accountType');
  @override
  late final GeneratedColumn<String> accountType = GeneratedColumn<String>(
      'account_type', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _isActiveMeta =
      const VerificationMeta('isActive');
  @override
  late final GeneratedColumn<bool> isActive = GeneratedColumn<bool>(
      'is_active', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("is_active" IN (0, 1))'),
      defaultValue: const Constant(false));
  static const VerificationMeta _profileImageUrlMeta =
      const VerificationMeta('profileImageUrl');
  @override
  late final GeneratedColumn<String> profileImageUrl = GeneratedColumn<String>(
      'profile_image_url', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _currencyMeta =
      const VerificationMeta('currency');
  @override
  late final GeneratedColumn<String> currency = GeneratedColumn<String>(
      'currency', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      defaultValue: const Constant('USD'));
  static const VerificationMeta _timezoneMeta =
      const VerificationMeta('timezone');
  @override
  late final GeneratedColumn<String> timezone = GeneratedColumn<String>(
      'timezone', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _isPremiumMeta =
      const VerificationMeta('isPremium');
  @override
  late final GeneratedColumn<bool> isPremium = GeneratedColumn<bool>(
      'is_premium', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("is_premium" IN (0, 1))'),
      defaultValue: const Constant(false));
  static const VerificationMeta _premiumExpiresAtMeta =
      const VerificationMeta('premiumExpiresAt');
  @override
  late final GeneratedColumn<DateTime> premiumExpiresAt =
      GeneratedColumn<DateTime>('premium_expires_at', aliasedName, true,
          type: DriftSqlType.dateTime, requiredDuringInsert: false);
  static const VerificationMeta _createdAtMeta =
      const VerificationMeta('createdAt');
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
      'created_at', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  static const VerificationMeta _updatedAtMeta =
      const VerificationMeta('updatedAt');
  @override
  late final GeneratedColumn<DateTime> updatedAt = GeneratedColumn<DateTime>(
      'updated_at', aliasedName, true,
      type: DriftSqlType.dateTime, requiredDuringInsert: false);
  static const VerificationMeta _lastLoginAtMeta =
      const VerificationMeta('lastLoginAt');
  @override
  late final GeneratedColumn<DateTime> lastLoginAt = GeneratedColumn<DateTime>(
      'last_login_at', aliasedName, true,
      type: DriftSqlType.dateTime, requiredDuringInsert: false);
  @override
  List<GeneratedColumn> get $columns => [
        id,
        name,
        email,
        accountType,
        isActive,
        profileImageUrl,
        currency,
        timezone,
        isPremium,
        premiumExpiresAt,
        createdAt,
        updatedAt,
        lastLoginAt
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'user_accounts';
  @override
  VerificationContext validateIntegrity(Insertable<UserAccount> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    if (data.containsKey('name')) {
      context.handle(
          _nameMeta, name.isAcceptableOrUnknown(data['name']!, _nameMeta));
    } else if (isInserting) {
      context.missing(_nameMeta);
    }
    if (data.containsKey('email')) {
      context.handle(
          _emailMeta, email.isAcceptableOrUnknown(data['email']!, _emailMeta));
    } else if (isInserting) {
      context.missing(_emailMeta);
    }
    if (data.containsKey('account_type')) {
      context.handle(
          _accountTypeMeta,
          accountType.isAcceptableOrUnknown(
              data['account_type']!, _accountTypeMeta));
    } else if (isInserting) {
      context.missing(_accountTypeMeta);
    }
    if (data.containsKey('is_active')) {
      context.handle(_isActiveMeta,
          isActive.isAcceptableOrUnknown(data['is_active']!, _isActiveMeta));
    }
    if (data.containsKey('profile_image_url')) {
      context.handle(
          _profileImageUrlMeta,
          profileImageUrl.isAcceptableOrUnknown(
              data['profile_image_url']!, _profileImageUrlMeta));
    }
    if (data.containsKey('currency')) {
      context.handle(_currencyMeta,
          currency.isAcceptableOrUnknown(data['currency']!, _currencyMeta));
    }
    if (data.containsKey('timezone')) {
      context.handle(_timezoneMeta,
          timezone.isAcceptableOrUnknown(data['timezone']!, _timezoneMeta));
    }
    if (data.containsKey('is_premium')) {
      context.handle(_isPremiumMeta,
          isPremium.isAcceptableOrUnknown(data['is_premium']!, _isPremiumMeta));
    }
    if (data.containsKey('premium_expires_at')) {
      context.handle(
          _premiumExpiresAtMeta,
          premiumExpiresAt.isAcceptableOrUnknown(
              data['premium_expires_at']!, _premiumExpiresAtMeta));
    }
    if (data.containsKey('created_at')) {
      context.handle(_createdAtMeta,
          createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta));
    } else if (isInserting) {
      context.missing(_createdAtMeta);
    }
    if (data.containsKey('updated_at')) {
      context.handle(_updatedAtMeta,
          updatedAt.isAcceptableOrUnknown(data['updated_at']!, _updatedAtMeta));
    }
    if (data.containsKey('last_login_at')) {
      context.handle(
          _lastLoginAtMeta,
          lastLoginAt.isAcceptableOrUnknown(
              data['last_login_at']!, _lastLoginAtMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  UserAccount map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return UserAccount(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      name: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}name'])!,
      email: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}email'])!,
      accountType: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}account_type'])!,
      isActive: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_active'])!,
      profileImageUrl: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}profile_image_url']),
      currency: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}currency'])!,
      timezone: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}timezone']),
      isPremium: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_premium'])!,
      premiumExpiresAt: attachedDatabase.typeMapping.read(
          DriftSqlType.dateTime, data['${effectivePrefix}premium_expires_at']),
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}created_at'])!,
      updatedAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}updated_at']),
      lastLoginAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}last_login_at']),
    );
  }

  @override
  $UserAccountsTable createAlias(String alias) {
    return $UserAccountsTable(attachedDatabase, alias);
  }
}

class UserAccount extends DataClass implements Insertable<UserAccount> {
  final String id;
  final String name;
  final String email;
  final String accountType;
  final bool isActive;
  final String? profileImageUrl;
  final String currency;
  final String? timezone;
  final bool isPremium;
  final DateTime? premiumExpiresAt;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final DateTime? lastLoginAt;
  const UserAccount(
      {required this.id,
      required this.name,
      required this.email,
      required this.accountType,
      required this.isActive,
      this.profileImageUrl,
      required this.currency,
      this.timezone,
      required this.isPremium,
      this.premiumExpiresAt,
      required this.createdAt,
      this.updatedAt,
      this.lastLoginAt});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    map['name'] = Variable<String>(name);
    map['email'] = Variable<String>(email);
    map['account_type'] = Variable<String>(accountType);
    map['is_active'] = Variable<bool>(isActive);
    if (!nullToAbsent || profileImageUrl != null) {
      map['profile_image_url'] = Variable<String>(profileImageUrl);
    }
    map['currency'] = Variable<String>(currency);
    if (!nullToAbsent || timezone != null) {
      map['timezone'] = Variable<String>(timezone);
    }
    map['is_premium'] = Variable<bool>(isPremium);
    if (!nullToAbsent || premiumExpiresAt != null) {
      map['premium_expires_at'] = Variable<DateTime>(premiumExpiresAt);
    }
    map['created_at'] = Variable<DateTime>(createdAt);
    if (!nullToAbsent || updatedAt != null) {
      map['updated_at'] = Variable<DateTime>(updatedAt);
    }
    if (!nullToAbsent || lastLoginAt != null) {
      map['last_login_at'] = Variable<DateTime>(lastLoginAt);
    }
    return map;
  }

  UserAccountsCompanion toCompanion(bool nullToAbsent) {
    return UserAccountsCompanion(
      id: Value(id),
      name: Value(name),
      email: Value(email),
      accountType: Value(accountType),
      isActive: Value(isActive),
      profileImageUrl: profileImageUrl == null && nullToAbsent
          ? const Value.absent()
          : Value(profileImageUrl),
      currency: Value(currency),
      timezone: timezone == null && nullToAbsent
          ? const Value.absent()
          : Value(timezone),
      isPremium: Value(isPremium),
      premiumExpiresAt: premiumExpiresAt == null && nullToAbsent
          ? const Value.absent()
          : Value(premiumExpiresAt),
      createdAt: Value(createdAt),
      updatedAt: updatedAt == null && nullToAbsent
          ? const Value.absent()
          : Value(updatedAt),
      lastLoginAt: lastLoginAt == null && nullToAbsent
          ? const Value.absent()
          : Value(lastLoginAt),
    );
  }

  factory UserAccount.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return UserAccount(
      id: serializer.fromJson<String>(json['id']),
      name: serializer.fromJson<String>(json['name']),
      email: serializer.fromJson<String>(json['email']),
      accountType: serializer.fromJson<String>(json['accountType']),
      isActive: serializer.fromJson<bool>(json['isActive']),
      profileImageUrl: serializer.fromJson<String?>(json['profileImageUrl']),
      currency: serializer.fromJson<String>(json['currency']),
      timezone: serializer.fromJson<String?>(json['timezone']),
      isPremium: serializer.fromJson<bool>(json['isPremium']),
      premiumExpiresAt:
          serializer.fromJson<DateTime?>(json['premiumExpiresAt']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
      updatedAt: serializer.fromJson<DateTime?>(json['updatedAt']),
      lastLoginAt: serializer.fromJson<DateTime?>(json['lastLoginAt']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'name': serializer.toJson<String>(name),
      'email': serializer.toJson<String>(email),
      'accountType': serializer.toJson<String>(accountType),
      'isActive': serializer.toJson<bool>(isActive),
      'profileImageUrl': serializer.toJson<String?>(profileImageUrl),
      'currency': serializer.toJson<String>(currency),
      'timezone': serializer.toJson<String?>(timezone),
      'isPremium': serializer.toJson<bool>(isPremium),
      'premiumExpiresAt': serializer.toJson<DateTime?>(premiumExpiresAt),
      'createdAt': serializer.toJson<DateTime>(createdAt),
      'updatedAt': serializer.toJson<DateTime?>(updatedAt),
      'lastLoginAt': serializer.toJson<DateTime?>(lastLoginAt),
    };
  }

  UserAccount copyWith(
          {String? id,
          String? name,
          String? email,
          String? accountType,
          bool? isActive,
          Value<String?> profileImageUrl = const Value.absent(),
          String? currency,
          Value<String?> timezone = const Value.absent(),
          bool? isPremium,
          Value<DateTime?> premiumExpiresAt = const Value.absent(),
          DateTime? createdAt,
          Value<DateTime?> updatedAt = const Value.absent(),
          Value<DateTime?> lastLoginAt = const Value.absent()}) =>
      UserAccount(
        id: id ?? this.id,
        name: name ?? this.name,
        email: email ?? this.email,
        accountType: accountType ?? this.accountType,
        isActive: isActive ?? this.isActive,
        profileImageUrl: profileImageUrl.present
            ? profileImageUrl.value
            : this.profileImageUrl,
        currency: currency ?? this.currency,
        timezone: timezone.present ? timezone.value : this.timezone,
        isPremium: isPremium ?? this.isPremium,
        premiumExpiresAt: premiumExpiresAt.present
            ? premiumExpiresAt.value
            : this.premiumExpiresAt,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt.present ? updatedAt.value : this.updatedAt,
        lastLoginAt: lastLoginAt.present ? lastLoginAt.value : this.lastLoginAt,
      );
  UserAccount copyWithCompanion(UserAccountsCompanion data) {
    return UserAccount(
      id: data.id.present ? data.id.value : this.id,
      name: data.name.present ? data.name.value : this.name,
      email: data.email.present ? data.email.value : this.email,
      accountType:
          data.accountType.present ? data.accountType.value : this.accountType,
      isActive: data.isActive.present ? data.isActive.value : this.isActive,
      profileImageUrl: data.profileImageUrl.present
          ? data.profileImageUrl.value
          : this.profileImageUrl,
      currency: data.currency.present ? data.currency.value : this.currency,
      timezone: data.timezone.present ? data.timezone.value : this.timezone,
      isPremium: data.isPremium.present ? data.isPremium.value : this.isPremium,
      premiumExpiresAt: data.premiumExpiresAt.present
          ? data.premiumExpiresAt.value
          : this.premiumExpiresAt,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
      updatedAt: data.updatedAt.present ? data.updatedAt.value : this.updatedAt,
      lastLoginAt:
          data.lastLoginAt.present ? data.lastLoginAt.value : this.lastLoginAt,
    );
  }

  @override
  String toString() {
    return (StringBuffer('UserAccount(')
          ..write('id: $id, ')
          ..write('name: $name, ')
          ..write('email: $email, ')
          ..write('accountType: $accountType, ')
          ..write('isActive: $isActive, ')
          ..write('profileImageUrl: $profileImageUrl, ')
          ..write('currency: $currency, ')
          ..write('timezone: $timezone, ')
          ..write('isPremium: $isPremium, ')
          ..write('premiumExpiresAt: $premiumExpiresAt, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('lastLoginAt: $lastLoginAt')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
      id,
      name,
      email,
      accountType,
      isActive,
      profileImageUrl,
      currency,
      timezone,
      isPremium,
      premiumExpiresAt,
      createdAt,
      updatedAt,
      lastLoginAt);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is UserAccount &&
          other.id == this.id &&
          other.name == this.name &&
          other.email == this.email &&
          other.accountType == this.accountType &&
          other.isActive == this.isActive &&
          other.profileImageUrl == this.profileImageUrl &&
          other.currency == this.currency &&
          other.timezone == this.timezone &&
          other.isPremium == this.isPremium &&
          other.premiumExpiresAt == this.premiumExpiresAt &&
          other.createdAt == this.createdAt &&
          other.updatedAt == this.updatedAt &&
          other.lastLoginAt == this.lastLoginAt);
}

class UserAccountsCompanion extends UpdateCompanion<UserAccount> {
  final Value<String> id;
  final Value<String> name;
  final Value<String> email;
  final Value<String> accountType;
  final Value<bool> isActive;
  final Value<String?> profileImageUrl;
  final Value<String> currency;
  final Value<String?> timezone;
  final Value<bool> isPremium;
  final Value<DateTime?> premiumExpiresAt;
  final Value<DateTime> createdAt;
  final Value<DateTime?> updatedAt;
  final Value<DateTime?> lastLoginAt;
  final Value<int> rowid;
  const UserAccountsCompanion({
    this.id = const Value.absent(),
    this.name = const Value.absent(),
    this.email = const Value.absent(),
    this.accountType = const Value.absent(),
    this.isActive = const Value.absent(),
    this.profileImageUrl = const Value.absent(),
    this.currency = const Value.absent(),
    this.timezone = const Value.absent(),
    this.isPremium = const Value.absent(),
    this.premiumExpiresAt = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.lastLoginAt = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  UserAccountsCompanion.insert({
    required String id,
    required String name,
    required String email,
    required String accountType,
    this.isActive = const Value.absent(),
    this.profileImageUrl = const Value.absent(),
    this.currency = const Value.absent(),
    this.timezone = const Value.absent(),
    this.isPremium = const Value.absent(),
    this.premiumExpiresAt = const Value.absent(),
    required DateTime createdAt,
    this.updatedAt = const Value.absent(),
    this.lastLoginAt = const Value.absent(),
    this.rowid = const Value.absent(),
  })  : id = Value(id),
        name = Value(name),
        email = Value(email),
        accountType = Value(accountType),
        createdAt = Value(createdAt);
  static Insertable<UserAccount> custom({
    Expression<String>? id,
    Expression<String>? name,
    Expression<String>? email,
    Expression<String>? accountType,
    Expression<bool>? isActive,
    Expression<String>? profileImageUrl,
    Expression<String>? currency,
    Expression<String>? timezone,
    Expression<bool>? isPremium,
    Expression<DateTime>? premiumExpiresAt,
    Expression<DateTime>? createdAt,
    Expression<DateTime>? updatedAt,
    Expression<DateTime>? lastLoginAt,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (name != null) 'name': name,
      if (email != null) 'email': email,
      if (accountType != null) 'account_type': accountType,
      if (isActive != null) 'is_active': isActive,
      if (profileImageUrl != null) 'profile_image_url': profileImageUrl,
      if (currency != null) 'currency': currency,
      if (timezone != null) 'timezone': timezone,
      if (isPremium != null) 'is_premium': isPremium,
      if (premiumExpiresAt != null) 'premium_expires_at': premiumExpiresAt,
      if (createdAt != null) 'created_at': createdAt,
      if (updatedAt != null) 'updated_at': updatedAt,
      if (lastLoginAt != null) 'last_login_at': lastLoginAt,
      if (rowid != null) 'rowid': rowid,
    });
  }

  UserAccountsCompanion copyWith(
      {Value<String>? id,
      Value<String>? name,
      Value<String>? email,
      Value<String>? accountType,
      Value<bool>? isActive,
      Value<String?>? profileImageUrl,
      Value<String>? currency,
      Value<String?>? timezone,
      Value<bool>? isPremium,
      Value<DateTime?>? premiumExpiresAt,
      Value<DateTime>? createdAt,
      Value<DateTime?>? updatedAt,
      Value<DateTime?>? lastLoginAt,
      Value<int>? rowid}) {
    return UserAccountsCompanion(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      accountType: accountType ?? this.accountType,
      isActive: isActive ?? this.isActive,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      currency: currency ?? this.currency,
      timezone: timezone ?? this.timezone,
      isPremium: isPremium ?? this.isPremium,
      premiumExpiresAt: premiumExpiresAt ?? this.premiumExpiresAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (name.present) {
      map['name'] = Variable<String>(name.value);
    }
    if (email.present) {
      map['email'] = Variable<String>(email.value);
    }
    if (accountType.present) {
      map['account_type'] = Variable<String>(accountType.value);
    }
    if (isActive.present) {
      map['is_active'] = Variable<bool>(isActive.value);
    }
    if (profileImageUrl.present) {
      map['profile_image_url'] = Variable<String>(profileImageUrl.value);
    }
    if (currency.present) {
      map['currency'] = Variable<String>(currency.value);
    }
    if (timezone.present) {
      map['timezone'] = Variable<String>(timezone.value);
    }
    if (isPremium.present) {
      map['is_premium'] = Variable<bool>(isPremium.value);
    }
    if (premiumExpiresAt.present) {
      map['premium_expires_at'] = Variable<DateTime>(premiumExpiresAt.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    if (updatedAt.present) {
      map['updated_at'] = Variable<DateTime>(updatedAt.value);
    }
    if (lastLoginAt.present) {
      map['last_login_at'] = Variable<DateTime>(lastLoginAt.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('UserAccountsCompanion(')
          ..write('id: $id, ')
          ..write('name: $name, ')
          ..write('email: $email, ')
          ..write('accountType: $accountType, ')
          ..write('isActive: $isActive, ')
          ..write('profileImageUrl: $profileImageUrl, ')
          ..write('currency: $currency, ')
          ..write('timezone: $timezone, ')
          ..write('isPremium: $isPremium, ')
          ..write('premiumExpiresAt: $premiumExpiresAt, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('lastLoginAt: $lastLoginAt, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $PersonalTransactionsTable extends PersonalTransactions
    with TableInfo<$PersonalTransactionsTable, PersonalTransaction> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $PersonalTransactionsTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _accountIdMeta =
      const VerificationMeta('accountId');
  @override
  late final GeneratedColumn<String> accountId = GeneratedColumn<String>(
      'account_id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _titleMeta = const VerificationMeta('title');
  @override
  late final GeneratedColumn<String> title = GeneratedColumn<String>(
      'title', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _descriptionMeta =
      const VerificationMeta('description');
  @override
  late final GeneratedColumn<String> description = GeneratedColumn<String>(
      'description', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _amountMeta = const VerificationMeta('amount');
  @override
  late final GeneratedColumn<double> amount = GeneratedColumn<double>(
      'amount', aliasedName, false,
      type: DriftSqlType.double, requiredDuringInsert: true);
  static const VerificationMeta _typeMeta = const VerificationMeta('type');
  @override
  late final GeneratedColumn<String> type = GeneratedColumn<String>(
      'type', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _categoryMeta =
      const VerificationMeta('category');
  @override
  late final GeneratedColumn<String> category = GeneratedColumn<String>(
      'category', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _dateMeta = const VerificationMeta('date');
  @override
  late final GeneratedColumn<DateTime> date = GeneratedColumn<DateTime>(
      'date', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  static const VerificationMeta _paymentMethodMeta =
      const VerificationMeta('paymentMethod');
  @override
  late final GeneratedColumn<String> paymentMethod = GeneratedColumn<String>(
      'payment_method', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _transactionCostMeta =
      const VerificationMeta('transactionCost');
  @override
  late final GeneratedColumn<double> transactionCost = GeneratedColumn<double>(
      'transaction_cost', aliasedName, false,
      type: DriftSqlType.double,
      requiredDuringInsert: false,
      defaultValue: const Constant(0.0));
  static const VerificationMeta _locationMeta =
      const VerificationMeta('location');
  @override
  late final GeneratedColumn<String> location = GeneratedColumn<String>(
      'location', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _tagsMeta = const VerificationMeta('tags');
  @override
  late final GeneratedColumn<String> tags = GeneratedColumn<String>(
      'tags', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _receiptImageUrlMeta =
      const VerificationMeta('receiptImageUrl');
  @override
  late final GeneratedColumn<String> receiptImageUrl = GeneratedColumn<String>(
      'receipt_image_url', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _isRecurringMeta =
      const VerificationMeta('isRecurring');
  @override
  late final GeneratedColumn<bool> isRecurring = GeneratedColumn<bool>(
      'is_recurring', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'CHECK ("is_recurring" IN (0, 1))'),
      defaultValue: const Constant(false));
  static const VerificationMeta _recurringPatternMeta =
      const VerificationMeta('recurringPattern');
  @override
  late final GeneratedColumn<String> recurringPattern = GeneratedColumn<String>(
      'recurring_pattern', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _nextRecurringDateMeta =
      const VerificationMeta('nextRecurringDate');
  @override
  late final GeneratedColumn<DateTime> nextRecurringDate =
      GeneratedColumn<DateTime>('next_recurring_date', aliasedName, true,
          type: DriftSqlType.dateTime, requiredDuringInsert: false);
  static const VerificationMeta _isSyncedMeta =
      const VerificationMeta('isSynced');
  @override
  late final GeneratedColumn<bool> isSynced = GeneratedColumn<bool>(
      'is_synced', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("is_synced" IN (0, 1))'),
      defaultValue: const Constant(false));
  static const VerificationMeta _createdAtMeta =
      const VerificationMeta('createdAt');
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
      'created_at', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  static const VerificationMeta _updatedAtMeta =
      const VerificationMeta('updatedAt');
  @override
  late final GeneratedColumn<DateTime> updatedAt = GeneratedColumn<DateTime>(
      'updated_at', aliasedName, true,
      type: DriftSqlType.dateTime, requiredDuringInsert: false);
  @override
  List<GeneratedColumn> get $columns => [
        id,
        accountId,
        title,
        description,
        amount,
        type,
        category,
        date,
        paymentMethod,
        transactionCost,
        location,
        tags,
        receiptImageUrl,
        isRecurring,
        recurringPattern,
        nextRecurringDate,
        isSynced,
        createdAt,
        updatedAt
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'personal_transactions';
  @override
  VerificationContext validateIntegrity(
      Insertable<PersonalTransaction> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    if (data.containsKey('account_id')) {
      context.handle(_accountIdMeta,
          accountId.isAcceptableOrUnknown(data['account_id']!, _accountIdMeta));
    } else if (isInserting) {
      context.missing(_accountIdMeta);
    }
    if (data.containsKey('title')) {
      context.handle(
          _titleMeta, title.isAcceptableOrUnknown(data['title']!, _titleMeta));
    } else if (isInserting) {
      context.missing(_titleMeta);
    }
    if (data.containsKey('description')) {
      context.handle(
          _descriptionMeta,
          description.isAcceptableOrUnknown(
              data['description']!, _descriptionMeta));
    }
    if (data.containsKey('amount')) {
      context.handle(_amountMeta,
          amount.isAcceptableOrUnknown(data['amount']!, _amountMeta));
    } else if (isInserting) {
      context.missing(_amountMeta);
    }
    if (data.containsKey('type')) {
      context.handle(
          _typeMeta, type.isAcceptableOrUnknown(data['type']!, _typeMeta));
    } else if (isInserting) {
      context.missing(_typeMeta);
    }
    if (data.containsKey('category')) {
      context.handle(_categoryMeta,
          category.isAcceptableOrUnknown(data['category']!, _categoryMeta));
    } else if (isInserting) {
      context.missing(_categoryMeta);
    }
    if (data.containsKey('date')) {
      context.handle(
          _dateMeta, date.isAcceptableOrUnknown(data['date']!, _dateMeta));
    } else if (isInserting) {
      context.missing(_dateMeta);
    }
    if (data.containsKey('payment_method')) {
      context.handle(
          _paymentMethodMeta,
          paymentMethod.isAcceptableOrUnknown(
              data['payment_method']!, _paymentMethodMeta));
    }
    if (data.containsKey('transaction_cost')) {
      context.handle(
          _transactionCostMeta,
          transactionCost.isAcceptableOrUnknown(
              data['transaction_cost']!, _transactionCostMeta));
    }
    if (data.containsKey('location')) {
      context.handle(_locationMeta,
          location.isAcceptableOrUnknown(data['location']!, _locationMeta));
    }
    if (data.containsKey('tags')) {
      context.handle(
          _tagsMeta, tags.isAcceptableOrUnknown(data['tags']!, _tagsMeta));
    }
    if (data.containsKey('receipt_image_url')) {
      context.handle(
          _receiptImageUrlMeta,
          receiptImageUrl.isAcceptableOrUnknown(
              data['receipt_image_url']!, _receiptImageUrlMeta));
    }
    if (data.containsKey('is_recurring')) {
      context.handle(
          _isRecurringMeta,
          isRecurring.isAcceptableOrUnknown(
              data['is_recurring']!, _isRecurringMeta));
    }
    if (data.containsKey('recurring_pattern')) {
      context.handle(
          _recurringPatternMeta,
          recurringPattern.isAcceptableOrUnknown(
              data['recurring_pattern']!, _recurringPatternMeta));
    }
    if (data.containsKey('next_recurring_date')) {
      context.handle(
          _nextRecurringDateMeta,
          nextRecurringDate.isAcceptableOrUnknown(
              data['next_recurring_date']!, _nextRecurringDateMeta));
    }
    if (data.containsKey('is_synced')) {
      context.handle(_isSyncedMeta,
          isSynced.isAcceptableOrUnknown(data['is_synced']!, _isSyncedMeta));
    }
    if (data.containsKey('created_at')) {
      context.handle(_createdAtMeta,
          createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta));
    } else if (isInserting) {
      context.missing(_createdAtMeta);
    }
    if (data.containsKey('updated_at')) {
      context.handle(_updatedAtMeta,
          updatedAt.isAcceptableOrUnknown(data['updated_at']!, _updatedAtMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  PersonalTransaction map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return PersonalTransaction(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      accountId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}account_id'])!,
      title: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}title'])!,
      description: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}description']),
      amount: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}amount'])!,
      type: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}type'])!,
      category: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}category'])!,
      date: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}date'])!,
      paymentMethod: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}payment_method']),
      transactionCost: attachedDatabase.typeMapping.read(
          DriftSqlType.double, data['${effectivePrefix}transaction_cost'])!,
      location: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}location']),
      tags: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}tags']),
      receiptImageUrl: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}receipt_image_url']),
      isRecurring: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_recurring'])!,
      recurringPattern: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}recurring_pattern']),
      nextRecurringDate: attachedDatabase.typeMapping.read(
          DriftSqlType.dateTime, data['${effectivePrefix}next_recurring_date']),
      isSynced: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_synced'])!,
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}created_at'])!,
      updatedAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}updated_at']),
    );
  }

  @override
  $PersonalTransactionsTable createAlias(String alias) {
    return $PersonalTransactionsTable(attachedDatabase, alias);
  }
}

class PersonalTransaction extends DataClass
    implements Insertable<PersonalTransaction> {
  final String id;
  final String accountId;
  final String title;
  final String? description;
  final double amount;
  final String type;
  final String category;
  final DateTime date;
  final String? paymentMethod;
  final double transactionCost;
  final String? location;
  final String? tags;
  final String? receiptImageUrl;
  final bool isRecurring;
  final String? recurringPattern;
  final DateTime? nextRecurringDate;
  final bool isSynced;
  final DateTime createdAt;
  final DateTime? updatedAt;
  const PersonalTransaction(
      {required this.id,
      required this.accountId,
      required this.title,
      this.description,
      required this.amount,
      required this.type,
      required this.category,
      required this.date,
      this.paymentMethod,
      required this.transactionCost,
      this.location,
      this.tags,
      this.receiptImageUrl,
      required this.isRecurring,
      this.recurringPattern,
      this.nextRecurringDate,
      required this.isSynced,
      required this.createdAt,
      this.updatedAt});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    map['account_id'] = Variable<String>(accountId);
    map['title'] = Variable<String>(title);
    if (!nullToAbsent || description != null) {
      map['description'] = Variable<String>(description);
    }
    map['amount'] = Variable<double>(amount);
    map['type'] = Variable<String>(type);
    map['category'] = Variable<String>(category);
    map['date'] = Variable<DateTime>(date);
    if (!nullToAbsent || paymentMethod != null) {
      map['payment_method'] = Variable<String>(paymentMethod);
    }
    map['transaction_cost'] = Variable<double>(transactionCost);
    if (!nullToAbsent || location != null) {
      map['location'] = Variable<String>(location);
    }
    if (!nullToAbsent || tags != null) {
      map['tags'] = Variable<String>(tags);
    }
    if (!nullToAbsent || receiptImageUrl != null) {
      map['receipt_image_url'] = Variable<String>(receiptImageUrl);
    }
    map['is_recurring'] = Variable<bool>(isRecurring);
    if (!nullToAbsent || recurringPattern != null) {
      map['recurring_pattern'] = Variable<String>(recurringPattern);
    }
    if (!nullToAbsent || nextRecurringDate != null) {
      map['next_recurring_date'] = Variable<DateTime>(nextRecurringDate);
    }
    map['is_synced'] = Variable<bool>(isSynced);
    map['created_at'] = Variable<DateTime>(createdAt);
    if (!nullToAbsent || updatedAt != null) {
      map['updated_at'] = Variable<DateTime>(updatedAt);
    }
    return map;
  }

  PersonalTransactionsCompanion toCompanion(bool nullToAbsent) {
    return PersonalTransactionsCompanion(
      id: Value(id),
      accountId: Value(accountId),
      title: Value(title),
      description: description == null && nullToAbsent
          ? const Value.absent()
          : Value(description),
      amount: Value(amount),
      type: Value(type),
      category: Value(category),
      date: Value(date),
      paymentMethod: paymentMethod == null && nullToAbsent
          ? const Value.absent()
          : Value(paymentMethod),
      transactionCost: Value(transactionCost),
      location: location == null && nullToAbsent
          ? const Value.absent()
          : Value(location),
      tags: tags == null && nullToAbsent ? const Value.absent() : Value(tags),
      receiptImageUrl: receiptImageUrl == null && nullToAbsent
          ? const Value.absent()
          : Value(receiptImageUrl),
      isRecurring: Value(isRecurring),
      recurringPattern: recurringPattern == null && nullToAbsent
          ? const Value.absent()
          : Value(recurringPattern),
      nextRecurringDate: nextRecurringDate == null && nullToAbsent
          ? const Value.absent()
          : Value(nextRecurringDate),
      isSynced: Value(isSynced),
      createdAt: Value(createdAt),
      updatedAt: updatedAt == null && nullToAbsent
          ? const Value.absent()
          : Value(updatedAt),
    );
  }

  factory PersonalTransaction.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return PersonalTransaction(
      id: serializer.fromJson<String>(json['id']),
      accountId: serializer.fromJson<String>(json['accountId']),
      title: serializer.fromJson<String>(json['title']),
      description: serializer.fromJson<String?>(json['description']),
      amount: serializer.fromJson<double>(json['amount']),
      type: serializer.fromJson<String>(json['type']),
      category: serializer.fromJson<String>(json['category']),
      date: serializer.fromJson<DateTime>(json['date']),
      paymentMethod: serializer.fromJson<String?>(json['paymentMethod']),
      transactionCost: serializer.fromJson<double>(json['transactionCost']),
      location: serializer.fromJson<String?>(json['location']),
      tags: serializer.fromJson<String?>(json['tags']),
      receiptImageUrl: serializer.fromJson<String?>(json['receiptImageUrl']),
      isRecurring: serializer.fromJson<bool>(json['isRecurring']),
      recurringPattern: serializer.fromJson<String?>(json['recurringPattern']),
      nextRecurringDate:
          serializer.fromJson<DateTime?>(json['nextRecurringDate']),
      isSynced: serializer.fromJson<bool>(json['isSynced']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
      updatedAt: serializer.fromJson<DateTime?>(json['updatedAt']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'accountId': serializer.toJson<String>(accountId),
      'title': serializer.toJson<String>(title),
      'description': serializer.toJson<String?>(description),
      'amount': serializer.toJson<double>(amount),
      'type': serializer.toJson<String>(type),
      'category': serializer.toJson<String>(category),
      'date': serializer.toJson<DateTime>(date),
      'paymentMethod': serializer.toJson<String?>(paymentMethod),
      'transactionCost': serializer.toJson<double>(transactionCost),
      'location': serializer.toJson<String?>(location),
      'tags': serializer.toJson<String?>(tags),
      'receiptImageUrl': serializer.toJson<String?>(receiptImageUrl),
      'isRecurring': serializer.toJson<bool>(isRecurring),
      'recurringPattern': serializer.toJson<String?>(recurringPattern),
      'nextRecurringDate': serializer.toJson<DateTime?>(nextRecurringDate),
      'isSynced': serializer.toJson<bool>(isSynced),
      'createdAt': serializer.toJson<DateTime>(createdAt),
      'updatedAt': serializer.toJson<DateTime?>(updatedAt),
    };
  }

  PersonalTransaction copyWith(
          {String? id,
          String? accountId,
          String? title,
          Value<String?> description = const Value.absent(),
          double? amount,
          String? type,
          String? category,
          DateTime? date,
          Value<String?> paymentMethod = const Value.absent(),
          double? transactionCost,
          Value<String?> location = const Value.absent(),
          Value<String?> tags = const Value.absent(),
          Value<String?> receiptImageUrl = const Value.absent(),
          bool? isRecurring,
          Value<String?> recurringPattern = const Value.absent(),
          Value<DateTime?> nextRecurringDate = const Value.absent(),
          bool? isSynced,
          DateTime? createdAt,
          Value<DateTime?> updatedAt = const Value.absent()}) =>
      PersonalTransaction(
        id: id ?? this.id,
        accountId: accountId ?? this.accountId,
        title: title ?? this.title,
        description: description.present ? description.value : this.description,
        amount: amount ?? this.amount,
        type: type ?? this.type,
        category: category ?? this.category,
        date: date ?? this.date,
        paymentMethod:
            paymentMethod.present ? paymentMethod.value : this.paymentMethod,
        transactionCost: transactionCost ?? this.transactionCost,
        location: location.present ? location.value : this.location,
        tags: tags.present ? tags.value : this.tags,
        receiptImageUrl: receiptImageUrl.present
            ? receiptImageUrl.value
            : this.receiptImageUrl,
        isRecurring: isRecurring ?? this.isRecurring,
        recurringPattern: recurringPattern.present
            ? recurringPattern.value
            : this.recurringPattern,
        nextRecurringDate: nextRecurringDate.present
            ? nextRecurringDate.value
            : this.nextRecurringDate,
        isSynced: isSynced ?? this.isSynced,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt.present ? updatedAt.value : this.updatedAt,
      );
  PersonalTransaction copyWithCompanion(PersonalTransactionsCompanion data) {
    return PersonalTransaction(
      id: data.id.present ? data.id.value : this.id,
      accountId: data.accountId.present ? data.accountId.value : this.accountId,
      title: data.title.present ? data.title.value : this.title,
      description:
          data.description.present ? data.description.value : this.description,
      amount: data.amount.present ? data.amount.value : this.amount,
      type: data.type.present ? data.type.value : this.type,
      category: data.category.present ? data.category.value : this.category,
      date: data.date.present ? data.date.value : this.date,
      paymentMethod: data.paymentMethod.present
          ? data.paymentMethod.value
          : this.paymentMethod,
      transactionCost: data.transactionCost.present
          ? data.transactionCost.value
          : this.transactionCost,
      location: data.location.present ? data.location.value : this.location,
      tags: data.tags.present ? data.tags.value : this.tags,
      receiptImageUrl: data.receiptImageUrl.present
          ? data.receiptImageUrl.value
          : this.receiptImageUrl,
      isRecurring:
          data.isRecurring.present ? data.isRecurring.value : this.isRecurring,
      recurringPattern: data.recurringPattern.present
          ? data.recurringPattern.value
          : this.recurringPattern,
      nextRecurringDate: data.nextRecurringDate.present
          ? data.nextRecurringDate.value
          : this.nextRecurringDate,
      isSynced: data.isSynced.present ? data.isSynced.value : this.isSynced,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
      updatedAt: data.updatedAt.present ? data.updatedAt.value : this.updatedAt,
    );
  }

  @override
  String toString() {
    return (StringBuffer('PersonalTransaction(')
          ..write('id: $id, ')
          ..write('accountId: $accountId, ')
          ..write('title: $title, ')
          ..write('description: $description, ')
          ..write('amount: $amount, ')
          ..write('type: $type, ')
          ..write('category: $category, ')
          ..write('date: $date, ')
          ..write('paymentMethod: $paymentMethod, ')
          ..write('transactionCost: $transactionCost, ')
          ..write('location: $location, ')
          ..write('tags: $tags, ')
          ..write('receiptImageUrl: $receiptImageUrl, ')
          ..write('isRecurring: $isRecurring, ')
          ..write('recurringPattern: $recurringPattern, ')
          ..write('nextRecurringDate: $nextRecurringDate, ')
          ..write('isSynced: $isSynced, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
      id,
      accountId,
      title,
      description,
      amount,
      type,
      category,
      date,
      paymentMethod,
      transactionCost,
      location,
      tags,
      receiptImageUrl,
      isRecurring,
      recurringPattern,
      nextRecurringDate,
      isSynced,
      createdAt,
      updatedAt);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is PersonalTransaction &&
          other.id == this.id &&
          other.accountId == this.accountId &&
          other.title == this.title &&
          other.description == this.description &&
          other.amount == this.amount &&
          other.type == this.type &&
          other.category == this.category &&
          other.date == this.date &&
          other.paymentMethod == this.paymentMethod &&
          other.transactionCost == this.transactionCost &&
          other.location == this.location &&
          other.tags == this.tags &&
          other.receiptImageUrl == this.receiptImageUrl &&
          other.isRecurring == this.isRecurring &&
          other.recurringPattern == this.recurringPattern &&
          other.nextRecurringDate == this.nextRecurringDate &&
          other.isSynced == this.isSynced &&
          other.createdAt == this.createdAt &&
          other.updatedAt == this.updatedAt);
}

class PersonalTransactionsCompanion
    extends UpdateCompanion<PersonalTransaction> {
  final Value<String> id;
  final Value<String> accountId;
  final Value<String> title;
  final Value<String?> description;
  final Value<double> amount;
  final Value<String> type;
  final Value<String> category;
  final Value<DateTime> date;
  final Value<String?> paymentMethod;
  final Value<double> transactionCost;
  final Value<String?> location;
  final Value<String?> tags;
  final Value<String?> receiptImageUrl;
  final Value<bool> isRecurring;
  final Value<String?> recurringPattern;
  final Value<DateTime?> nextRecurringDate;
  final Value<bool> isSynced;
  final Value<DateTime> createdAt;
  final Value<DateTime?> updatedAt;
  final Value<int> rowid;
  const PersonalTransactionsCompanion({
    this.id = const Value.absent(),
    this.accountId = const Value.absent(),
    this.title = const Value.absent(),
    this.description = const Value.absent(),
    this.amount = const Value.absent(),
    this.type = const Value.absent(),
    this.category = const Value.absent(),
    this.date = const Value.absent(),
    this.paymentMethod = const Value.absent(),
    this.transactionCost = const Value.absent(),
    this.location = const Value.absent(),
    this.tags = const Value.absent(),
    this.receiptImageUrl = const Value.absent(),
    this.isRecurring = const Value.absent(),
    this.recurringPattern = const Value.absent(),
    this.nextRecurringDate = const Value.absent(),
    this.isSynced = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  PersonalTransactionsCompanion.insert({
    required String id,
    required String accountId,
    required String title,
    this.description = const Value.absent(),
    required double amount,
    required String type,
    required String category,
    required DateTime date,
    this.paymentMethod = const Value.absent(),
    this.transactionCost = const Value.absent(),
    this.location = const Value.absent(),
    this.tags = const Value.absent(),
    this.receiptImageUrl = const Value.absent(),
    this.isRecurring = const Value.absent(),
    this.recurringPattern = const Value.absent(),
    this.nextRecurringDate = const Value.absent(),
    this.isSynced = const Value.absent(),
    required DateTime createdAt,
    this.updatedAt = const Value.absent(),
    this.rowid = const Value.absent(),
  })  : id = Value(id),
        accountId = Value(accountId),
        title = Value(title),
        amount = Value(amount),
        type = Value(type),
        category = Value(category),
        date = Value(date),
        createdAt = Value(createdAt);
  static Insertable<PersonalTransaction> custom({
    Expression<String>? id,
    Expression<String>? accountId,
    Expression<String>? title,
    Expression<String>? description,
    Expression<double>? amount,
    Expression<String>? type,
    Expression<String>? category,
    Expression<DateTime>? date,
    Expression<String>? paymentMethod,
    Expression<double>? transactionCost,
    Expression<String>? location,
    Expression<String>? tags,
    Expression<String>? receiptImageUrl,
    Expression<bool>? isRecurring,
    Expression<String>? recurringPattern,
    Expression<DateTime>? nextRecurringDate,
    Expression<bool>? isSynced,
    Expression<DateTime>? createdAt,
    Expression<DateTime>? updatedAt,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (accountId != null) 'account_id': accountId,
      if (title != null) 'title': title,
      if (description != null) 'description': description,
      if (amount != null) 'amount': amount,
      if (type != null) 'type': type,
      if (category != null) 'category': category,
      if (date != null) 'date': date,
      if (paymentMethod != null) 'payment_method': paymentMethod,
      if (transactionCost != null) 'transaction_cost': transactionCost,
      if (location != null) 'location': location,
      if (tags != null) 'tags': tags,
      if (receiptImageUrl != null) 'receipt_image_url': receiptImageUrl,
      if (isRecurring != null) 'is_recurring': isRecurring,
      if (recurringPattern != null) 'recurring_pattern': recurringPattern,
      if (nextRecurringDate != null) 'next_recurring_date': nextRecurringDate,
      if (isSynced != null) 'is_synced': isSynced,
      if (createdAt != null) 'created_at': createdAt,
      if (updatedAt != null) 'updated_at': updatedAt,
      if (rowid != null) 'rowid': rowid,
    });
  }

  PersonalTransactionsCompanion copyWith(
      {Value<String>? id,
      Value<String>? accountId,
      Value<String>? title,
      Value<String?>? description,
      Value<double>? amount,
      Value<String>? type,
      Value<String>? category,
      Value<DateTime>? date,
      Value<String?>? paymentMethod,
      Value<double>? transactionCost,
      Value<String?>? location,
      Value<String?>? tags,
      Value<String?>? receiptImageUrl,
      Value<bool>? isRecurring,
      Value<String?>? recurringPattern,
      Value<DateTime?>? nextRecurringDate,
      Value<bool>? isSynced,
      Value<DateTime>? createdAt,
      Value<DateTime?>? updatedAt,
      Value<int>? rowid}) {
    return PersonalTransactionsCompanion(
      id: id ?? this.id,
      accountId: accountId ?? this.accountId,
      title: title ?? this.title,
      description: description ?? this.description,
      amount: amount ?? this.amount,
      type: type ?? this.type,
      category: category ?? this.category,
      date: date ?? this.date,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      transactionCost: transactionCost ?? this.transactionCost,
      location: location ?? this.location,
      tags: tags ?? this.tags,
      receiptImageUrl: receiptImageUrl ?? this.receiptImageUrl,
      isRecurring: isRecurring ?? this.isRecurring,
      recurringPattern: recurringPattern ?? this.recurringPattern,
      nextRecurringDate: nextRecurringDate ?? this.nextRecurringDate,
      isSynced: isSynced ?? this.isSynced,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (accountId.present) {
      map['account_id'] = Variable<String>(accountId.value);
    }
    if (title.present) {
      map['title'] = Variable<String>(title.value);
    }
    if (description.present) {
      map['description'] = Variable<String>(description.value);
    }
    if (amount.present) {
      map['amount'] = Variable<double>(amount.value);
    }
    if (type.present) {
      map['type'] = Variable<String>(type.value);
    }
    if (category.present) {
      map['category'] = Variable<String>(category.value);
    }
    if (date.present) {
      map['date'] = Variable<DateTime>(date.value);
    }
    if (paymentMethod.present) {
      map['payment_method'] = Variable<String>(paymentMethod.value);
    }
    if (transactionCost.present) {
      map['transaction_cost'] = Variable<double>(transactionCost.value);
    }
    if (location.present) {
      map['location'] = Variable<String>(location.value);
    }
    if (tags.present) {
      map['tags'] = Variable<String>(tags.value);
    }
    if (receiptImageUrl.present) {
      map['receipt_image_url'] = Variable<String>(receiptImageUrl.value);
    }
    if (isRecurring.present) {
      map['is_recurring'] = Variable<bool>(isRecurring.value);
    }
    if (recurringPattern.present) {
      map['recurring_pattern'] = Variable<String>(recurringPattern.value);
    }
    if (nextRecurringDate.present) {
      map['next_recurring_date'] = Variable<DateTime>(nextRecurringDate.value);
    }
    if (isSynced.present) {
      map['is_synced'] = Variable<bool>(isSynced.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    if (updatedAt.present) {
      map['updated_at'] = Variable<DateTime>(updatedAt.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('PersonalTransactionsCompanion(')
          ..write('id: $id, ')
          ..write('accountId: $accountId, ')
          ..write('title: $title, ')
          ..write('description: $description, ')
          ..write('amount: $amount, ')
          ..write('type: $type, ')
          ..write('category: $category, ')
          ..write('date: $date, ')
          ..write('paymentMethod: $paymentMethod, ')
          ..write('transactionCost: $transactionCost, ')
          ..write('location: $location, ')
          ..write('tags: $tags, ')
          ..write('receiptImageUrl: $receiptImageUrl, ')
          ..write('isRecurring: $isRecurring, ')
          ..write('recurringPattern: $recurringPattern, ')
          ..write('nextRecurringDate: $nextRecurringDate, ')
          ..write('isSynced: $isSynced, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $AccountSettingsTable extends AccountSettings
    with TableInfo<$AccountSettingsTable, AccountSetting> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $AccountSettingsTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _accountIdMeta =
      const VerificationMeta('accountId');
  @override
  late final GeneratedColumn<String> accountId = GeneratedColumn<String>(
      'account_id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _keyMeta = const VerificationMeta('key');
  @override
  late final GeneratedColumn<String> key = GeneratedColumn<String>(
      'key', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _valueMeta = const VerificationMeta('value');
  @override
  late final GeneratedColumn<String> value = GeneratedColumn<String>(
      'value', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _updatedAtMeta =
      const VerificationMeta('updatedAt');
  @override
  late final GeneratedColumn<DateTime> updatedAt = GeneratedColumn<DateTime>(
      'updated_at', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  @override
  List<GeneratedColumn> get $columns => [accountId, key, value, updatedAt];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'account_settings';
  @override
  VerificationContext validateIntegrity(Insertable<AccountSetting> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('account_id')) {
      context.handle(_accountIdMeta,
          accountId.isAcceptableOrUnknown(data['account_id']!, _accountIdMeta));
    } else if (isInserting) {
      context.missing(_accountIdMeta);
    }
    if (data.containsKey('key')) {
      context.handle(
          _keyMeta, key.isAcceptableOrUnknown(data['key']!, _keyMeta));
    } else if (isInserting) {
      context.missing(_keyMeta);
    }
    if (data.containsKey('value')) {
      context.handle(
          _valueMeta, value.isAcceptableOrUnknown(data['value']!, _valueMeta));
    } else if (isInserting) {
      context.missing(_valueMeta);
    }
    if (data.containsKey('updated_at')) {
      context.handle(_updatedAtMeta,
          updatedAt.isAcceptableOrUnknown(data['updated_at']!, _updatedAtMeta));
    } else if (isInserting) {
      context.missing(_updatedAtMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {accountId, key};
  @override
  AccountSetting map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return AccountSetting(
      accountId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}account_id'])!,
      key: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}key'])!,
      value: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}value'])!,
      updatedAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}updated_at'])!,
    );
  }

  @override
  $AccountSettingsTable createAlias(String alias) {
    return $AccountSettingsTable(attachedDatabase, alias);
  }
}

class AccountSetting extends DataClass implements Insertable<AccountSetting> {
  final String accountId;
  final String key;
  final String value;
  final DateTime updatedAt;
  const AccountSetting(
      {required this.accountId,
      required this.key,
      required this.value,
      required this.updatedAt});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['account_id'] = Variable<String>(accountId);
    map['key'] = Variable<String>(key);
    map['value'] = Variable<String>(value);
    map['updated_at'] = Variable<DateTime>(updatedAt);
    return map;
  }

  AccountSettingsCompanion toCompanion(bool nullToAbsent) {
    return AccountSettingsCompanion(
      accountId: Value(accountId),
      key: Value(key),
      value: Value(value),
      updatedAt: Value(updatedAt),
    );
  }

  factory AccountSetting.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return AccountSetting(
      accountId: serializer.fromJson<String>(json['accountId']),
      key: serializer.fromJson<String>(json['key']),
      value: serializer.fromJson<String>(json['value']),
      updatedAt: serializer.fromJson<DateTime>(json['updatedAt']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'accountId': serializer.toJson<String>(accountId),
      'key': serializer.toJson<String>(key),
      'value': serializer.toJson<String>(value),
      'updatedAt': serializer.toJson<DateTime>(updatedAt),
    };
  }

  AccountSetting copyWith(
          {String? accountId,
          String? key,
          String? value,
          DateTime? updatedAt}) =>
      AccountSetting(
        accountId: accountId ?? this.accountId,
        key: key ?? this.key,
        value: value ?? this.value,
        updatedAt: updatedAt ?? this.updatedAt,
      );
  AccountSetting copyWithCompanion(AccountSettingsCompanion data) {
    return AccountSetting(
      accountId: data.accountId.present ? data.accountId.value : this.accountId,
      key: data.key.present ? data.key.value : this.key,
      value: data.value.present ? data.value.value : this.value,
      updatedAt: data.updatedAt.present ? data.updatedAt.value : this.updatedAt,
    );
  }

  @override
  String toString() {
    return (StringBuffer('AccountSetting(')
          ..write('accountId: $accountId, ')
          ..write('key: $key, ')
          ..write('value: $value, ')
          ..write('updatedAt: $updatedAt')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(accountId, key, value, updatedAt);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is AccountSetting &&
          other.accountId == this.accountId &&
          other.key == this.key &&
          other.value == this.value &&
          other.updatedAt == this.updatedAt);
}

class AccountSettingsCompanion extends UpdateCompanion<AccountSetting> {
  final Value<String> accountId;
  final Value<String> key;
  final Value<String> value;
  final Value<DateTime> updatedAt;
  final Value<int> rowid;
  const AccountSettingsCompanion({
    this.accountId = const Value.absent(),
    this.key = const Value.absent(),
    this.value = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  AccountSettingsCompanion.insert({
    required String accountId,
    required String key,
    required String value,
    required DateTime updatedAt,
    this.rowid = const Value.absent(),
  })  : accountId = Value(accountId),
        key = Value(key),
        value = Value(value),
        updatedAt = Value(updatedAt);
  static Insertable<AccountSetting> custom({
    Expression<String>? accountId,
    Expression<String>? key,
    Expression<String>? value,
    Expression<DateTime>? updatedAt,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (accountId != null) 'account_id': accountId,
      if (key != null) 'key': key,
      if (value != null) 'value': value,
      if (updatedAt != null) 'updated_at': updatedAt,
      if (rowid != null) 'rowid': rowid,
    });
  }

  AccountSettingsCompanion copyWith(
      {Value<String>? accountId,
      Value<String>? key,
      Value<String>? value,
      Value<DateTime>? updatedAt,
      Value<int>? rowid}) {
    return AccountSettingsCompanion(
      accountId: accountId ?? this.accountId,
      key: key ?? this.key,
      value: value ?? this.value,
      updatedAt: updatedAt ?? this.updatedAt,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (accountId.present) {
      map['account_id'] = Variable<String>(accountId.value);
    }
    if (key.present) {
      map['key'] = Variable<String>(key.value);
    }
    if (value.present) {
      map['value'] = Variable<String>(value.value);
    }
    if (updatedAt.present) {
      map['updated_at'] = Variable<DateTime>(updatedAt.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('AccountSettingsCompanion(')
          ..write('accountId: $accountId, ')
          ..write('key: $key, ')
          ..write('value: $value, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $SyncStatusTable extends SyncStatus
    with TableInfo<$SyncStatusTable, SyncStatu> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $SyncStatusTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _accountIdMeta =
      const VerificationMeta('accountId');
  @override
  late final GeneratedColumn<String> accountId = GeneratedColumn<String>(
      'account_id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _lastSyncAtMeta =
      const VerificationMeta('lastSyncAt');
  @override
  late final GeneratedColumn<DateTime> lastSyncAt = GeneratedColumn<DateTime>(
      'last_sync_at', aliasedName, true,
      type: DriftSqlType.dateTime, requiredDuringInsert: false);
  static const VerificationMeta _hasUnsyncedChangesMeta =
      const VerificationMeta('hasUnsyncedChanges');
  @override
  late final GeneratedColumn<bool> hasUnsyncedChanges = GeneratedColumn<bool>(
      'has_unsynced_changes', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'CHECK ("has_unsynced_changes" IN (0, 1))'),
      defaultValue: const Constant(false));
  static const VerificationMeta _syncErrorMeta =
      const VerificationMeta('syncError');
  @override
  late final GeneratedColumn<String> syncError = GeneratedColumn<String>(
      'sync_error', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _updatedAtMeta =
      const VerificationMeta('updatedAt');
  @override
  late final GeneratedColumn<DateTime> updatedAt = GeneratedColumn<DateTime>(
      'updated_at', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  @override
  List<GeneratedColumn> get $columns =>
      [accountId, lastSyncAt, hasUnsyncedChanges, syncError, updatedAt];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'sync_status';
  @override
  VerificationContext validateIntegrity(Insertable<SyncStatu> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('account_id')) {
      context.handle(_accountIdMeta,
          accountId.isAcceptableOrUnknown(data['account_id']!, _accountIdMeta));
    } else if (isInserting) {
      context.missing(_accountIdMeta);
    }
    if (data.containsKey('last_sync_at')) {
      context.handle(
          _lastSyncAtMeta,
          lastSyncAt.isAcceptableOrUnknown(
              data['last_sync_at']!, _lastSyncAtMeta));
    }
    if (data.containsKey('has_unsynced_changes')) {
      context.handle(
          _hasUnsyncedChangesMeta,
          hasUnsyncedChanges.isAcceptableOrUnknown(
              data['has_unsynced_changes']!, _hasUnsyncedChangesMeta));
    }
    if (data.containsKey('sync_error')) {
      context.handle(_syncErrorMeta,
          syncError.isAcceptableOrUnknown(data['sync_error']!, _syncErrorMeta));
    }
    if (data.containsKey('updated_at')) {
      context.handle(_updatedAtMeta,
          updatedAt.isAcceptableOrUnknown(data['updated_at']!, _updatedAtMeta));
    } else if (isInserting) {
      context.missing(_updatedAtMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {accountId};
  @override
  SyncStatu map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return SyncStatu(
      accountId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}account_id'])!,
      lastSyncAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}last_sync_at']),
      hasUnsyncedChanges: attachedDatabase.typeMapping.read(
          DriftSqlType.bool, data['${effectivePrefix}has_unsynced_changes'])!,
      syncError: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}sync_error']),
      updatedAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}updated_at'])!,
    );
  }

  @override
  $SyncStatusTable createAlias(String alias) {
    return $SyncStatusTable(attachedDatabase, alias);
  }
}

class SyncStatu extends DataClass implements Insertable<SyncStatu> {
  final String accountId;
  final DateTime? lastSyncAt;
  final bool hasUnsyncedChanges;
  final String? syncError;
  final DateTime updatedAt;
  const SyncStatu(
      {required this.accountId,
      this.lastSyncAt,
      required this.hasUnsyncedChanges,
      this.syncError,
      required this.updatedAt});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['account_id'] = Variable<String>(accountId);
    if (!nullToAbsent || lastSyncAt != null) {
      map['last_sync_at'] = Variable<DateTime>(lastSyncAt);
    }
    map['has_unsynced_changes'] = Variable<bool>(hasUnsyncedChanges);
    if (!nullToAbsent || syncError != null) {
      map['sync_error'] = Variable<String>(syncError);
    }
    map['updated_at'] = Variable<DateTime>(updatedAt);
    return map;
  }

  SyncStatusCompanion toCompanion(bool nullToAbsent) {
    return SyncStatusCompanion(
      accountId: Value(accountId),
      lastSyncAt: lastSyncAt == null && nullToAbsent
          ? const Value.absent()
          : Value(lastSyncAt),
      hasUnsyncedChanges: Value(hasUnsyncedChanges),
      syncError: syncError == null && nullToAbsent
          ? const Value.absent()
          : Value(syncError),
      updatedAt: Value(updatedAt),
    );
  }

  factory SyncStatu.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return SyncStatu(
      accountId: serializer.fromJson<String>(json['accountId']),
      lastSyncAt: serializer.fromJson<DateTime?>(json['lastSyncAt']),
      hasUnsyncedChanges: serializer.fromJson<bool>(json['hasUnsyncedChanges']),
      syncError: serializer.fromJson<String?>(json['syncError']),
      updatedAt: serializer.fromJson<DateTime>(json['updatedAt']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'accountId': serializer.toJson<String>(accountId),
      'lastSyncAt': serializer.toJson<DateTime?>(lastSyncAt),
      'hasUnsyncedChanges': serializer.toJson<bool>(hasUnsyncedChanges),
      'syncError': serializer.toJson<String?>(syncError),
      'updatedAt': serializer.toJson<DateTime>(updatedAt),
    };
  }

  SyncStatu copyWith(
          {String? accountId,
          Value<DateTime?> lastSyncAt = const Value.absent(),
          bool? hasUnsyncedChanges,
          Value<String?> syncError = const Value.absent(),
          DateTime? updatedAt}) =>
      SyncStatu(
        accountId: accountId ?? this.accountId,
        lastSyncAt: lastSyncAt.present ? lastSyncAt.value : this.lastSyncAt,
        hasUnsyncedChanges: hasUnsyncedChanges ?? this.hasUnsyncedChanges,
        syncError: syncError.present ? syncError.value : this.syncError,
        updatedAt: updatedAt ?? this.updatedAt,
      );
  SyncStatu copyWithCompanion(SyncStatusCompanion data) {
    return SyncStatu(
      accountId: data.accountId.present ? data.accountId.value : this.accountId,
      lastSyncAt:
          data.lastSyncAt.present ? data.lastSyncAt.value : this.lastSyncAt,
      hasUnsyncedChanges: data.hasUnsyncedChanges.present
          ? data.hasUnsyncedChanges.value
          : this.hasUnsyncedChanges,
      syncError: data.syncError.present ? data.syncError.value : this.syncError,
      updatedAt: data.updatedAt.present ? data.updatedAt.value : this.updatedAt,
    );
  }

  @override
  String toString() {
    return (StringBuffer('SyncStatu(')
          ..write('accountId: $accountId, ')
          ..write('lastSyncAt: $lastSyncAt, ')
          ..write('hasUnsyncedChanges: $hasUnsyncedChanges, ')
          ..write('syncError: $syncError, ')
          ..write('updatedAt: $updatedAt')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
      accountId, lastSyncAt, hasUnsyncedChanges, syncError, updatedAt);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is SyncStatu &&
          other.accountId == this.accountId &&
          other.lastSyncAt == this.lastSyncAt &&
          other.hasUnsyncedChanges == this.hasUnsyncedChanges &&
          other.syncError == this.syncError &&
          other.updatedAt == this.updatedAt);
}

class SyncStatusCompanion extends UpdateCompanion<SyncStatu> {
  final Value<String> accountId;
  final Value<DateTime?> lastSyncAt;
  final Value<bool> hasUnsyncedChanges;
  final Value<String?> syncError;
  final Value<DateTime> updatedAt;
  final Value<int> rowid;
  const SyncStatusCompanion({
    this.accountId = const Value.absent(),
    this.lastSyncAt = const Value.absent(),
    this.hasUnsyncedChanges = const Value.absent(),
    this.syncError = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  SyncStatusCompanion.insert({
    required String accountId,
    this.lastSyncAt = const Value.absent(),
    this.hasUnsyncedChanges = const Value.absent(),
    this.syncError = const Value.absent(),
    required DateTime updatedAt,
    this.rowid = const Value.absent(),
  })  : accountId = Value(accountId),
        updatedAt = Value(updatedAt);
  static Insertable<SyncStatu> custom({
    Expression<String>? accountId,
    Expression<DateTime>? lastSyncAt,
    Expression<bool>? hasUnsyncedChanges,
    Expression<String>? syncError,
    Expression<DateTime>? updatedAt,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (accountId != null) 'account_id': accountId,
      if (lastSyncAt != null) 'last_sync_at': lastSyncAt,
      if (hasUnsyncedChanges != null)
        'has_unsynced_changes': hasUnsyncedChanges,
      if (syncError != null) 'sync_error': syncError,
      if (updatedAt != null) 'updated_at': updatedAt,
      if (rowid != null) 'rowid': rowid,
    });
  }

  SyncStatusCompanion copyWith(
      {Value<String>? accountId,
      Value<DateTime?>? lastSyncAt,
      Value<bool>? hasUnsyncedChanges,
      Value<String?>? syncError,
      Value<DateTime>? updatedAt,
      Value<int>? rowid}) {
    return SyncStatusCompanion(
      accountId: accountId ?? this.accountId,
      lastSyncAt: lastSyncAt ?? this.lastSyncAt,
      hasUnsyncedChanges: hasUnsyncedChanges ?? this.hasUnsyncedChanges,
      syncError: syncError ?? this.syncError,
      updatedAt: updatedAt ?? this.updatedAt,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (accountId.present) {
      map['account_id'] = Variable<String>(accountId.value);
    }
    if (lastSyncAt.present) {
      map['last_sync_at'] = Variable<DateTime>(lastSyncAt.value);
    }
    if (hasUnsyncedChanges.present) {
      map['has_unsynced_changes'] = Variable<bool>(hasUnsyncedChanges.value);
    }
    if (syncError.present) {
      map['sync_error'] = Variable<String>(syncError.value);
    }
    if (updatedAt.present) {
      map['updated_at'] = Variable<DateTime>(updatedAt.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('SyncStatusCompanion(')
          ..write('accountId: $accountId, ')
          ..write('lastSyncAt: $lastSyncAt, ')
          ..write('hasUnsyncedChanges: $hasUnsyncedChanges, ')
          ..write('syncError: $syncError, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

abstract class _$AppDatabase extends GeneratedDatabase {
  _$AppDatabase(QueryExecutor e) : super(e);
  $AppDatabaseManager get managers => $AppDatabaseManager(this);
  late final $UserAccountsTable userAccounts = $UserAccountsTable(this);
  late final $PersonalTransactionsTable personalTransactions =
      $PersonalTransactionsTable(this);
  late final $AccountSettingsTable accountSettings =
      $AccountSettingsTable(this);
  late final $SyncStatusTable syncStatus = $SyncStatusTable(this);
  @override
  Iterable<TableInfo<Table, Object?>> get allTables =>
      allSchemaEntities.whereType<TableInfo<Table, Object?>>();
  @override
  List<DatabaseSchemaEntity> get allSchemaEntities =>
      [userAccounts, personalTransactions, accountSettings, syncStatus];
}

typedef $$UserAccountsTableCreateCompanionBuilder = UserAccountsCompanion
    Function({
  required String id,
  required String name,
  required String email,
  required String accountType,
  Value<bool> isActive,
  Value<String?> profileImageUrl,
  Value<String> currency,
  Value<String?> timezone,
  Value<bool> isPremium,
  Value<DateTime?> premiumExpiresAt,
  required DateTime createdAt,
  Value<DateTime?> updatedAt,
  Value<DateTime?> lastLoginAt,
  Value<int> rowid,
});
typedef $$UserAccountsTableUpdateCompanionBuilder = UserAccountsCompanion
    Function({
  Value<String> id,
  Value<String> name,
  Value<String> email,
  Value<String> accountType,
  Value<bool> isActive,
  Value<String?> profileImageUrl,
  Value<String> currency,
  Value<String?> timezone,
  Value<bool> isPremium,
  Value<DateTime?> premiumExpiresAt,
  Value<DateTime> createdAt,
  Value<DateTime?> updatedAt,
  Value<DateTime?> lastLoginAt,
  Value<int> rowid,
});

class $$UserAccountsTableTableManager extends RootTableManager<
    _$AppDatabase,
    $UserAccountsTable,
    UserAccount,
    $$UserAccountsTableFilterComposer,
    $$UserAccountsTableOrderingComposer,
    $$UserAccountsTableCreateCompanionBuilder,
    $$UserAccountsTableUpdateCompanionBuilder> {
  $$UserAccountsTableTableManager(_$AppDatabase db, $UserAccountsTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          filteringComposer:
              $$UserAccountsTableFilterComposer(ComposerState(db, table)),
          orderingComposer:
              $$UserAccountsTableOrderingComposer(ComposerState(db, table)),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<String> name = const Value.absent(),
            Value<String> email = const Value.absent(),
            Value<String> accountType = const Value.absent(),
            Value<bool> isActive = const Value.absent(),
            Value<String?> profileImageUrl = const Value.absent(),
            Value<String> currency = const Value.absent(),
            Value<String?> timezone = const Value.absent(),
            Value<bool> isPremium = const Value.absent(),
            Value<DateTime?> premiumExpiresAt = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<DateTime?> updatedAt = const Value.absent(),
            Value<DateTime?> lastLoginAt = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              UserAccountsCompanion(
            id: id,
            name: name,
            email: email,
            accountType: accountType,
            isActive: isActive,
            profileImageUrl: profileImageUrl,
            currency: currency,
            timezone: timezone,
            isPremium: isPremium,
            premiumExpiresAt: premiumExpiresAt,
            createdAt: createdAt,
            updatedAt: updatedAt,
            lastLoginAt: lastLoginAt,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            required String name,
            required String email,
            required String accountType,
            Value<bool> isActive = const Value.absent(),
            Value<String?> profileImageUrl = const Value.absent(),
            Value<String> currency = const Value.absent(),
            Value<String?> timezone = const Value.absent(),
            Value<bool> isPremium = const Value.absent(),
            Value<DateTime?> premiumExpiresAt = const Value.absent(),
            required DateTime createdAt,
            Value<DateTime?> updatedAt = const Value.absent(),
            Value<DateTime?> lastLoginAt = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              UserAccountsCompanion.insert(
            id: id,
            name: name,
            email: email,
            accountType: accountType,
            isActive: isActive,
            profileImageUrl: profileImageUrl,
            currency: currency,
            timezone: timezone,
            isPremium: isPremium,
            premiumExpiresAt: premiumExpiresAt,
            createdAt: createdAt,
            updatedAt: updatedAt,
            lastLoginAt: lastLoginAt,
            rowid: rowid,
          ),
        ));
}

class $$UserAccountsTableFilterComposer
    extends FilterComposer<_$AppDatabase, $UserAccountsTable> {
  $$UserAccountsTableFilterComposer(super.$state);
  ColumnFilters<String> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get name => $state.composableBuilder(
      column: $state.table.name,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get email => $state.composableBuilder(
      column: $state.table.email,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get accountType => $state.composableBuilder(
      column: $state.table.accountType,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<bool> get isActive => $state.composableBuilder(
      column: $state.table.isActive,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get profileImageUrl => $state.composableBuilder(
      column: $state.table.profileImageUrl,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get currency => $state.composableBuilder(
      column: $state.table.currency,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get timezone => $state.composableBuilder(
      column: $state.table.timezone,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<bool> get isPremium => $state.composableBuilder(
      column: $state.table.isPremium,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<DateTime> get premiumExpiresAt => $state.composableBuilder(
      column: $state.table.premiumExpiresAt,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<DateTime> get createdAt => $state.composableBuilder(
      column: $state.table.createdAt,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<DateTime> get updatedAt => $state.composableBuilder(
      column: $state.table.updatedAt,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<DateTime> get lastLoginAt => $state.composableBuilder(
      column: $state.table.lastLoginAt,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));
}

class $$UserAccountsTableOrderingComposer
    extends OrderingComposer<_$AppDatabase, $UserAccountsTable> {
  $$UserAccountsTableOrderingComposer(super.$state);
  ColumnOrderings<String> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get name => $state.composableBuilder(
      column: $state.table.name,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get email => $state.composableBuilder(
      column: $state.table.email,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get accountType => $state.composableBuilder(
      column: $state.table.accountType,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<bool> get isActive => $state.composableBuilder(
      column: $state.table.isActive,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get profileImageUrl => $state.composableBuilder(
      column: $state.table.profileImageUrl,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get currency => $state.composableBuilder(
      column: $state.table.currency,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get timezone => $state.composableBuilder(
      column: $state.table.timezone,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<bool> get isPremium => $state.composableBuilder(
      column: $state.table.isPremium,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<DateTime> get premiumExpiresAt => $state.composableBuilder(
      column: $state.table.premiumExpiresAt,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<DateTime> get createdAt => $state.composableBuilder(
      column: $state.table.createdAt,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<DateTime> get updatedAt => $state.composableBuilder(
      column: $state.table.updatedAt,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<DateTime> get lastLoginAt => $state.composableBuilder(
      column: $state.table.lastLoginAt,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));
}

typedef $$PersonalTransactionsTableCreateCompanionBuilder
    = PersonalTransactionsCompanion Function({
  required String id,
  required String accountId,
  required String title,
  Value<String?> description,
  required double amount,
  required String type,
  required String category,
  required DateTime date,
  Value<String?> paymentMethod,
  Value<double> transactionCost,
  Value<String?> location,
  Value<String?> tags,
  Value<String?> receiptImageUrl,
  Value<bool> isRecurring,
  Value<String?> recurringPattern,
  Value<DateTime?> nextRecurringDate,
  Value<bool> isSynced,
  required DateTime createdAt,
  Value<DateTime?> updatedAt,
  Value<int> rowid,
});
typedef $$PersonalTransactionsTableUpdateCompanionBuilder
    = PersonalTransactionsCompanion Function({
  Value<String> id,
  Value<String> accountId,
  Value<String> title,
  Value<String?> description,
  Value<double> amount,
  Value<String> type,
  Value<String> category,
  Value<DateTime> date,
  Value<String?> paymentMethod,
  Value<double> transactionCost,
  Value<String?> location,
  Value<String?> tags,
  Value<String?> receiptImageUrl,
  Value<bool> isRecurring,
  Value<String?> recurringPattern,
  Value<DateTime?> nextRecurringDate,
  Value<bool> isSynced,
  Value<DateTime> createdAt,
  Value<DateTime?> updatedAt,
  Value<int> rowid,
});

class $$PersonalTransactionsTableTableManager extends RootTableManager<
    _$AppDatabase,
    $PersonalTransactionsTable,
    PersonalTransaction,
    $$PersonalTransactionsTableFilterComposer,
    $$PersonalTransactionsTableOrderingComposer,
    $$PersonalTransactionsTableCreateCompanionBuilder,
    $$PersonalTransactionsTableUpdateCompanionBuilder> {
  $$PersonalTransactionsTableTableManager(
      _$AppDatabase db, $PersonalTransactionsTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          filteringComposer: $$PersonalTransactionsTableFilterComposer(
              ComposerState(db, table)),
          orderingComposer: $$PersonalTransactionsTableOrderingComposer(
              ComposerState(db, table)),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<String> accountId = const Value.absent(),
            Value<String> title = const Value.absent(),
            Value<String?> description = const Value.absent(),
            Value<double> amount = const Value.absent(),
            Value<String> type = const Value.absent(),
            Value<String> category = const Value.absent(),
            Value<DateTime> date = const Value.absent(),
            Value<String?> paymentMethod = const Value.absent(),
            Value<double> transactionCost = const Value.absent(),
            Value<String?> location = const Value.absent(),
            Value<String?> tags = const Value.absent(),
            Value<String?> receiptImageUrl = const Value.absent(),
            Value<bool> isRecurring = const Value.absent(),
            Value<String?> recurringPattern = const Value.absent(),
            Value<DateTime?> nextRecurringDate = const Value.absent(),
            Value<bool> isSynced = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<DateTime?> updatedAt = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              PersonalTransactionsCompanion(
            id: id,
            accountId: accountId,
            title: title,
            description: description,
            amount: amount,
            type: type,
            category: category,
            date: date,
            paymentMethod: paymentMethod,
            transactionCost: transactionCost,
            location: location,
            tags: tags,
            receiptImageUrl: receiptImageUrl,
            isRecurring: isRecurring,
            recurringPattern: recurringPattern,
            nextRecurringDate: nextRecurringDate,
            isSynced: isSynced,
            createdAt: createdAt,
            updatedAt: updatedAt,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            required String accountId,
            required String title,
            Value<String?> description = const Value.absent(),
            required double amount,
            required String type,
            required String category,
            required DateTime date,
            Value<String?> paymentMethod = const Value.absent(),
            Value<double> transactionCost = const Value.absent(),
            Value<String?> location = const Value.absent(),
            Value<String?> tags = const Value.absent(),
            Value<String?> receiptImageUrl = const Value.absent(),
            Value<bool> isRecurring = const Value.absent(),
            Value<String?> recurringPattern = const Value.absent(),
            Value<DateTime?> nextRecurringDate = const Value.absent(),
            Value<bool> isSynced = const Value.absent(),
            required DateTime createdAt,
            Value<DateTime?> updatedAt = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              PersonalTransactionsCompanion.insert(
            id: id,
            accountId: accountId,
            title: title,
            description: description,
            amount: amount,
            type: type,
            category: category,
            date: date,
            paymentMethod: paymentMethod,
            transactionCost: transactionCost,
            location: location,
            tags: tags,
            receiptImageUrl: receiptImageUrl,
            isRecurring: isRecurring,
            recurringPattern: recurringPattern,
            nextRecurringDate: nextRecurringDate,
            isSynced: isSynced,
            createdAt: createdAt,
            updatedAt: updatedAt,
            rowid: rowid,
          ),
        ));
}

class $$PersonalTransactionsTableFilterComposer
    extends FilterComposer<_$AppDatabase, $PersonalTransactionsTable> {
  $$PersonalTransactionsTableFilterComposer(super.$state);
  ColumnFilters<String> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get accountId => $state.composableBuilder(
      column: $state.table.accountId,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get title => $state.composableBuilder(
      column: $state.table.title,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get description => $state.composableBuilder(
      column: $state.table.description,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<double> get amount => $state.composableBuilder(
      column: $state.table.amount,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get type => $state.composableBuilder(
      column: $state.table.type,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get category => $state.composableBuilder(
      column: $state.table.category,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<DateTime> get date => $state.composableBuilder(
      column: $state.table.date,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get paymentMethod => $state.composableBuilder(
      column: $state.table.paymentMethod,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<double> get transactionCost => $state.composableBuilder(
      column: $state.table.transactionCost,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get location => $state.composableBuilder(
      column: $state.table.location,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get tags => $state.composableBuilder(
      column: $state.table.tags,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get receiptImageUrl => $state.composableBuilder(
      column: $state.table.receiptImageUrl,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<bool> get isRecurring => $state.composableBuilder(
      column: $state.table.isRecurring,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get recurringPattern => $state.composableBuilder(
      column: $state.table.recurringPattern,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<DateTime> get nextRecurringDate => $state.composableBuilder(
      column: $state.table.nextRecurringDate,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<bool> get isSynced => $state.composableBuilder(
      column: $state.table.isSynced,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<DateTime> get createdAt => $state.composableBuilder(
      column: $state.table.createdAt,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<DateTime> get updatedAt => $state.composableBuilder(
      column: $state.table.updatedAt,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));
}

class $$PersonalTransactionsTableOrderingComposer
    extends OrderingComposer<_$AppDatabase, $PersonalTransactionsTable> {
  $$PersonalTransactionsTableOrderingComposer(super.$state);
  ColumnOrderings<String> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get accountId => $state.composableBuilder(
      column: $state.table.accountId,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get title => $state.composableBuilder(
      column: $state.table.title,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get description => $state.composableBuilder(
      column: $state.table.description,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<double> get amount => $state.composableBuilder(
      column: $state.table.amount,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get type => $state.composableBuilder(
      column: $state.table.type,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get category => $state.composableBuilder(
      column: $state.table.category,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<DateTime> get date => $state.composableBuilder(
      column: $state.table.date,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get paymentMethod => $state.composableBuilder(
      column: $state.table.paymentMethod,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<double> get transactionCost => $state.composableBuilder(
      column: $state.table.transactionCost,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get location => $state.composableBuilder(
      column: $state.table.location,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get tags => $state.composableBuilder(
      column: $state.table.tags,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get receiptImageUrl => $state.composableBuilder(
      column: $state.table.receiptImageUrl,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<bool> get isRecurring => $state.composableBuilder(
      column: $state.table.isRecurring,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get recurringPattern => $state.composableBuilder(
      column: $state.table.recurringPattern,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<DateTime> get nextRecurringDate => $state.composableBuilder(
      column: $state.table.nextRecurringDate,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<bool> get isSynced => $state.composableBuilder(
      column: $state.table.isSynced,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<DateTime> get createdAt => $state.composableBuilder(
      column: $state.table.createdAt,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<DateTime> get updatedAt => $state.composableBuilder(
      column: $state.table.updatedAt,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));
}

typedef $$AccountSettingsTableCreateCompanionBuilder = AccountSettingsCompanion
    Function({
  required String accountId,
  required String key,
  required String value,
  required DateTime updatedAt,
  Value<int> rowid,
});
typedef $$AccountSettingsTableUpdateCompanionBuilder = AccountSettingsCompanion
    Function({
  Value<String> accountId,
  Value<String> key,
  Value<String> value,
  Value<DateTime> updatedAt,
  Value<int> rowid,
});

class $$AccountSettingsTableTableManager extends RootTableManager<
    _$AppDatabase,
    $AccountSettingsTable,
    AccountSetting,
    $$AccountSettingsTableFilterComposer,
    $$AccountSettingsTableOrderingComposer,
    $$AccountSettingsTableCreateCompanionBuilder,
    $$AccountSettingsTableUpdateCompanionBuilder> {
  $$AccountSettingsTableTableManager(
      _$AppDatabase db, $AccountSettingsTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          filteringComposer:
              $$AccountSettingsTableFilterComposer(ComposerState(db, table)),
          orderingComposer:
              $$AccountSettingsTableOrderingComposer(ComposerState(db, table)),
          updateCompanionCallback: ({
            Value<String> accountId = const Value.absent(),
            Value<String> key = const Value.absent(),
            Value<String> value = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              AccountSettingsCompanion(
            accountId: accountId,
            key: key,
            value: value,
            updatedAt: updatedAt,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String accountId,
            required String key,
            required String value,
            required DateTime updatedAt,
            Value<int> rowid = const Value.absent(),
          }) =>
              AccountSettingsCompanion.insert(
            accountId: accountId,
            key: key,
            value: value,
            updatedAt: updatedAt,
            rowid: rowid,
          ),
        ));
}

class $$AccountSettingsTableFilterComposer
    extends FilterComposer<_$AppDatabase, $AccountSettingsTable> {
  $$AccountSettingsTableFilterComposer(super.$state);
  ColumnFilters<String> get accountId => $state.composableBuilder(
      column: $state.table.accountId,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get key => $state.composableBuilder(
      column: $state.table.key,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get value => $state.composableBuilder(
      column: $state.table.value,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<DateTime> get updatedAt => $state.composableBuilder(
      column: $state.table.updatedAt,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));
}

class $$AccountSettingsTableOrderingComposer
    extends OrderingComposer<_$AppDatabase, $AccountSettingsTable> {
  $$AccountSettingsTableOrderingComposer(super.$state);
  ColumnOrderings<String> get accountId => $state.composableBuilder(
      column: $state.table.accountId,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get key => $state.composableBuilder(
      column: $state.table.key,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get value => $state.composableBuilder(
      column: $state.table.value,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<DateTime> get updatedAt => $state.composableBuilder(
      column: $state.table.updatedAt,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));
}

typedef $$SyncStatusTableCreateCompanionBuilder = SyncStatusCompanion Function({
  required String accountId,
  Value<DateTime?> lastSyncAt,
  Value<bool> hasUnsyncedChanges,
  Value<String?> syncError,
  required DateTime updatedAt,
  Value<int> rowid,
});
typedef $$SyncStatusTableUpdateCompanionBuilder = SyncStatusCompanion Function({
  Value<String> accountId,
  Value<DateTime?> lastSyncAt,
  Value<bool> hasUnsyncedChanges,
  Value<String?> syncError,
  Value<DateTime> updatedAt,
  Value<int> rowid,
});

class $$SyncStatusTableTableManager extends RootTableManager<
    _$AppDatabase,
    $SyncStatusTable,
    SyncStatu,
    $$SyncStatusTableFilterComposer,
    $$SyncStatusTableOrderingComposer,
    $$SyncStatusTableCreateCompanionBuilder,
    $$SyncStatusTableUpdateCompanionBuilder> {
  $$SyncStatusTableTableManager(_$AppDatabase db, $SyncStatusTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          filteringComposer:
              $$SyncStatusTableFilterComposer(ComposerState(db, table)),
          orderingComposer:
              $$SyncStatusTableOrderingComposer(ComposerState(db, table)),
          updateCompanionCallback: ({
            Value<String> accountId = const Value.absent(),
            Value<DateTime?> lastSyncAt = const Value.absent(),
            Value<bool> hasUnsyncedChanges = const Value.absent(),
            Value<String?> syncError = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              SyncStatusCompanion(
            accountId: accountId,
            lastSyncAt: lastSyncAt,
            hasUnsyncedChanges: hasUnsyncedChanges,
            syncError: syncError,
            updatedAt: updatedAt,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String accountId,
            Value<DateTime?> lastSyncAt = const Value.absent(),
            Value<bool> hasUnsyncedChanges = const Value.absent(),
            Value<String?> syncError = const Value.absent(),
            required DateTime updatedAt,
            Value<int> rowid = const Value.absent(),
          }) =>
              SyncStatusCompanion.insert(
            accountId: accountId,
            lastSyncAt: lastSyncAt,
            hasUnsyncedChanges: hasUnsyncedChanges,
            syncError: syncError,
            updatedAt: updatedAt,
            rowid: rowid,
          ),
        ));
}

class $$SyncStatusTableFilterComposer
    extends FilterComposer<_$AppDatabase, $SyncStatusTable> {
  $$SyncStatusTableFilterComposer(super.$state);
  ColumnFilters<String> get accountId => $state.composableBuilder(
      column: $state.table.accountId,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<DateTime> get lastSyncAt => $state.composableBuilder(
      column: $state.table.lastSyncAt,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<bool> get hasUnsyncedChanges => $state.composableBuilder(
      column: $state.table.hasUnsyncedChanges,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get syncError => $state.composableBuilder(
      column: $state.table.syncError,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<DateTime> get updatedAt => $state.composableBuilder(
      column: $state.table.updatedAt,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));
}

class $$SyncStatusTableOrderingComposer
    extends OrderingComposer<_$AppDatabase, $SyncStatusTable> {
  $$SyncStatusTableOrderingComposer(super.$state);
  ColumnOrderings<String> get accountId => $state.composableBuilder(
      column: $state.table.accountId,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<DateTime> get lastSyncAt => $state.composableBuilder(
      column: $state.table.lastSyncAt,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<bool> get hasUnsyncedChanges => $state.composableBuilder(
      column: $state.table.hasUnsyncedChanges,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get syncError => $state.composableBuilder(
      column: $state.table.syncError,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<DateTime> get updatedAt => $state.composableBuilder(
      column: $state.table.updatedAt,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));
}

class $AppDatabaseManager {
  final _$AppDatabase _db;
  $AppDatabaseManager(this._db);
  $$UserAccountsTableTableManager get userAccounts =>
      $$UserAccountsTableTableManager(_db, _db.userAccounts);
  $$PersonalTransactionsTableTableManager get personalTransactions =>
      $$PersonalTransactionsTableTableManager(_db, _db.personalTransactions);
  $$AccountSettingsTableTableManager get accountSettings =>
      $$AccountSettingsTableTableManager(_db, _db.accountSettings);
  $$SyncStatusTableTableManager get syncStatus =>
      $$SyncStatusTableTableManager(_db, _db.syncStatus);
}
