import 'package:get/get.dart';
import '../../features/onboarding/screens/splash_screen.dart';
import '../../features/onboarding/screens/onboarding_screen.dart';
import '../../features/auth/screens/login_screen.dart';
import '../../features/auth/screens/signup_screen.dart';
import '../../features/navigation/screens/main_navigation_wrapper.dart';
import '../../features/dashboard/screens/dashboard_screen.dart';
import '../../features/transactions/screens/transactions_screen.dart';
import '../../features/transactions/screens/add_transaction_screen.dart';
import '../../features/transactions/screens/transaction_management_screen.dart';
import '../../features/income/screens/income_screen.dart';
import '../../features/expenses/screens/expenses_screen.dart';
import '../../features/sales/screens/sales_screen.dart';
import '../../features/purchases/screens/purchases_screen.dart';
import '../../features/inventory/screens/inventory_screen.dart';
import '../../features/profile/screens/profile_screen.dart';
import '../../features/analytics/screens/analytics_screen.dart';
import '../../features/settings/screens/theme_settings_screen.dart';
import '../../features/transactions/screens/add_universal_transaction_screen.dart';
import '../../features/transactions/screens/recurring_transactions_screen.dart';
import '../../features/budget/screens/budget_screen.dart';
import '../../features/training/screens/transaction_training_screen.dart';

class AppRoutes {
  static const String splash = '/';
  static const String onboarding = '/onboarding';
  static const String login = '/login';
  static const String signup = '/signup';
  static const String dashboard = '/dashboard';
  static const String home = '/home';
  static const String transactions = '/transactions';
  static const String income = '/income';
  static const String expenses = '/expenses';
  static const String sales = '/sales';
  static const String purchases = '/purchases';
  static const String inventory = '/inventory';
  static const String profile = '/profile';
  static const String analytics = '/analytics';
  static const String addTransaction = '/add-transaction';
  static const String addUniversalTransaction = '/add-universal-transaction';
  static const String recurringTransactions = '/recurring-transactions';
  static const String transactionManagement = '/transaction-management';
  static const String themeSettings = '/theme-settings';
  static const String budget = '/budget';
  static const String budgets = '/budgets';
  static const String training = '/training';

  static List<GetPage> routes = [
    GetPage(
      name: splash,
      page: () => const SplashScreen(),
    ),
    GetPage(
      name: onboarding,
      page: () => const OnboardingScreen(),
    ),
    GetPage(
      name: login,
      page: () => const LoginScreen(),
    ),
    GetPage(
      name: signup,
      page: () => const SignUpScreen(),
    ),
    GetPage(
      name: dashboard,
      page: () => const MainNavigationWrapper(),
    ),
    GetPage(
      name: home,
      page: () => const DashboardScreen(),
    ),
    GetPage(
      name: transactions,
      page: () => const TransactionsScreen(),
    ),
    GetPage(
      name: income,
      page: () => const IncomeScreen(),
    ),
    GetPage(
      name: expenses,
      page: () => const ExpensesScreen(),
    ),
    GetPage(
      name: sales,
      page: () => const SalesScreen(),
    ),
    GetPage(
      name: purchases,
      page: () => const PurchasesScreen(),
    ),
    GetPage(
      name: inventory,
      page: () => const InventoryScreen(),
    ),
    GetPage(
      name: profile,
      page: () => const ProfileScreen(),
    ),
    GetPage(
      name: analytics,
      page: () => const AnalyticsScreen(),
    ),
    GetPage(
      name: addTransaction,
      page: () => const AddTransactionScreen(),
    ),
    GetPage(
      name: addUniversalTransaction,
      page: () => const AddUniversalTransactionScreen(),
    ),
    GetPage(
      name: recurringTransactions,
      page: () => const RecurringTransactionsScreen(),
    ),
    GetPage(
      name: transactionManagement,
      page: () => const TransactionManagementScreen(),
    ),
    GetPage(
      name: themeSettings,
      page: () => const ThemeSettingsScreen(),
    ),
    GetPage(
      name: budget,
      page: () => const BudgetScreen(),
    ),
    GetPage(
      name: budgets,
      page: () => const BudgetScreen(),
    ),
    GetPage(
      name: training,
      page: () => const TransactionTrainingScreen(),
    ),
  ];
}
