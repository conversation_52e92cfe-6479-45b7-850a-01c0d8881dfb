import 'dart:convert';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import '../models/transaction.dart';
import '../models/budget.dart';
import 'local_storage_service.dart';
import '../services/account_service.dart';

class BudgetService extends GetxController {
  static BudgetService get to => Get.find();
  
  final GetStorage _storage = GetStorage();
  final RxList<Budget> _budgets = <Budget>[].obs;
  final RxBool _isLoading = false.obs;
  final AccountService _accountService = Get.find<AccountService>();

  List<Budget> get budgets => _budgets;
  bool get isLoading => _isLoading.value;

  @override
  void onInit() {
    super.onInit();
    loadBudgets();
  }

  /// Load budgets from storage
  Future<void> loadBudgets() async {
    final accountId = AccountService.to.currentAccountId;
    if (accountId.isEmpty) return;

    try {
      _isLoading.value = true;
      final key = 'budgets_$accountId';
      final budgetsJson = _storage.read(key);
      
      if (budgetsJson != null) {
        final List<dynamic> budgetsList = jsonDecode(budgetsJson);
        _budgets.value = budgetsList
            .map((json) => Budget.fromJson(json))
            .toList();
      } else {
        // Initialize with sample data for demo
        _initializeSampleBudgets();
      }
    } catch (e) {
      print('Error loading budgets: $e');
      _initializeSampleBudgets();
    } finally {
      _isLoading.value = false;
    }
  }

  /// Save budgets to storage
  Future<void> _saveBudgets() async {
    final accountId = AccountService.to.currentAccountId;
    if (accountId.isEmpty) return;

    try {
      final key = 'budgets_$accountId';
      final jsonList = _budgets
          .map((budget) => budget.toJson())
          .toList();
      
      await _storage.write(key, jsonEncode(jsonList));
    } catch (e) {
      print('Error saving budgets: $e');
    }
  }

  /// Add a new budget
  Future<bool> addBudget({
    required String name,
    required double amount,
    required TransactionCategory category,
    required BudgetPeriod period,
  }) async {
    try {
      _isLoading.value = true;
      
      // Check if budget already exists for this category and period
      final existingBudget = _budgets.firstWhereOrNull(
        (budget) => budget.category == category && budget.period == period,
      );
      
      if (existingBudget != null) {
        Get.snackbar(
          'Budget Exists',
          'A budget for this category and period already exists',
          backgroundColor: Get.theme.colorScheme.error,
          colorText: Get.theme.colorScheme.onError,
        );
        return false;
      }

      final newBudget = Budget(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        accountId: _accountService.currentAccount?.id ?? '',
        name: name,
        budgetAmount: amount,
        spentAmount: 0.0,
        category: category,
        period: period,
        startDate: DateTime.now(),
        endDate: DateTime.now().add(const Duration(days: 30)),
        createdAt: DateTime.now(),
      );

      _budgets.add(newBudget);
      await _saveBudgets();
      
      Get.snackbar(
        'Success',
        'Budget created successfully',
        backgroundColor: Get.theme.colorScheme.primary,
        colorText: Get.theme.colorScheme.onPrimary,
      );
      
      return true;
    } catch (e) {
      print('Error adding budget: $e');
      Get.snackbar(
        'Error',
        'Failed to create budget',
        backgroundColor: Get.theme.colorScheme.error,
        colorText: Get.theme.colorScheme.onError,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Update an existing budget
  Future<bool> updateBudget({
    required String id,
    required String name,
    required double amount,
    required TransactionCategory category,
    required BudgetPeriod period,
  }) async {
    try {
      _isLoading.value = true;
      
      final index = _budgets.indexWhere((budget) => budget.id == id);
      if (index == -1) {
        Get.snackbar(
          'Error',
          'Budget not found',
          backgroundColor: Get.theme.colorScheme.error,
          colorText: Get.theme.colorScheme.onError,
        );
        return false;
      }

      final existingBudget = _budgets[index];
      final updatedBudget = Budget(
        id: existingBudget.id,
        accountId: existingBudget.accountId,
        name: name,
        budgetAmount: amount,
        spentAmount: existingBudget.spentAmount,
        category: category,
        period: period,
        startDate: existingBudget.startDate,
        endDate: existingBudget.endDate,
        createdAt: existingBudget.createdAt,
        updatedAt: DateTime.now(),
      );

      _budgets[index] = updatedBudget;
      await _saveBudgets();
      
      Get.snackbar(
        'Success',
        'Budget updated successfully',
        backgroundColor: Get.theme.colorScheme.primary,
        colorText: Get.theme.colorScheme.onPrimary,
      );
      
      return true;
    } catch (e) {
      print('Error updating budget: $e');
      Get.snackbar(
        'Error',
        'Failed to update budget',
        backgroundColor: Get.theme.colorScheme.error,
        colorText: Get.theme.colorScheme.onError,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Delete a budget
  Future<bool> deleteBudget(String id) async {
    try {
      _isLoading.value = true;
      
      _budgets.removeWhere((budget) => budget.id == id);
      await _saveBudgets();
      
      Get.snackbar(
        'Success',
        'Budget deleted successfully',
        backgroundColor: Get.theme.colorScheme.primary,
        colorText: Get.theme.colorScheme.onPrimary,
      );
      
      return true;
    } catch (e) {
      print('Error deleting budget: $e');
      Get.snackbar(
        'Error',
        'Failed to delete budget',
        backgroundColor: Get.theme.colorScheme.error,
        colorText: Get.theme.colorScheme.onError,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Get budgets by period
  List<Budget> getBudgetsByPeriod(BudgetPeriod period) {
    return _budgets.where((budget) => budget.period == period).toList();
  }

  /// Get budget by category and period
  Budget? getBudgetByCategory(TransactionCategory category, BudgetPeriod period) {
    return _budgets.firstWhereOrNull(
      (budget) => budget.category == category && budget.period == period,
    );
  }

  /// Update spent amount for a budget
  Future<void> updateSpentAmount(String budgetId, double newSpentAmount) async {
    final index = _budgets.indexWhere((budget) => budget.id == budgetId);
    if (index != -1) {
      final budget = _budgets[index];
      _budgets[index] = Budget(
        id: budget.id,
        accountId: budget.accountId,
        name: budget.name,
        budgetAmount: budget.budgetAmount,
        spentAmount: newSpentAmount,
        category: budget.category,
        period: budget.period,
        startDate: budget.startDate,
        endDate: budget.endDate,
        createdAt: budget.createdAt,
        updatedAt: DateTime.now(),
      );
      await _saveBudgets();
    }
  }

  /// Get total budget amount for a period
  double getTotalBudgetAmount(BudgetPeriod period) {
    return getBudgetsByPeriod(period)
        .fold(0.0, (sum, budget) => sum + budget.budgetAmount);
  }

  /// Get total spent amount for a period
  double getTotalSpentAmount(BudgetPeriod period) {
    return getBudgetsByPeriod(period)
        .fold(0.0, (sum, budget) => sum + budget.spentAmount);
  }

  /// Initialize sample budgets for demo
  void _initializeSampleBudgets() {
    final currentAccountId = _accountService.currentAccount?.id ?? '';
    final now = DateTime.now();
    _budgets.value = [
      Budget(
        id: '1',
        accountId: currentAccountId,
        name: 'Food & Dining',
        budgetAmount: 800.0,
        spentAmount: 650.0,
        category: TransactionCategory.food,
        period: BudgetPeriod.monthly,
        startDate: now.subtract(const Duration(days: 30)),
        endDate: now,
        createdAt: now.subtract(const Duration(days: 30)),
      ),
      Budget(
        id: '2',
        accountId: currentAccountId,
        name: 'Transportation',
        budgetAmount: 300.0,
        spentAmount: 280.0,
        category: TransactionCategory.transportation,
        period: BudgetPeriod.monthly,
        startDate: now.subtract(const Duration(days: 25)),
        endDate: now.add(const Duration(days: 5)),
        createdAt: now.subtract(const Duration(days: 25)),
      ),
      Budget(
        id: '3',
        accountId: currentAccountId,
        name: 'Entertainment',
        budgetAmount: 200.0,
        spentAmount: 150.0,
        category: TransactionCategory.entertainment,
        period: BudgetPeriod.monthly,
        startDate: now.subtract(const Duration(days: 20)),
        endDate: now.add(const Duration(days: 10)),
        createdAt: now.subtract(const Duration(days: 20)),
      ),
      Budget(
        id: '4',
        accountId: currentAccountId,
        name: 'Shopping',
        budgetAmount: 400.0,
        spentAmount: 420.0,
        category: TransactionCategory.shopping,
        period: BudgetPeriod.monthly,
        startDate: now.subtract(const Duration(days: 15)),
        endDate: now.add(const Duration(days: 15)),
        createdAt: now.subtract(const Duration(days: 15)),
      ),
      Budget(
        id: '5',
        accountId: currentAccountId,
        name: 'Lunch Budget',
        budgetAmount: 100.0,
        spentAmount: 85.0,
        category: TransactionCategory.food,
        period: BudgetPeriod.weekly,
        startDate: now.subtract(const Duration(days: 7)),
        endDate: now,
        createdAt: now.subtract(const Duration(days: 7)),
      ),
      Budget(
        id: '6',
        accountId: currentAccountId,
        name: 'Weekend Fun',
        budgetAmount: 80.0,
        spentAmount: 45.0,
        category: TransactionCategory.entertainment,
        period: BudgetPeriod.weekly,
        startDate: now.subtract(const Duration(days: 5)),
        endDate: now.add(const Duration(days: 2)),
        createdAt: now.subtract(const Duration(days: 5)),
      ),
    ];
    _saveBudgets();
  }

  /// Clear all budgets (for development)
  Future<void> clearAllBudgets() async {
    _budgets.clear();
    await _saveBudgets();
  }
}
