import 'package:get/get.dart';
import 'dart:math';
import '../models/transaction.dart';
import '../models/budget.dart';
import '../models/analytics_insight.dart';
import '../models/spending_pattern.dart';
import '../models/financial_health.dart';
import 'personal_transaction_service.dart';
import 'budget_service.dart';
import 'account_service.dart';

class AnalyticsService extends GetxService {
  final PersonalTransactionService _transactionService = Get.find();
  final BudgetService _budgetService = Get.find();
  final AccountService _accountService = Get.find();

  final RxList<AnalyticsInsight> _insights = <AnalyticsInsight>[].obs;
  final Rx<FinancialHealth?> _financialHealth = Rx<FinancialHealth?>(null);
  final RxBool _isAnalyzing = false.obs;

  List<AnalyticsInsight> get insights => _insights;
  FinancialHealth? get financialHealth => _financialHealth.value;
  bool get isAnalyzing => _isAnalyzing.value;

  @override
  void onInit() {
    super.onInit();
    _generateInsights();
    
    // Listen to transaction changes
    ever(_transactionService.transactions as RxInterface<Object?>, (_) => _generateInsights());
  }

  /// Generate comprehensive financial insights
  Future<void> _generateInsights() async {
    try {
      _isAnalyzing.value = true;
      
      final personalTransactions = _transactionService.transactions;
      final budgets = _budgetService.budgets;

      if (personalTransactions.isEmpty) {
        _insights.clear();
        _financialHealth.value = null;
        return;
      }

      // Convert PersonalTransaction to Transaction for analysis
      final transactions = personalTransactions.map((pt) => Transaction(
        id: pt.id,
        accountId: pt.accountId,
        title: pt.title,
        description: pt.description,
        amount: pt.amount,
        type: TransactionType.values.firstWhere(
          (e) => e.name == pt.type,
          orElse: () => TransactionType.expense,
        ),
        category: TransactionCategory.values.firstWhere(
          (e) => e.name == pt.category,
          orElse: () => TransactionCategory.other,
        ),
        date: pt.date,
        createdAt: pt.createdAt,
        updatedAt: pt.updatedAt,
      )).toList();

      final insights = <AnalyticsInsight>[];

      // Generate various insights
      insights.addAll(await _generateSpendingInsights(transactions));
      insights.addAll(await _generateIncomeInsights(transactions));
      insights.addAll(await _generateBudgetInsights(transactions, budgets));
      insights.addAll(await _generateTrendInsights(transactions));
      insights.addAll(await _generateCategoryInsights(transactions));
      insights.addAll(await _generateSavingsInsights(transactions));
      
      // Sort by priority and relevance
      insights.sort((a, b) => b.priority.compareTo(a.priority));
      
      _insights.value = insights;
      _financialHealth.value = await _calculateFinancialHealth(transactions, budgets);
      
    } catch (e) {
      print('Error generating insights: $e');
    } finally {
      _isAnalyzing.value = false;
    }
  }

  /// Generate spending-related insights
  Future<List<AnalyticsInsight>> _generateSpendingInsights(List<Transaction> transactions) async {
    final insights = <AnalyticsInsight>[];
    final now = DateTime.now();
    final thisMonth = transactions.where((t) =>
        t.type == TransactionType.expense &&
        t.date.year == now.year &&
        t.date.month == now.month).toList();

    final lastMonth = transactions.where((t) =>
        t.type == TransactionType.expense &&
        t.date.year == (now.month == 1 ? now.year - 1 : now.year) &&
        t.date.month == (now.month == 1 ? 12 : now.month - 1)).toList();

    if (thisMonth.isNotEmpty && lastMonth.isNotEmpty) {
      final thisMonthTotal = thisMonth.fold(0.0, (sum, t) => sum + t.amount);
      final lastMonthTotal = lastMonth.fold(0.0, (sum, t) => sum + t.amount);
      final change = ((thisMonthTotal - lastMonthTotal) / lastMonthTotal) * 100;

      if (change.abs() > 10) {
        insights.add(AnalyticsInsight(
          id: 'spending_change',
          type: change > 0 ? InsightType.warning : InsightType.positive,
          title: change > 0 ? 'Spending Increased' : 'Spending Decreased',
          description: 'Your spending ${change > 0 ? 'increased' : 'decreased'} by ${change.abs().toStringAsFixed(1)}% this month',
          value: change,
          priority: change.abs() > 25 ? 9 : 7,
          actionable: true,
          action: change > 0 ? 'Review your expenses and identify areas to cut back' : 'Great job on reducing expenses!',
        ));
      }
    }

    // Identify highest spending day
    final dailySpending = <DateTime, double>{};
    for (final transaction in thisMonth) {
      final date = DateTime(transaction.date.year, transaction.date.month, transaction.date.day);
      dailySpending[date] = (dailySpending[date] ?? 0) + transaction.amount;
    }

    if (dailySpending.isNotEmpty) {
      final highestDay = dailySpending.entries.reduce((a, b) => a.value > b.value ? a : b);
      final averageDaily = dailySpending.values.reduce((a, b) => a + b) / dailySpending.length;
      
      if (highestDay.value > averageDaily * 2) {
        insights.add(AnalyticsInsight(
          id: 'highest_spending_day',
          type: InsightType.info,
          title: 'Highest Spending Day',
          description: 'You spent KES ${highestDay.value.toStringAsFixed(2)} on ${_formatDate(highestDay.key)}',
          value: highestDay.value,
          priority: 6,
          actionable: true,
          action: 'Review what caused this high spending day',
        ));
      }
    }

    return insights;
  }

  /// Generate income-related insights
  Future<List<AnalyticsInsight>> _generateIncomeInsights(List<Transaction> transactions) async {
    final insights = <AnalyticsInsight>[];
    final now = DateTime.now();
    final thisMonth = transactions.where((t) =>
        t.type == TransactionType.income &&
        t.date.year == now.year &&
        t.date.month == now.month).toList();

    if (thisMonth.isNotEmpty) {
      final totalIncome = thisMonth.fold(0.0, (sum, t) => sum + t.amount);
      final incomeStreams = thisMonth.map((t) => t.category).toSet().length;

      insights.add(AnalyticsInsight(
        id: 'income_streams',
        type: incomeStreams > 1 ? InsightType.positive : InsightType.info,
        title: 'Income Diversification',
        description: 'You have $incomeStreams income stream${incomeStreams > 1 ? 's' : ''} this month',
        value: incomeStreams.toDouble(),
        priority: incomeStreams == 1 ? 8 : 5,
        actionable: incomeStreams == 1,
        action: incomeStreams == 1 ? 'Consider diversifying your income sources' : null,
      ));
    }

    return insights;
  }

  /// Generate budget-related insights
  Future<List<AnalyticsInsight>> _generateBudgetInsights(List<Transaction> transactions, List<Budget> budgets) async {
    final insights = <AnalyticsInsight>[];
    final now = DateTime.now();

    for (final budget in budgets) {
      if (!budget.isActive) continue;

      final categoryTransactions = transactions.where((t) =>
          t.category == budget.category &&
          t.type == TransactionType.expense &&
          t.date.year == now.year &&
          t.date.month == now.month).toList();

      final spent = categoryTransactions.fold(0.0, (sum, t) => sum + t.amount);
      final percentage = (spent / budget.budgetAmount) * 100;

      if (percentage > 90) {
        insights.add(AnalyticsInsight(
          id: 'budget_exceeded_${budget.category}',
          type: percentage > 100 ? InsightType.critical : InsightType.warning,
          title: 'Budget Alert: ${budget.category}',
          description: 'You\'ve used ${percentage.toStringAsFixed(1)}% of your ${budget.category} budget',
          value: percentage,
          priority: percentage > 100 ? 10 : 8,
          actionable: true,
          action: 'Consider reducing spending in this category',
        ));
      } else if (percentage < 50 && now.day > 20) {
        insights.add(AnalyticsInsight(
          id: 'budget_underused_${budget.category}',
          type: InsightType.positive,
          title: 'Budget Surplus: ${budget.category}',
          description: 'You\'ve only used ${percentage.toStringAsFixed(1)}% of your ${budget.category} budget',
          value: percentage,
          priority: 4,
          actionable: true,
          action: 'Consider reallocating unused budget to other categories',
        ));
      }
    }

    return insights;
  }

  /// Generate trend insights
  Future<List<AnalyticsInsight>> _generateTrendInsights(List<Transaction> transactions) async {
    final insights = <AnalyticsInsight>[];
    
    // Analyze spending trends over last 6 months
    final patterns = _analyzeSpendingPatterns(transactions);
    
    for (final pattern in patterns) {
      if (pattern.confidence > 0.7) {
        insights.add(AnalyticsInsight(
          id: 'trend_${pattern.category}',
          type: pattern.trend == 'increasing' ? InsightType.warning : InsightType.positive,
          title: '${pattern.category} Trend',
          description: 'Your ${pattern.category} spending is ${pattern.trend}',
          value: pattern.changePercentage,
          priority: pattern.confidence > 0.9 ? 7 : 5,
          actionable: pattern.trend == 'increasing',
          action: pattern.trend == 'increasing' ? 'Monitor this category closely' : null,
        ));
      }
    }

    return insights;
  }

  /// Generate category insights
  Future<List<AnalyticsInsight>> _generateCategoryInsights(List<Transaction> transactions) async {
    final insights = <AnalyticsInsight>[];
    final now = DateTime.now();
    final thisMonth = transactions.where((t) =>
        t.type == TransactionType.expense &&
        t.date.year == now.year &&
        t.date.month == now.month).toList();

    if (thisMonth.isEmpty) return insights;

    // Find top spending category
    final categoryTotals = <String, double>{};
    for (final transaction in thisMonth) {
      final categoryKey = transaction.category.name;
      categoryTotals[categoryKey] =
          (categoryTotals[categoryKey] ?? 0) + transaction.amount;
    }

    final topCategory = categoryTotals.entries.reduce((a, b) => a.value > b.value ? a : b);
    final totalSpending = categoryTotals.values.reduce((a, b) => a + b);
    final percentage = (topCategory.value / totalSpending) * 100;

    if (percentage > 40) {
      insights.add(AnalyticsInsight(
        id: 'top_category',
        type: InsightType.warning,
        title: 'Dominant Spending Category',
        description: '${topCategory.key} accounts for ${percentage.toStringAsFixed(1)}% of your spending',
        value: percentage,
        priority: 7,
        actionable: true,
        action: 'Consider diversifying your spending or reducing this category',
      ));
    }

    return insights;
  }

  /// Generate savings insights
  Future<List<AnalyticsInsight>> _generateSavingsInsights(List<Transaction> transactions) async {
    final insights = <AnalyticsInsight>[];
    final now = DateTime.now();
    
    final thisMonthIncome = transactions.where((t) =>
        t.type == TransactionType.income &&
        t.date.year == now.year &&
        t.date.month == now.month).fold(0.0, (sum, t) => sum + t.amount);

    final thisMonthExpenses = transactions.where((t) =>
        t.type == TransactionType.expense &&
        t.date.year == now.year &&
        t.date.month == now.month).fold(0.0, (sum, t) => sum + t.amount);

    if (thisMonthIncome > 0) {
      final savingsRate = ((thisMonthIncome - thisMonthExpenses) / thisMonthIncome) * 100;
      
      insights.add(AnalyticsInsight(
        id: 'savings_rate',
        type: savingsRate > 20 ? InsightType.positive : 
              savingsRate > 10 ? InsightType.info : InsightType.warning,
        title: 'Savings Rate',
        description: 'You\'re saving ${savingsRate.toStringAsFixed(1)}% of your income this month',
        value: savingsRate,
        priority: savingsRate < 10 ? 9 : 6,
        actionable: savingsRate < 20,
        action: savingsRate < 10 ? 'Try to save at least 10% of your income' : 
                savingsRate < 20 ? 'Aim for a 20% savings rate' : null,
      ));
    }

    return insights;
  }

  /// Calculate financial health score
  Future<FinancialHealth> _calculateFinancialHealth(List<Transaction> transactions, List<Budget> budgets) async {
    double score = 0.0;
    final factors = <String, double>{};
    
    final now = DateTime.now();
    final thisMonthIncome = transactions.where((t) =>
        t.type == TransactionType.income &&
        t.date.year == now.year &&
        t.date.month == now.month).fold(0.0, (sum, t) => sum + t.amount);

    final thisMonthExpenses = transactions.where((t) =>
        t.type == TransactionType.expense &&
        t.date.year == now.year &&
        t.date.month == now.month).fold(0.0, (sum, t) => sum + t.amount);

    // Savings rate (30% weight)
    if (thisMonthIncome > 0) {
      final savingsRate = ((thisMonthIncome - thisMonthExpenses) / thisMonthIncome) * 100;
      final savingsScore = (savingsRate / 20).clamp(0.0, 1.0) * 30;
      score += savingsScore;
      factors['Savings Rate'] = savingsScore;
    }

    // Budget adherence (25% weight)
    double budgetScore = 0.0;
    int budgetCount = 0;
    for (final budget in budgets.where((b) => b.isActive)) {
      final spent = transactions.where((t) =>
          t.category == budget.category &&
          t.type == TransactionType.expense &&
          t.date.year == now.year &&
          t.date.month == now.month).fold(0.0, (sum, t) => sum + t.amount);
      
      final adherence = 1.0 - (spent / budget.budgetAmount).clamp(0.0, 2.0);
      budgetScore += adherence;
      budgetCount++;
    }
    if (budgetCount > 0) {
      final avgBudgetScore = (budgetScore / budgetCount) * 25;
      score += avgBudgetScore;
      factors['Budget Adherence'] = avgBudgetScore;
    }

    // Income stability (20% weight)
    final incomeStability = _calculateIncomeStability(transactions);
    final stabilityScore = incomeStability * 20;
    score += stabilityScore;
    factors['Income Stability'] = stabilityScore;

    // Spending consistency (15% weight)
    final spendingConsistency = _calculateSpendingConsistency(transactions);
    final consistencyScore = spendingConsistency * 15;
    score += consistencyScore;
    factors['Spending Consistency'] = consistencyScore;

    // Emergency fund (10% weight)
    // This would require additional data about savings/emergency fund
    final emergencyScore = 5.0; // Placeholder
    score += emergencyScore;
    factors['Emergency Fund'] = emergencyScore;

    return FinancialHealth(
      score: score.clamp(0.0, 100.0),
      grade: _getHealthGrade(score),
      factors: factors,
      recommendations: _getHealthRecommendations(score, factors),
      lastCalculated: DateTime.now(),
    );
  }

  /// Analyze spending patterns
  List<SpendingPattern> _analyzeSpendingPatterns(List<Transaction> transactions) {
    final patterns = <SpendingPattern>[];
    final categories = transactions.where((t) => t.type == TransactionType.expense).map((t) => t.category).toSet();

    for (final category in categories) {
      final categoryTransactions = transactions.where((t) =>
          t.category == category && t.type == TransactionType.expense).toList();
      
      if (categoryTransactions.length < 6) continue; // Need at least 6 data points
      
      // Group by month and calculate trend
      final monthlyTotals = <String, double>{};
      for (final transaction in categoryTransactions) {
        final key = '${transaction.date.year}-${transaction.date.month.toString().padLeft(2, '0')}';
        monthlyTotals[key] = (monthlyTotals[key] ?? 0) + transaction.amount;
      }
      
      final values = monthlyTotals.values.toList();
      final trend = _calculateTrend(values);
      
      patterns.add(SpendingPattern(
        category: category.name,
        trend: trend['direction'],
        changePercentage: trend['change'],
        confidence: trend['confidence'],
        monthlyAverages: values,
      ));
    }
    
    return patterns;
  }

  /// Calculate trend from values
  Map<String, dynamic> _calculateTrend(List<double> values) {
    if (values.length < 2) return {'direction': 'stable', 'change': 0.0, 'confidence': 0.0};
    
    // Simple linear regression
    final n = values.length;
    final x = List.generate(n, (i) => i.toDouble());
    final y = values;
    
    final sumX = x.reduce((a, b) => a + b);
    final sumY = y.reduce((a, b) => a + b);
    final sumXY = List.generate(n, (i) => x[i] * y[i]).reduce((a, b) => a + b);
    final sumXX = x.map((v) => v * v).reduce((a, b) => a + b);
    
    final slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    final avgY = sumY / n;
    final changePercentage = (slope / avgY) * 100;
    
    // Calculate R-squared for confidence
    final yMean = avgY;
    final ssRes = List.generate(n, (i) => pow(y[i] - (slope * x[i] + (yMean - slope * sumX / n)), 2)).reduce((a, b) => a + b);
    final ssTot = y.map((v) => pow(v - yMean, 2)).reduce((a, b) => a + b);
    final rSquared = 1 - (ssRes / ssTot);
    
    String direction;
    if (changePercentage.abs() < 5) {
      direction = 'stable';
    } else if (changePercentage > 0) {
      direction = 'increasing';
    } else {
      direction = 'decreasing';
    }
    
    return {
      'direction': direction,
      'change': changePercentage.abs(),
      'confidence': rSquared.clamp(0.0, 1.0),
    };
  }

  /// Calculate income stability
  double _calculateIncomeStability(List<Transaction> transactions) {
    final incomeTransactions = transactions.where((t) => t.type == 'income').toList();
    if (incomeTransactions.length < 3) return 0.5; // Default for insufficient data
    
    // Group by month
    final monthlyIncome = <String, double>{};
    for (final transaction in incomeTransactions) {
      final key = '${transaction.date.year}-${transaction.date.month.toString().padLeft(2, '0')}';
      monthlyIncome[key] = (monthlyIncome[key] ?? 0) + transaction.amount;
    }
    
    final values = monthlyIncome.values.toList();
    if (values.length < 2) return 0.5;
    
    final mean = values.reduce((a, b) => a + b) / values.length;
    final variance = values.map((v) => pow(v - mean, 2)).reduce((a, b) => a + b) / values.length;
    final coefficientOfVariation = sqrt(variance) / mean;
    
    // Lower coefficient of variation = higher stability
    return (1 - coefficientOfVariation.clamp(0.0, 1.0));
  }

  /// Calculate spending consistency
  double _calculateSpendingConsistency(List<Transaction> transactions) {
    final expenseTransactions = transactions.where((t) => t.type == TransactionType.expense).toList();
    if (expenseTransactions.length < 10) return 0.5; // Default for insufficient data
    
    // Group by month
    final monthlyExpenses = <String, double>{};
    for (final transaction in expenseTransactions) {
      final key = '${transaction.date.year}-${transaction.date.month.toString().padLeft(2, '0')}';
      monthlyExpenses[key] = (monthlyExpenses[key] ?? 0) + transaction.amount;
    }
    
    final values = monthlyExpenses.values.toList();
    if (values.length < 2) return 0.5;
    
    final mean = values.reduce((a, b) => a + b) / values.length;
    final variance = values.map((v) => pow(v - mean, 2)).reduce((a, b) => a + b) / values.length;
    final coefficientOfVariation = sqrt(variance) / mean;
    
    // Lower coefficient of variation = higher consistency
    return (1 - coefficientOfVariation.clamp(0.0, 1.0));
  }

  /// Get health grade from score
  String _getHealthGrade(double score) {
    if (score >= 90) return 'A+';
    if (score >= 80) return 'A';
    if (score >= 70) return 'B';
    if (score >= 60) return 'C';
    if (score >= 50) return 'D';
    return 'F';
  }

  /// Get health recommendations
  List<String> _getHealthRecommendations(double score, Map<String, double> factors) {
    final recommendations = <String>[];
    
    if (score < 70) {
      recommendations.add('Focus on improving your overall financial health');
    }
    
    if ((factors['Savings Rate'] ?? 0) < 15) {
      recommendations.add('Increase your savings rate to at least 20%');
    }
    
    if ((factors['Budget Adherence'] ?? 0) < 15) {
      recommendations.add('Stick to your budgets more consistently');
    }
    
    if ((factors['Income Stability'] ?? 0) < 10) {
      recommendations.add('Work on stabilizing your income sources');
    }
    
    if ((factors['Spending Consistency'] ?? 0) < 10) {
      recommendations.add('Try to maintain more consistent spending patterns');
    }
    
    return recommendations;
  }

  /// Format date for display
  String _formatDate(DateTime date) {
    final months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                   'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return '${months[date.month - 1]} ${date.day}';
  }

  /// Force refresh insights
  Future<void> refreshInsights() async {
    await _generateInsights();
  }

  /// Get insights by type
  List<AnalyticsInsight> getInsightsByType(InsightType type) {
    return _insights.where((insight) => insight.type == type).toList();
  }

  /// Get actionable insights
  List<AnalyticsInsight> getActionableInsights() {
    return _insights.where((insight) => insight.actionable).toList();
  }
}
