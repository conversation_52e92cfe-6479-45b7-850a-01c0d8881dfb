import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import '../models/country_flag.dart';
import '../theme/sophisticated_theme.dart';

class ThemeService extends GetxService {
  static ThemeService get to => Get.find();

  final GetStorage _storage = GetStorage();
  final Rx<CountryFlag> _selectedFlag = CountryFlag.defaultFlag.obs;
  final Rx<ThemeData> _currentTheme = SophisticatedTheme.lightTheme.obs;
  final RxBool _isDarkMode = false.obs;

  CountryFlag get selectedFlag => _selectedFlag.value;
  ThemeData get currentTheme => _currentTheme.value;
  bool get isDarkMode => _isDarkMode.value;

  static const String _flagCodeKey = 'selected_flag_code';
  static const String _darkModeKey = 'is_dark_mode';

  @override
  Future<void> onInit() async {
    super.onInit();
    await _loadSavedTheme();
  }

  Future<void> _loadSavedTheme() async {
    try {
      // Load saved flag
      final savedFlagCode = _storage.read(_flagCodeKey);
      if (savedFlagCode != null) {
        final flag = CountryFlag.getByCode(savedFlagCode);
        _selectedFlag.value = flag;
      }

      // Load dark mode preference
      _isDarkMode.value = _storage.read(_darkModeKey) ?? false;

      // Update theme
      _updateTheme();
    } catch (e) {
      print('Error loading saved theme: $e');
    }
  }

  Future<void> changeFlag(CountryFlag flag) async {
    try {
      _selectedFlag.value = flag;
      _updateTheme();
      
      // Save to storage
      _storage.write(_flagCodeKey, flag.code);
      
      Get.snackbar(
        'Theme Updated',
        'Theme changed to ${flag.name}',
        backgroundColor: flag.primary.withOpacity(0.9),
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
      );
    } catch (e) {
      print('Error changing flag: $e');
    }
  }

  Future<void> toggleDarkMode() async {
    try {
      _isDarkMode.value = !_isDarkMode.value;
      _updateTheme();
      
      // Save to storage
      _storage.write(_darkModeKey, _isDarkMode.value);
      
      Get.snackbar(
        'Theme Updated',
        '${_isDarkMode.value ? 'Dark' : 'Light'} mode enabled',
        backgroundColor: _selectedFlag.value.primary.withOpacity(0.9),
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
      );
    } catch (e) {
      print('Error toggling dark mode: $e');
    }
  }

  void _updateTheme() {
    _currentTheme.value = _isDarkMode.value
        ? SophisticatedTheme.darkTheme
        : SophisticatedTheme.lightTheme;
  }

  // Get theme colors for UI components
  Color get primaryColor => _selectedFlag.value.primary;
  Color get secondaryColor => _selectedFlag.value.secondary;
  Color get accentColor => _selectedFlag.value.accent;
  
  Color get surfaceColor => _isDarkMode.value 
      ? const Color(0xFF1E1E1E) 
      : Colors.white;
      
  Color get backgroundColor => _isDarkMode.value 
      ? const Color(0xFF121212) 
      : const Color(0xFFF5F5F5);
      
  Color get textPrimaryColor => _isDarkMode.value 
      ? Colors.white 
      : const Color(0xFF1A1A1A);
      
  Color get textSecondaryColor => _isDarkMode.value 
      ? Colors.white70 
      : const Color(0xFF666666);

  // Income/Expense colors that adapt to theme
  Color get incomeColor => _selectedFlag.value.colors.contains(const Color(0xFF4CAF50))
      ? const Color(0xFF4CAF50)
      : _selectedFlag.value.accent.computeLuminance() > 0.5
          ? const Color(0xFF2E7D32)
          : const Color(0xFF66BB6A);

  Color get expenseColor => _selectedFlag.value.colors.contains(const Color(0xFFE53935))
      ? const Color(0xFFE53935)
      : _selectedFlag.value.secondary.computeLuminance() > 0.5
          ? const Color(0xFFC62828)
          : const Color(0xFFEF5350);

  // Gradient colors based on flag
  LinearGradient get primaryGradient => LinearGradient(
    colors: [
      _selectedFlag.value.primary,
      _selectedFlag.value.primary.withOpacity(0.8),
    ],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  LinearGradient get secondaryGradient => LinearGradient(
    colors: [
      _selectedFlag.value.secondary,
      _selectedFlag.value.secondary.withOpacity(0.8),
    ],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  LinearGradient get accentGradient => LinearGradient(
    colors: [
      _selectedFlag.value.accent,
      _selectedFlag.value.accent.withOpacity(0.8),
    ],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // Flag-based card colors
  Color get cardColor => _isDarkMode.value
      ? _selectedFlag.value.primary.withOpacity(0.1)
      : _selectedFlag.value.primary.withOpacity(0.05);

  Color get borderColor => _isDarkMode.value
      ? _selectedFlag.value.primary.withOpacity(0.3)
      : _selectedFlag.value.primary.withOpacity(0.2);

  // Status colors
  Color get successColor => incomeColor;
  Color get errorColor => expenseColor;
  Color get warningColor => _selectedFlag.value.accent;
  Color get infoColor => _selectedFlag.value.primary;

  // Get contrasting text color for a background
  Color getContrastingTextColor(Color backgroundColor) {
    return backgroundColor.computeLuminance() > 0.5 
        ? Colors.black87 
        : Colors.white;
  }

  // Get flag emoji for display
  String get flagEmoji => _selectedFlag.value.emoji;
  
  // Get flag name for display
  String get flagName => _selectedFlag.value.name;

  // Reset to default theme
  Future<void> resetToDefault() async {
    try {
      await changeFlag(CountryFlag.defaultFlag);
      if (_isDarkMode.value) {
        await toggleDarkMode();
      }
    } catch (e) {
      print('Error resetting theme: $e');
    }
  }

  // Get all available flags
  List<CountryFlag> get availableFlags => CountryFlag.flags;

  // Check if current flag matches a specific country
  bool isFlag(String countryCode) => _selectedFlag.value.code == countryCode;

  // Get theme preview colors for settings
  Map<String, Color> get themePreview => {
    'primary': primaryColor,
    'secondary': secondaryColor,
    'accent': accentColor,
    'surface': surfaceColor,
    'background': backgroundColor,
  };
}
