import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'database_service.dart';
import 'account_service.dart';
import 'theme_service.dart';

class DataManagementService extends GetxService {
  static DataManagementService get to => Get.find();

  final RxBool _isClearing = false.obs;
  bool get isClearing => _isClearing.value;

  /// Clear all data for the current account
  Future<bool> clearCurrentAccountData() async {
    final accountId = AccountService.to.currentAccountId;
    if (accountId.isEmpty) return false;

    try {
      _isClearing.value = true;
      
      final database = DatabaseService.to.database;
      
      await database.transaction(() async {
        // Clear personal transactions
        await (database.delete(database.personalTransactions)
          ..where((tbl) => tbl.accountId.equals(accountId))).go();
        
        // Clear sync status
        await (database.delete(database.syncStatus)
          ..where((tbl) => tbl.accountId.equals(accountId))).go();
        
        // Reset account settings to defaults (but don't delete them)
        await database.setAccountSetting(accountId, 'data_cleared_at', DateTime.now().toIso8601String());
      });

      return true;
    } catch (e) {
      print('Error clearing account data: $e');
      return false;
    } finally {
      _isClearing.value = false;
    }
  }

  /// Clear all data for all accounts (complete reset)
  Future<bool> clearAllData() async {
    try {
      _isClearing.value = true;
      
      final database = DatabaseService.to.database;
      
      await database.transaction(() async {
        // Clear all transactions
        await database.delete(database.personalTransactions).go();
        
        // Clear all sync status
        await database.delete(database.syncStatus).go();
        
        // Clear all account settings
        await database.delete(database.accountSettings).go();
        
        // Clear all accounts except the current one
        final currentAccountId = AccountService.to.currentAccountId;
        if (currentAccountId.isNotEmpty) {
          await (database.delete(database.userAccounts)
            ..where((tbl) => tbl.id.isNotValue(currentAccountId))).go();
        }
      });

      return true;
    } catch (e) {
      print('Error clearing all data: $e');
      return false;
    } finally {
      _isClearing.value = false;
    }
  }

  /// Show confirmation dialog for clearing data
  Future<void> showClearDataDialog(BuildContext context, {bool allData = false}) async {
    final themeService = ThemeService.to;
    
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: themeService.surfaceColor,
          title: Text(
            allData ? 'Clear All Data' : 'Clear Account Data',
            style: TextStyle(color: themeService.textPrimaryColor),
          ),
          content: Text(
            allData 
                ? 'This will permanently delete ALL data from ALL accounts. This action cannot be undone.'
                : 'This will permanently delete all data for the current account. This action cannot be undone.',
            style: TextStyle(color: themeService.textSecondaryColor),
          ),
          actions: <Widget>[
            TextButton(
              child: Text(
                'Cancel',
                style: TextStyle(color: themeService.textSecondaryColor),
              ),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: Text(
                'Clear Data',
                style: TextStyle(color: Colors.red),
              ),
              onPressed: () async {
                Navigator.of(context).pop();
                
                final success = allData 
                    ? await clearAllData()
                    : await clearCurrentAccountData();
                
                if (success) {
                  Get.snackbar(
                    'Success',
                    allData ? 'All data cleared successfully' : 'Account data cleared successfully',
                    backgroundColor: Colors.green,
                    colorText: Colors.white,
                  );
                } else {
                  Get.snackbar(
                    'Error',
                    'Failed to clear data',
                    backgroundColor: Colors.red,
                    colorText: Colors.white,
                  );
                }
              },
            ),
          ],
        );
      },
    );
  }

  /// Export account data (placeholder for future implementation)
  Future<bool> exportAccountData() async {
    // TODO: Implement data export functionality
    Get.snackbar(
      'Coming Soon',
      'Data export feature will be available soon',
      backgroundColor: Colors.blue,
      colorText: Colors.white,
    );
    return false;
  }

  /// Import account data (placeholder for future implementation)
  Future<bool> importAccountData() async {
    // TODO: Implement data import functionality
    Get.snackbar(
      'Coming Soon',
      'Data import feature will be available soon',
      backgroundColor: Colors.blue,
      colorText: Colors.white,
    );
    return false;
  }

  /// Get data statistics for current account
  Future<Map<String, int>> getDataStatistics() async {
    final accountId = AccountService.to.currentAccountId;
    if (accountId.isEmpty) return {};

    try {
      final database = DatabaseService.to.database;
      
      // Count transactions
      final transactionCount = await (database.select(database.personalTransactions)
        ..where((tbl) => tbl.accountId.equals(accountId))).get();
      
      // Count settings
      final settingsCount = await (database.select(database.accountSettings)
        ..where((tbl) => tbl.accountId.equals(accountId))).get();

      return {
        'transactions': transactionCount.length,
        'settings': settingsCount.length,
      };
    } catch (e) {
      print('Error getting data statistics: $e');
      return {};
    }
  }
}
