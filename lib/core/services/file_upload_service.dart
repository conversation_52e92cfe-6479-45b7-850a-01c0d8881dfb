import 'dart:io';
import 'package:get/get.dart';
import 'package:appwrite/appwrite.dart';
import 'package:appwrite/models.dart' as models;
import 'package:image_picker/image_picker.dart';
import '../config/appwrite_config.dart';
import 'appwrite_service.dart';
import 'auth_service.dart';

class FileUploadService extends GetxService {
  static FileUploadService get to => Get.find();

  final AppwriteService _appwriteService = AppwriteService.to;
  final AuthService _authService = AuthService.to;

  final RxBool _isUploading = false.obs;
  final RxDouble _uploadProgress = 0.0.obs;

  bool get isUploading => _isUploading.value;
  double get uploadProgress => _uploadProgress.value;

  /// Upload a file to Appwrite Storage
  Future<String?> uploadFile({
    required File file,
    required String fileName,
    String? folder,
    Function(double)? onProgress,
  }) async {
    if (!_authService.isAuthenticated || !_appwriteService.isOnline) {
      return null;
    }

    try {
      _isUploading.value = true;
      _uploadProgress.value = 0.0;

      // Validate file size
      final fileSize = await file.length();
      if (fileSize > AppwriteConfig.maxFileSize) {
        Get.snackbar(
          'File Too Large',
          'File size exceeds ${AppwriteConfig.maxFileSize ~/ (1024 * 1024)}MB limit',
          snackPosition: SnackPosition.BOTTOM,
        );
        return null;
      }

      // Create unique file ID
      final fileId = '${DateTime.now().millisecondsSinceEpoch}_$fileName';

      // Read file as bytes
      final bytes = await file.readAsBytes();

      // Upload file to Appwrite Storage
      final uploadedFile = await _appwriteService.storage.createFile(
        bucketId: AppwriteConfig.storageId,
        fileId: fileId,
        file: InputFile.fromBytes(
          bytes: bytes,
          filename: fileName,
        ),
      );

      _uploadProgress.value = 1.0;

      // Return the file URL
      final fileUrl = _getFileUrl(uploadedFile.$id);
      
      print('File uploaded successfully: $fileUrl');
      return fileUrl;

    } catch (e) {
      print('Error uploading file: $e');
      Get.snackbar(
        'Upload Failed',
        'Failed to upload file: ${_appwriteService.getErrorMessage(e)}',
        snackPosition: SnackPosition.BOTTOM,
      );
      return null;
    } finally {
      _isUploading.value = false;
      _uploadProgress.value = 0.0;
    }
  }

  /// Upload image from camera or gallery
  Future<String?> uploadImage({
    required ImageSource source,
    String? folder,
    int? imageQuality,
  }) async {
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(
        source: source,
        imageQuality: imageQuality ?? 80,
      );

      if (pickedFile == null) return null;

      final file = File(pickedFile.path);
      final fileName = 'image_${DateTime.now().millisecondsSinceEpoch}.jpg';

      return await uploadFile(
        file: file,
        fileName: fileName,
        folder: folder,
      );

    } catch (e) {
      print('Error picking/uploading image: $e');
      Get.snackbar(
        'Image Upload Failed',
        'Failed to upload image: $e',
        snackPosition: SnackPosition.BOTTOM,
      );
      return null;
    }
  }

  /// Upload receipt image
  Future<String?> uploadReceiptImage({
    required ImageSource source,
  }) async {
    return await uploadImage(
      source: source,
      folder: 'receipts',
      imageQuality: 85,
    );
  }

  /// Upload profile picture
  Future<String?> uploadProfilePicture({
    required ImageSource source,
  }) async {
    return await uploadImage(
      source: source,
      folder: 'profiles',
      imageQuality: 90,
    );
  }

  /// Delete a file from storage
  Future<bool> deleteFile(String fileId) async {
    try {
      await _appwriteService.storage.deleteFile(
        bucketId: AppwriteConfig.storageId,
        fileId: fileId,
      );
      return true;
    } catch (e) {
      print('Error deleting file: $e');
      return false;
    }
  }

  /// Get file URL for viewing/downloading
  String _getFileUrl(String fileId) {
    return '${AppwriteConfig.endpoint}/storage/buckets/${AppwriteConfig.storageId}/files/$fileId/view?project=${AppwriteConfig.projectId}';
  }

  /// Get file download URL
  String getFileDownloadUrl(String fileId) {
    return '${AppwriteConfig.endpoint}/storage/buckets/${AppwriteConfig.storageId}/files/$fileId/download?project=${AppwriteConfig.projectId}';
  }

  /// Get file preview URL with dimensions
  String getFilePreviewUrl(String fileId, {int? width, int? height}) {
    String url = '${AppwriteConfig.endpoint}/storage/buckets/${AppwriteConfig.storageId}/files/$fileId/preview?project=${AppwriteConfig.projectId}';
    
    if (width != null) url += '&width=$width';
    if (height != null) url += '&height=$height';
    
    return url;
  }

  /// Validate file type
  bool _isValidImageType(String mimeType) {
    return AppwriteConfig.allowedImageTypes.contains(mimeType.toLowerCase());
  }

  /// Get file info
  Future<models.File?> getFileInfo(String fileId) async {
    try {
      return await _appwriteService.storage.getFile(
        bucketId: AppwriteConfig.storageId,
        fileId: fileId,
      );
    } catch (e) {
      print('Error getting file info: $e');
      return null;
    }
  }

  /// List files in a folder
  Future<models.FileList?> listFiles({
    String? search,
    int? limit,
    int? offset,
  }) async {
    try {
      return await _appwriteService.storage.listFiles(
        bucketId: AppwriteConfig.storageId,
        queries: [
          if (search != null) Query.search('name', search),
          if (limit != null) Query.limit(limit),
          if (offset != null) Query.offset(offset),
        ],
      );
    } catch (e) {
      print('Error listing files: $e');
      return null;
    }
  }

  /// Compress image before upload
  Future<File?> _compressImage(File file, {int quality = 80}) async {
    try {
      // This is a placeholder for image compression
      // In a real app, you'd use a package like flutter_image_compress
      return file;
    } catch (e) {
      print('Error compressing image: $e');
      return file;
    }
  }

  /// Upload multiple files
  Future<List<String>> uploadMultipleFiles({
    required List<File> files,
    String? folder,
    Function(int, int)? onProgress,
  }) async {
    final uploadedUrls = <String>[];

    for (int i = 0; i < files.length; i++) {
      final file = files[i];
      final fileName = 'file_${DateTime.now().millisecondsSinceEpoch}_$i.${file.path.split('.').last}';
      
      final url = await uploadFile(
        file: file,
        fileName: fileName,
        folder: folder,
      );

      if (url != null) {
        uploadedUrls.add(url);
      }

      onProgress?.call(i + 1, files.length);
    }

    return uploadedUrls;
  }

  /// Show image picker options (to be used by UI components)
  Future<String?> pickAndUploadImage({
    required ImageSource source,
    String? folder,
  }) async {
    return await uploadImage(
      source: source,
      folder: folder,
    );
  }
}
