import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:appwrite/appwrite.dart';
import '../config/appwrite_config.dart';

enum ErrorType {
  network,
  authentication,
  validation,
  server,
  storage,
  permission,
  unknown,
}

class AppError {
  final ErrorType type;
  final String message;
  final String? details;
  final int? code;
  final DateTime timestamp;

  AppError({
    required this.type,
    required this.message,
    this.details,
    this.code,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  @override
  String toString() {
    return 'AppError(type: $type, message: $message, code: $code, details: $details)';
  }
}

class ErrorHandlingService extends GetxService {
  static ErrorHandlingService get to => Get.find();

  final RxList<AppError> _errorHistory = <AppError>[].obs;
  final RxBool _showErrorDialog = true.obs;

  List<AppError> get errorHistory => _errorHistory;
  bool get shouldShowErrorDialog => _showErrorDialog.value;

  /// Handle any error and convert it to AppError
  AppError handleError(dynamic error, {String? context}) {
    final appError = _convertToAppError(error, context: context);
    _logError(appError);
    _showErrorToUser(appError);
    return appError;
  }

  /// Convert various error types to AppError
  AppError _convertToAppError(dynamic error, {String? context}) {
    if (error is AppwriteException) {
      return _handleAppwriteError(error, context: context);
    } else if (error is Exception) {
      return _handleGenericException(error, context: context);
    } else {
      return AppError(
        type: ErrorType.unknown,
        message: error.toString(),
        details: context,
      );
    }
  }

  /// Handle Appwrite specific errors
  AppError _handleAppwriteError(AppwriteException error, {String? context}) {
    ErrorType type;
    String message;

    switch (error.code) {
      case 400:
        type = ErrorType.validation;
        message = 'Invalid request. Please check your input.';
        break;
      case 401:
        type = ErrorType.authentication;
        message = AppwriteConfig.authErrorMessage;
        break;
      case 403:
        type = ErrorType.permission;
        message = 'You don\'t have permission to perform this action.';
        break;
      case 404:
        type = ErrorType.validation;
        message = 'The requested resource was not found.';
        break;
      case 409:
        type = ErrorType.validation;
        message = 'A conflict occurred. The resource may already exist.';
        break;
      case 429:
        type = ErrorType.server;
        message = 'Too many requests. Please try again later.';
        break;
      case 500:
      case 502:
      case 503:
        type = ErrorType.server;
        message = AppwriteConfig.serverErrorMessage;
        break;
      default:
        type = ErrorType.unknown;
        message = error.message ?? 'An unknown error occurred.';
    }

    return AppError(
      type: type,
      message: message,
      details: context,
      code: error.code,
    );
  }

  /// Handle generic exceptions
  AppError _handleGenericException(Exception error, {String? context}) {
    String message = error.toString();
    ErrorType type = ErrorType.unknown;

    // Check for common network errors
    if (message.contains('SocketException') || 
        message.contains('NetworkException') ||
        message.contains('Connection failed')) {
      type = ErrorType.network;
      message = AppwriteConfig.networkErrorMessage;
    } else if (message.contains('FormatException')) {
      type = ErrorType.validation;
      message = 'Invalid data format received.';
    } else if (message.contains('TimeoutException')) {
      type = ErrorType.network;
      message = 'Request timed out. Please try again.';
    }

    return AppError(
      type: type,
      message: message,
      details: context,
    );
  }

  /// Log error for debugging and analytics
  void _logError(AppError error) {
    print('ERROR: ${error.toString()}');
    
    // Add to error history (keep last 50 errors)
    _errorHistory.add(error);
    if (_errorHistory.length > 50) {
      _errorHistory.removeAt(0);
    }

    // In production, you might want to send errors to a logging service
    // like Firebase Crashlytics, Sentry, etc.
  }

  /// Show error to user via snackbar or dialog
  void _showErrorToUser(AppError error) {
    if (!_showErrorDialog.value) return;

    Color backgroundColor;
    IconData icon;

    switch (error.type) {
      case ErrorType.network:
        backgroundColor = Colors.orange;
        icon = Icons.wifi_off;
        break;
      case ErrorType.authentication:
        backgroundColor = Colors.red;
        icon = Icons.lock;
        break;
      case ErrorType.validation:
        backgroundColor = Colors.amber;
        icon = Icons.warning;
        break;
      case ErrorType.server:
        backgroundColor = Colors.red;
        icon = Icons.error;
        break;
      case ErrorType.storage:
        backgroundColor = Colors.blue;
        icon = Icons.storage;
        break;
      case ErrorType.permission:
        backgroundColor = Colors.purple;
        icon = Icons.security;
        break;
      default:
        backgroundColor = Colors.grey;
        icon = Icons.error_outline;
    }

    Get.snackbar(
      _getErrorTitle(error.type),
      error.message,
      backgroundColor: backgroundColor,
      colorText: Colors.white,
      icon: Icon(icon, color: Colors.white),
      duration: const Duration(seconds: 4),
      snackPosition: SnackPosition.BOTTOM,
      margin: const EdgeInsets.all(16),
      borderRadius: 8,
    );
  }

  /// Get appropriate title for error type
  String _getErrorTitle(ErrorType type) {
    switch (type) {
      case ErrorType.network:
        return 'Connection Error';
      case ErrorType.authentication:
        return 'Authentication Error';
      case ErrorType.validation:
        return 'Validation Error';
      case ErrorType.server:
        return 'Server Error';
      case ErrorType.storage:
        return 'Storage Error';
      case ErrorType.permission:
        return 'Permission Error';
      default:
        return 'Error';
    }
  }

  /// Show detailed error dialog
  void showErrorDialog(AppError error) {
    Get.dialog(
      AlertDialog(
        title: Row(
          children: [
            Icon(Icons.error, color: Colors.red),
            const SizedBox(width: 8),
            Text(_getErrorTitle(error.type)),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(error.message),
            if (error.details != null) ...[
              const SizedBox(height: 8),
              Text(
                'Details: ${error.details}',
                style: const TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ],
            if (error.code != null) ...[
              const SizedBox(height: 4),
              Text(
                'Error Code: ${error.code}',
                style: const TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Clear error history
  void clearErrorHistory() {
    _errorHistory.clear();
  }

  /// Toggle error dialog display
  void toggleErrorDialog(bool show) {
    _showErrorDialog.value = show;
  }

  /// Get errors by type
  List<AppError> getErrorsByType(ErrorType type) {
    return _errorHistory.where((error) => error.type == type).toList();
  }

  /// Get recent errors (last n errors)
  List<AppError> getRecentErrors({int count = 10}) {
    final errors = _errorHistory.toList();
    errors.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    return errors.take(count).toList();
  }

  /// Check if there are critical errors
  bool hasCriticalErrors() {
    return _errorHistory.any((error) => 
      error.type == ErrorType.authentication || 
      error.type == ErrorType.server
    );
  }

  /// Retry mechanism wrapper
  Future<T> retryOperation<T>(
    Future<T> Function() operation, {
    int maxRetries = 3,
    Duration delay = const Duration(seconds: 1),
    String? context,
  }) async {
    int attempts = 0;
    
    while (attempts < maxRetries) {
      try {
        return await operation();
      } catch (e) {
        attempts++;
        
        if (attempts >= maxRetries) {
          handleError(e, context: context);
          rethrow;
        }
        
        print('Operation failed (attempt $attempts/$maxRetries): $e');
        await Future.delayed(delay * attempts);
      }
    }
    
    throw Exception('Max retry attempts reached');
  }
}
