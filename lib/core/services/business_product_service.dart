import 'package:get/get.dart';
import '../models/business_product.dart';

class BusinessProductService extends GetxService {
  final RxList<BusinessProduct> _products = <BusinessProduct>[].obs;
  final RxList<BusinessTransaction> _transactions = <BusinessTransaction>[].obs;
  final RxString _currentAccountId = ''.obs;

  List<BusinessProduct> get products => _products;
  List<BusinessTransaction> get transactions => _transactions;
  String get currentAccountId => _currentAccountId.value;

  // Product Management
  Future<void> addProduct(BusinessProduct product) async {
    _products.add(product);
    _products.refresh();
  }

  Future<void> updateProduct(BusinessProduct product) async {
    final index = _products.indexWhere((p) => p.id == product.id);
    if (index != -1) {
      _products[index] = product;
      _products.refresh();
    }
  }

  Future<void> deleteProduct(String productId) async {
    _products.removeWhere((p) => p.id == productId);
    _products.refresh();
  }

  BusinessProduct? getProduct(String productId) {
    try {
      return _products.firstWhere((p) => p.id == productId);
    } catch (e) {
      return null;
    }
  }

  List<BusinessProduct> getProductsByType(ProductType type) {
    return _products.where((p) => p.type == type).toList();
  }

  List<BusinessProduct> getActiveProducts() {
    return _products.where((p) => p.status == ProductStatus.active).toList();
  }

  List<BusinessProduct> getLowStockProducts() {
    return _products.where((p) => p.isLowStock).toList();
  }

  List<BusinessProduct> getOutOfStockProducts() {
    return _products.where((p) => p.status == ProductStatus.outOfStock).toList();
  }

  // Inventory Management
  Future<void> updateStock(String productId, int newQuantity) async {
    final product = getProduct(productId);
    if (product != null && product.isProduct) {
      final updatedProduct = product.copyWith(
        quantity: newQuantity,
        status: newQuantity <= 0 ? ProductStatus.outOfStock : ProductStatus.active,
        updatedAt: DateTime.now(),
      );
      await updateProduct(updatedProduct);
    }
  }

  Future<void> adjustStock(String productId, int adjustment) async {
    final product = getProduct(productId);
    if (product != null && product.isProduct) {
      final newQuantity = (product.quantity ?? 0) + adjustment;
      await updateStock(productId, newQuantity);
    }
  }

  // Transaction Management
  Future<void> addTransaction(BusinessTransaction transaction) async {
    _transactions.add(transaction);
    _transactions.refresh();

    // Update inventory for product transactions
    if (transaction.product.isProduct) {
      if (transaction.isSale) {
        // Reduce stock for sales
        await adjustStock(transaction.productId, -transaction.quantity);
      } else if (transaction.isPurchase) {
        // Increase stock for purchases
        await adjustStock(transaction.productId, transaction.quantity);
      }
    }
  }

  Future<void> updateTransaction(BusinessTransaction transaction) async {
    final index = _transactions.indexWhere((t) => t.id == transaction.id);
    if (index != -1) {
      final oldTransaction = _transactions[index];
      _transactions[index] = transaction;
      _transactions.refresh();

      // Adjust inventory based on transaction changes
      if (transaction.product.isProduct) {
        // Reverse old transaction effect
        if (oldTransaction.isSale) {
          await adjustStock(transaction.productId, oldTransaction.quantity);
        } else if (oldTransaction.isPurchase) {
          await adjustStock(transaction.productId, -oldTransaction.quantity);
        }

        // Apply new transaction effect
        if (transaction.isSale) {
          await adjustStock(transaction.productId, -transaction.quantity);
        } else if (transaction.isPurchase) {
          await adjustStock(transaction.productId, transaction.quantity);
        }
      }
    }
  }

  Future<void> deleteTransaction(String transactionId) async {
    final transaction = _transactions.firstWhere((t) => t.id == transactionId);
    _transactions.removeWhere((t) => t.id == transactionId);
    _transactions.refresh();

    // Reverse transaction effect on inventory
    if (transaction.product.isProduct) {
      if (transaction.isSale) {
        await adjustStock(transaction.productId, transaction.quantity);
      } else if (transaction.isPurchase) {
        await adjustStock(transaction.productId, -transaction.quantity);
      }
    }
  }

  List<BusinessTransaction> getTransactionsByProduct(String productId) {
    return _transactions.where((t) => t.productId == productId).toList();
  }

  List<BusinessTransaction> getTransactionsByType(BusinessTransactionType type) {
    return _transactions.where((t) => t.type == type).toList();
  }

  List<BusinessTransaction> getSales() {
    return getTransactionsByType(BusinessTransactionType.sale);
  }

  List<BusinessTransaction> getPurchases() {
    return getTransactionsByType(BusinessTransactionType.purchase);
  }

  // Analytics
  double getTotalSales({DateTime? startDate, DateTime? endDate}) {
    var sales = getSales();
    
    if (startDate != null) {
      sales = sales.where((s) => s.transactionDate.isAfter(startDate)).toList();
    }
    
    if (endDate != null) {
      sales = sales.where((s) => s.transactionDate.isBefore(endDate)).toList();
    }
    
    return sales.fold(0.0, (sum, sale) => sum + sale.totalAmount);
  }

  double getTotalPurchases({DateTime? startDate, DateTime? endDate}) {
    var purchases = getPurchases();
    
    if (startDate != null) {
      purchases = purchases.where((p) => p.transactionDate.isAfter(startDate)).toList();
    }
    
    if (endDate != null) {
      purchases = purchases.where((p) => p.transactionDate.isBefore(endDate)).toList();
    }
    
    return purchases.fold(0.0, (sum, purchase) => sum + purchase.totalAmount);
  }

  double getProfit({DateTime? startDate, DateTime? endDate}) {
    return getTotalSales(startDate: startDate, endDate: endDate) - 
           getTotalPurchases(startDate: startDate, endDate: endDate);
  }

  Map<String, double> getTopSellingProducts({int limit = 5}) {
    final salesByProduct = <String, double>{};
    
    for (final sale in getSales()) {
      final productName = sale.product.name;
      salesByProduct[productName] = (salesByProduct[productName] ?? 0) + sale.totalAmount;
    }
    
    final sortedEntries = salesByProduct.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return Map.fromEntries(sortedEntries.take(limit));
  }

  int getTotalInventoryValue() {
    return _products
        .where((p) => p.isProduct && p.quantity != null)
        .fold(0, (sum, product) => sum + (product.quantity! * product.price).round());
  }

  // Search and Filter
  List<BusinessProduct> searchProducts(String query) {
    final lowercaseQuery = query.toLowerCase();
    return _products.where((p) => 
      p.name.toLowerCase().contains(lowercaseQuery) ||
      (p.description?.toLowerCase().contains(lowercaseQuery) ?? false) ||
      (p.sku?.toLowerCase().contains(lowercaseQuery) ?? false) ||
      (p.category?.toLowerCase().contains(lowercaseQuery) ?? false)
    ).toList();
  }

  List<BusinessProduct> filterProducts({
    ProductType? type,
    ProductStatus? status,
    String? category,
  }) {
    var filtered = _products.toList();
    
    if (type != null) {
      filtered = filtered.where((p) => p.type == type).toList();
    }
    
    if (status != null) {
      filtered = filtered.where((p) => p.status == status).toList();
    }
    
    if (category != null) {
      filtered = filtered.where((p) => p.category == category).toList();
    }
    
    return filtered;
  }

  // Analytics Methods
  List<Map<String, dynamic>> getHotSellingProducts({int limit = 10, int? days}) {
    final cutoffDate = days != null
        ? DateTime.now().subtract(Duration(days: days))
        : null;

    final salesTransactions = _transactions.where((t) =>
        t.isSale &&
        (cutoffDate == null || t.transactionDate.isAfter(cutoffDate))
    ).toList();

    final productSales = <String, Map<String, dynamic>>{};

    for (final transaction in salesTransactions) {
      final productId = transaction.productId;
      if (!productSales.containsKey(productId)) {
        productSales[productId] = {
          'product': transaction.product,
          'totalQuantity': 0,
          'totalRevenue': 0.0,
          'transactionCount': 0,
        };
      }

      productSales[productId]!['totalQuantity'] += transaction.quantity;
      productSales[productId]!['totalRevenue'] += transaction.totalAmount;
      productSales[productId]!['transactionCount'] += 1;
    }

    final sortedProducts = productSales.values.toList()
      ..sort((a, b) => (b['totalQuantity'] as int).compareTo(a['totalQuantity'] as int));

    return sortedProducts.take(limit).toList();
  }

  List<Map<String, dynamic>> getTopRevenueProducts({int limit = 10, int? days}) {
    final cutoffDate = days != null
        ? DateTime.now().subtract(Duration(days: days))
        : null;

    final salesTransactions = _transactions.where((t) =>
        t.isSale &&
        (cutoffDate == null || t.transactionDate.isAfter(cutoffDate))
    ).toList();

    final productRevenue = <String, Map<String, dynamic>>{};

    for (final transaction in salesTransactions) {
      final productId = transaction.productId;
      if (!productRevenue.containsKey(productId)) {
        productRevenue[productId] = {
          'product': transaction.product,
          'totalRevenue': 0.0,
          'totalQuantity': 0,
          'transactionCount': 0,
        };
      }

      productRevenue[productId]!['totalRevenue'] += transaction.totalAmount;
      productRevenue[productId]!['totalQuantity'] += transaction.quantity;
      productRevenue[productId]!['transactionCount'] += 1;
    }

    final sortedProducts = productRevenue.values.toList()
      ..sort((a, b) => (b['totalRevenue'] as double).compareTo(a['totalRevenue'] as double));

    return sortedProducts.take(limit).toList();
  }

  Map<String, dynamic> getProductAnalytics(String productId, {int? days}) {
    final cutoffDate = days != null
        ? DateTime.now().subtract(Duration(days: days))
        : null;

    final productTransactions = _transactions.where((t) =>
        t.productId == productId &&
        (cutoffDate == null || t.transactionDate.isAfter(cutoffDate))
    ).toList();

    final sales = productTransactions.where((t) => t.isSale).toList();
    final purchases = productTransactions.where((t) => t.isPurchase).toList();

    final totalSalesQuantity = sales.fold<int>(0, (sum, t) => sum + t.quantity);
    final totalSalesRevenue = sales.fold<double>(0, (sum, t) => sum + t.totalAmount);
    final totalPurchasesQuantity = purchases.fold<int>(0, (sum, t) => sum + t.quantity);
    final totalPurchasesCost = purchases.fold<double>(0, (sum, t) => sum + t.totalAmount);

    return {
      'productId': productId,
      'product': getProduct(productId),
      'salesCount': sales.length,
      'purchasesCount': purchases.length,
      'totalSalesQuantity': totalSalesQuantity,
      'totalSalesRevenue': totalSalesRevenue,
      'totalPurchasesQuantity': totalPurchasesQuantity,
      'totalPurchasesCost': totalPurchasesCost,
      'profit': totalSalesRevenue - totalPurchasesCost,
      'averageSalePrice': sales.isNotEmpty ? totalSalesRevenue / totalSalesQuantity : 0.0,
      'averagePurchasePrice': purchases.isNotEmpty ? totalPurchasesCost / totalPurchasesQuantity : 0.0,
    };
  }

  Map<String, dynamic> getOverallAnalytics({int? days}) {
    final cutoffDate = days != null
        ? DateTime.now().subtract(Duration(days: days))
        : null;

    final filteredTransactions = _transactions.where((t) =>
        cutoffDate == null || t.transactionDate.isAfter(cutoffDate)
    ).toList();

    final sales = filteredTransactions.where((t) => t.isSale).toList();
    final purchases = filteredTransactions.where((t) => t.isPurchase).toList();

    final totalSalesRevenue = sales.fold<double>(0, (sum, t) => sum + t.totalAmount);
    final totalPurchasesCost = purchases.fold<double>(0, (sum, t) => sum + t.totalAmount);

    return {
      'totalSales': sales.length,
      'totalPurchases': purchases.length,
      'totalSalesRevenue': totalSalesRevenue,
      'totalPurchasesCost': totalPurchasesCost,
      'grossProfit': totalSalesRevenue - totalPurchasesCost,
      'averageSaleValue': sales.isNotEmpty ? totalSalesRevenue / sales.length : 0.0,
      'averagePurchaseValue': purchases.isNotEmpty ? totalPurchasesCost / purchases.length : 0.0,
      'uniqueProductsSold': sales.map((t) => t.productId).toSet().length,
      'uniqueProductsPurchased': purchases.map((t) => t.productId).toSet().length,
    };
  }

  // Utility Methods
  void setCurrentAccount(String accountId) {
    _currentAccountId.value = accountId;
    // In a real app, you would load products for this account from database
    _loadProductsForAccount(accountId);
  }

  Future<void> _loadProductsForAccount(String accountId) async {
    // TODO: Load from database
    // For now, we'll use sample data
    _products.clear();
    _transactions.clear();
  }

  List<String> getCategories() {
    return _products
        .where((p) => p.category != null)
        .map((p) => p.category!)
        .toSet()
        .toList();
  }

  void clearData() {
    _products.clear();
    _transactions.clear();
  }
}
