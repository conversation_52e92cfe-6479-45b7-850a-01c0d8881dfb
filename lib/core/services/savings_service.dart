import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import '../models/savings_account.dart';
import 'account_service.dart';

class SavingsService extends GetxService {
  static SavingsService get to => Get.find();

  final GetStorage _storage = GetStorage();
  final AccountService _accountService = Get.find();

  final RxList<SavingsAccount> _savingsAccounts = <SavingsAccount>[].obs;
  final RxList<SavingsTransaction> _savingsTransactions = <SavingsTransaction>[].obs;
  final RxBool _isLoading = false.obs;

  List<SavingsAccount> get savingsAccounts => _savingsAccounts;
  List<SavingsTransaction> get savingsTransactions => _savingsTransactions;
  bool get isLoading => _isLoading.value;

  String get currentAccountId => _accountService.currentAccount?.id ?? '';

  @override
  Future<void> onInit() async {
    super.onInit();
    await _loadSavingsData();
  }

  Future<void> _loadSavingsData() async {
    try {
      _isLoading.value = true;
      
      // Load savings accounts
      final savingsData = _storage.read('savings_accounts_${currentAccountId}') as List?;
      if (savingsData != null) {
        _savingsAccounts.value = savingsData
            .map((data) => SavingsAccount.fromJson(Map<String, dynamic>.from(data)))
            .toList();
      }

      // Load savings transactions
      final transactionsData = _storage.read('savings_transactions_${currentAccountId}') as List?;
      if (transactionsData != null) {
        _savingsTransactions.value = transactionsData
            .map((data) => SavingsTransaction.fromJson(Map<String, dynamic>.from(data)))
            .toList();
      }
    } catch (e) {
      print('Error loading savings data: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  Future<void> _saveSavingsAccounts() async {
    try {
      final data = _savingsAccounts.map((account) => account.toJson()).toList();
      await _storage.write('savings_accounts_${currentAccountId}', data);
    } catch (e) {
      print('Error saving savings accounts: $e');
    }
  }

  Future<void> _saveSavingsTransactions() async {
    try {
      final data = _savingsTransactions.map((transaction) => transaction.toJson()).toList();
      await _storage.write('savings_transactions_${currentAccountId}', data);
    } catch (e) {
      print('Error saving savings transactions: $e');
    }
  }

  Future<bool> createSavingsAccount({
    required String name,
    String? description,
    required SavingsType type,
    required double targetAmount,
    required DateTime targetDate,
    required PaymentMethod paymentMethod,
    String? paymentMethodDescription,
    CryptoProvider? cryptoProvider,
    String? cryptoWalletAddress,
  }) async {
    try {
      final savingsAccount = SavingsAccount(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        accountId: currentAccountId,
        name: name,
        description: description,
        type: type,
        targetAmount: targetAmount,
        currentAmount: 0.0,
        targetDate: targetDate,
        paymentMethod: paymentMethod,
        paymentMethodDescription: paymentMethodDescription,
        cryptoProvider: cryptoProvider,
        cryptoWalletAddress: cryptoWalletAddress,
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      _savingsAccounts.add(savingsAccount);
      await _saveSavingsAccounts();

      Get.snackbar(
        'Success',
        'Savings account created successfully',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: const Color(0xFF5CC9B6),
        colorText: Colors.white,
      );

      return true;
    } catch (e) {
      print('Error creating savings account: $e');
      Get.snackbar(
        'Error',
        'Failed to create savings account',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: const Color(0xFFFF7F6F),
        colorText: Colors.white,
      );
      return false;
    }
  }

  Future<bool> updateSavingsAccount(SavingsAccount updatedAccount) async {
    try {
      final index = _savingsAccounts.indexWhere((account) => account.id == updatedAccount.id);
      if (index == -1) return false;

      _savingsAccounts[index] = updatedAccount.copyWith(updatedAt: DateTime.now());
      await _saveSavingsAccounts();

      Get.snackbar(
        'Success',
        'Savings account updated successfully',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: const Color(0xFF5CC9B6),
        colorText: Colors.white,
      );

      return true;
    } catch (e) {
      print('Error updating savings account: $e');
      return false;
    }
  }

  Future<bool> deleteSavingsAccount(String accountId) async {
    try {
      _savingsAccounts.removeWhere((account) => account.id == accountId);
      _savingsTransactions.removeWhere((transaction) => transaction.savingsAccountId == accountId);
      
      await _saveSavingsAccounts();
      await _saveSavingsTransactions();

      Get.snackbar(
        'Success',
        'Savings account deleted successfully',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: const Color(0xFF5CC9B6),
        colorText: Colors.white,
      );

      return true;
    } catch (e) {
      print('Error deleting savings account: $e');
      return false;
    }
  }

  Future<bool> addSavingsTransaction({
    required String savingsAccountId,
    required double amount,
    String? description,
    required bool isDeposit,
  }) async {
    try {
      final transaction = SavingsTransaction(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        savingsAccountId: savingsAccountId,
        amount: amount,
        description: description,
        isDeposit: isDeposit,
        createdAt: DateTime.now(),
      );

      _savingsTransactions.add(transaction);

      // Update savings account balance
      final accountIndex = _savingsAccounts.indexWhere((account) => account.id == savingsAccountId);
      if (accountIndex != -1) {
        final account = _savingsAccounts[accountIndex];
        final newAmount = isDeposit 
            ? account.currentAmount + amount
            : account.currentAmount - amount;
        
        _savingsAccounts[accountIndex] = account.copyWith(
          currentAmount: newAmount.clamp(0.0, double.infinity),
          updatedAt: DateTime.now(),
        );
      }

      await _saveSavingsTransactions();
      await _saveSavingsAccounts();

      Get.snackbar(
        'Success',
        isDeposit ? 'Deposit added successfully' : 'Withdrawal recorded successfully',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: const Color(0xFF5CC9B6),
        colorText: Colors.white,
      );

      return true;
    } catch (e) {
      print('Error adding savings transaction: $e');
      return false;
    }
  }

  List<SavingsAccount> getSavingsAccountsByType(SavingsType type) {
    return _savingsAccounts.where((account) => account.type == type && account.isActive).toList();
  }

  List<SavingsTransaction> getTransactionsForAccount(String accountId) {
    return _savingsTransactions
        .where((transaction) => transaction.savingsAccountId == accountId)
        .toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }

  double getTotalSavings() {
    return _savingsAccounts
        .where((account) => account.isActive)
        .fold(0.0, (sum, account) => sum + account.currentAmount);
  }

  double getTotalTargetAmount() {
    return _savingsAccounts
        .where((account) => account.isActive)
        .fold(0.0, (sum, account) => sum + account.targetAmount);
  }

  List<SavingsAccount> getCompletedGoals() {
    return _savingsAccounts
        .where((account) => account.isCompleted && account.isActive)
        .toList();
  }

  List<SavingsAccount> getActiveGoals() {
    return _savingsAccounts
        .where((account) => !account.isCompleted && account.isActive)
        .toList();
  }

  SavingsAccount? getSavingsAccountById(String id) {
    try {
      return _savingsAccounts.firstWhere((account) => account.id == id);
    } catch (e) {
      return null;
    }
  }

  Future<void> refreshData() async {
    await _loadSavingsData();
  }

  // Analytics methods
  Map<SavingsType, double> getSavingsByType() {
    final Map<SavingsType, double> result = {};
    
    for (final account in _savingsAccounts.where((a) => a.isActive)) {
      result[account.type] = (result[account.type] ?? 0.0) + account.currentAmount;
    }
    
    return result;
  }

  double getMonthlyProgress() {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    
    final monthlyTransactions = _savingsTransactions.where((transaction) =>
        transaction.createdAt.isAfter(startOfMonth) && transaction.isDeposit);
    
    return monthlyTransactions.fold(0.0, (sum, transaction) => sum + transaction.amount);
  }

  List<SavingsAccount> getUpcomingDeadlines({int days = 30}) {
    final cutoffDate = DateTime.now().add(Duration(days: days));
    
    return _savingsAccounts
        .where((account) => 
            account.isActive && 
            !account.isCompleted && 
            account.targetDate.isBefore(cutoffDate))
        .toList()
      ..sort((a, b) => a.targetDate.compareTo(b.targetDate));
  }
}
