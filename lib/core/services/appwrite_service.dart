import 'package:appwrite/appwrite.dart';
import 'package:appwrite/models.dart' as models;
import 'package:get/get.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../config/appwrite_config.dart';
import 'auth_service.dart';
import 'account_service.dart';
import 'personal_transaction_service.dart';
import 'error_handling_service.dart';

class AppwriteService extends GetxService {
  static AppwriteService get to => Get.find();

  late Client _client;
  late Account _account;
  late Databases _databases;
  late Storage _storage;

  final RxBool _isInitialized = false.obs;
  final RxBool _isOnline = true.obs;

  // Getters
  Client get client => _client;
  Account get account => _account;
  Databases get databases => _databases;
  Storage get storage => _storage;

  bool get isInitialized => _isInitialized.value;
  bool get isOnline => _isOnline.value;
  
  @override
  Future<void> onInit() async {
    super.onInit();
    await _initializeAppwrite();
    await _setupConnectivityListener();
  }
  
  Future<void> _initializeAppwrite() async {
    try {
      // Initialize Appwrite client
      _client = Client()
          .setEndpoint(AppwriteConfig.endpoint)
          .setProject(AppwriteConfig.projectId)
          .setSelfSigned(status: true); // For development only

      // Initialize services
      _account = Account(_client);
      _databases = Databases(_client);
      _storage = Storage(_client);

      _isInitialized.value = true;

      print('Appwrite service initialized successfully');
    } catch (e) {
      print('Error initializing Appwrite service: $e');
      _isInitialized.value = false;
    }
  }
  
  Future<void> _setupConnectivityListener() async {
    try {
      // Check initial connectivity
      final connectivityResults = await Connectivity().checkConnectivity();
      _updateConnectivityStatus(connectivityResults);

      // Listen for connectivity changes
      Connectivity().onConnectivityChanged.listen(_updateConnectivityStatus);
    } catch (e) {
      print('Error setting up connectivity listener: $e');
    }
  }
  
  void _updateConnectivityStatus(List<ConnectivityResult> results) {
    final isConnected = results.any((result) => 
        result == ConnectivityResult.mobile || 
        result == ConnectivityResult.wifi ||
        result == ConnectivityResult.ethernet);
    
    _isOnline.value = isConnected;
    
    if (isConnected) {
      print('Device is online');
      // Trigger sync when coming back online
      _triggerSync();
    } else {
      print('Device is offline');
    }
  }
  
  void _triggerSync() {
    // Trigger sync for all services when coming back online
    try {
      // Check if services are available before accessing them
      if (Get.isRegistered<AccountService>()) {
        final accountService = Get.find<AccountService>();
        if (Get.isRegistered<AuthService>()) {
          final authService = Get.find<AuthService>();
          accountService.loadUserAccounts(authService.currentUser?.id ?? '');
        }
      }

      if (Get.isRegistered<PersonalTransactionService>()) {
        final transactionService = Get.find<PersonalTransactionService>();
        transactionService.loadTransactionsFromAppwrite();
      }

      print('Data sync triggered successfully');
    } catch (e) {
      print('Error triggering sync: $e');
    }
  }
  
  // Health check method
  Future<bool> checkHealth() async {
    try {
      if (!_isInitialized.value) {
        await _initializeAppwrite();
      }

      // Simple connectivity check
      return _isOnline.value;
    } catch (e) {
      print('Backend health check failed: $e');
      return false;
    }
  }
  
  // Error handling helper
  String getErrorMessage(dynamic error) {
    try {
      final errorHandlingService = Get.find<ErrorHandlingService>();
      final appError = errorHandlingService.handleError(error, context: 'Appwrite Service');
      return appError.message;
    } catch (e) {
      // Fallback if error handling service is not available
      if (error is AppwriteException) {
        switch (error.code) {
          case 401:
            return AppwriteConfig.authErrorMessage;
          case 500:
          case 502:
          case 503:
            return AppwriteConfig.serverErrorMessage;
          default:
            return 'Appwrite Error ${error.code}: ${error.message}';
        }
      } else {
        return error.toString();
      }
    }
  }
  
  // Retry mechanism for network operations
  Future<T> retryOperation<T>(
    Future<T> Function() operation, {
    int maxRetries = 3,
    Duration delay = const Duration(seconds: 1),
  }) async {
    int attempts = 0;
    
    while (attempts < maxRetries) {
      try {
        return await operation();
      } catch (e) {
        attempts++;
        
        if (attempts >= maxRetries) {
          rethrow;
        }
        
        print('Operation failed (attempt $attempts/$maxRetries): $e');
        await Future.delayed(delay * attempts);
      }
    }
    
    throw Exception('Max retry attempts reached');
  }

  // Database Operations

  /// Create a document in a collection
  Future<models.Document?> createDocument({
    required String collectionId,
    required String documentId,
    required Map<String, dynamic> data,
    List<String>? permissions,
  }) async {
    try {
      return await retryOperation(() async {
        return await _databases.createDocument(
          databaseId: AppwriteConfig.databaseId,
          collectionId: collectionId,
          documentId: documentId,
          data: data,
          permissions: permissions,
        );
      });
    } catch (e) {
      print('Error creating document: $e');
      return null;
    }
  }

  /// Get a document by ID
  Future<models.Document?> getDocument({
    required String collectionId,
    required String documentId,
  }) async {
    try {
      return await retryOperation(() async {
        return await _databases.getDocument(
          databaseId: AppwriteConfig.databaseId,
          collectionId: collectionId,
          documentId: documentId,
        );
      });
    } catch (e) {
      print('Error getting document: $e');
      return null;
    }
  }

  /// Update a document
  Future<models.Document?> updateDocument({
    required String collectionId,
    required String documentId,
    required Map<String, dynamic> data,
    List<String>? permissions,
  }) async {
    try {
      return await retryOperation(() async {
        return await _databases.updateDocument(
          databaseId: AppwriteConfig.databaseId,
          collectionId: collectionId,
          documentId: documentId,
          data: data,
          permissions: permissions,
        );
      });
    } catch (e) {
      print('Error updating document: $e');
      return null;
    }
  }

  /// Delete a document
  Future<bool> deleteDocument({
    required String collectionId,
    required String documentId,
  }) async {
    try {
      await retryOperation(() async {
        await _databases.deleteDocument(
          databaseId: AppwriteConfig.databaseId,
          collectionId: collectionId,
          documentId: documentId,
        );
      });
      return true;
    } catch (e) {
      print('Error deleting document: $e');
      return false;
    }
  }

  /// List documents with queries
  Future<models.DocumentList?> listDocuments({
    required String collectionId,
    List<String>? queries,
  }) async {
    try {
      return await retryOperation(() async {
        return await _databases.listDocuments(
          databaseId: AppwriteConfig.databaseId,
          collectionId: collectionId,
          queries: queries ?? [],
        );
      });
    } catch (e) {
      print('Error listing documents: $e');
      return null;
    }
  }

  // Transaction-specific methods

  /// Create a transaction in Appwrite
  Future<models.Document?> createTransaction(Map<String, dynamic> transactionData) async {
    return await createDocument(
      collectionId: AppwriteConfig.transactionsCollectionId,
      documentId: transactionData['id'],
      data: transactionData,
    );
  }

  /// Update a transaction in Appwrite
  Future<models.Document?> updateTransaction(String transactionId, Map<String, dynamic> transactionData) async {
    return await updateDocument(
      collectionId: AppwriteConfig.transactionsCollectionId,
      documentId: transactionId,
      data: transactionData,
    );
  }

  /// Delete a transaction from Appwrite
  Future<bool> deleteTransaction(String transactionId) async {
    return await deleteDocument(
      collectionId: AppwriteConfig.transactionsCollectionId,
      documentId: transactionId,
    );
  }

  /// Get transactions from Appwrite
  Future<models.DocumentList?> getTransactions({
    String? accountId,
    List<String>? queries,
  }) async {
    final allQueries = <String>[];
    if (accountId != null) {
      allQueries.add(Query.equal('accountId', accountId));
    }
    if (queries != null) {
      allQueries.addAll(queries);
    }

    return await listDocuments(
      collectionId: AppwriteConfig.transactionsCollectionId,
      queries: allQueries,
    );
  }

  // Budget-specific methods

  /// Create a budget in Appwrite
  Future<models.Document?> createBudget(Map<String, dynamic> budgetData) async {
    return await createDocument(
      collectionId: AppwriteConfig.budgetsCollectionId,
      documentId: budgetData['id'],
      data: budgetData,
    );
  }

  /// Update a budget in Appwrite
  Future<models.Document?> updateBudget(String budgetId, Map<String, dynamic> budgetData) async {
    return await updateDocument(
      collectionId: AppwriteConfig.budgetsCollectionId,
      documentId: budgetId,
      data: budgetData,
    );
  }

  /// Delete a budget from Appwrite
  Future<bool> deleteBudget(String budgetId) async {
    return await deleteDocument(
      collectionId: AppwriteConfig.budgetsCollectionId,
      documentId: budgetId,
    );
  }

  /// Get budgets from Appwrite
  Future<models.DocumentList?> getBudgets({
    String? accountId,
    List<String>? queries,
  }) async {
    final allQueries = <String>[];
    if (accountId != null) {
      allQueries.add(Query.equal('accountId', accountId));
    }
    if (queries != null) {
      allQueries.addAll(queries);
    }

    return await listDocuments(
      collectionId: AppwriteConfig.budgetsCollectionId,
      queries: allQueries,
    );
  }
}
