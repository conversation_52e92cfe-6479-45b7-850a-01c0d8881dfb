import 'package:get/get.dart';
import 'package:local_auth/local_auth.dart';
import 'package:get_storage/get_storage.dart';
import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart';
import 'dart:convert';
import 'dart:typed_data';
import 'dart:math';

class SecurityService extends GetxService {
  final LocalAuthentication _localAuth = LocalAuthentication();
  final GetStorage _storage = GetStorage();
  
  late final Encrypter _encrypter;
  late final IV _iv;
  
  final RxBool _isBiometricEnabled = false.obs;
  final RxBool _isAppLocked = false.obs;
  final RxBool _isBiometricAvailable = false.obs;
  final RxString _securityLevel = 'medium'.obs;

  bool get isBiometricEnabled => _isBiometricEnabled.value;
  bool get isAppLocked => _isAppLocked.value;
  bool get isBiometricAvailable => _isBiometricAvailable.value;
  String get securityLevel => _securityLevel.value;

  @override
  void onInit() {
    super.onInit();
    _initializeSecurity();
  }

  /// Initialize security settings
  Future<void> _initializeSecurity() async {
    try {
      // Initialize encryption
      _initializeEncryption();
      
      // Check biometric availability
      await _checkBiometricAvailability();
      
      // Load security settings
      _loadSecuritySettings();
      
      // Set app lock status based on settings
      _updateAppLockStatus();
      
    } catch (e) {
      print('Error initializing security: $e');
    }
  }

  /// Initialize encryption with a secure key
  void _initializeEncryption() {
    // Generate or retrieve encryption key
    String? keyString = _storage.read('encryption_key');
    
    if (keyString == null) {
      // Generate new key
      final key = _generateSecureKey();
      keyString = base64Encode(key);
      _storage.write('encryption_key', keyString);
    }
    
    final key = Key(base64Decode(keyString));
    _encrypter = Encrypter(AES(key));
    _iv = IV.fromSecureRandom(16);
  }

  /// Generate a secure encryption key
  Uint8List _generateSecureKey() {
    final random = Random.secure();
    final key = Uint8List(32); // 256-bit key
    for (int i = 0; i < key.length; i++) {
      key[i] = random.nextInt(256);
    }
    return key;
  }

  /// Check if biometric authentication is available
  Future<void> _checkBiometricAvailability() async {
    try {
      final isAvailable = await _localAuth.canCheckBiometrics;
      final isDeviceSupported = await _localAuth.isDeviceSupported();
      
      _isBiometricAvailable.value = isAvailable && isDeviceSupported;
      
      if (_isBiometricAvailable.value) {
        final availableBiometrics = await _localAuth.getAvailableBiometrics();
        print('Available biometrics: $availableBiometrics');
      }
    } catch (e) {
      print('Error checking biometric availability: $e');
      _isBiometricAvailable.value = false;
    }
  }

  /// Load security settings from storage
  void _loadSecuritySettings() {
    _isBiometricEnabled.value = _storage.read('biometric_enabled') ?? false;
    _securityLevel.value = _storage.read('security_level') ?? 'medium';
  }

  /// Update app lock status
  void _updateAppLockStatus() {
    // App is locked if biometric is enabled or if it's a fresh start
    _isAppLocked.value = _isBiometricEnabled.value || _shouldLockOnStart();
  }

  /// Check if app should be locked on start
  bool _shouldLockOnStart() {
    final lastActiveTime = _storage.read('last_active_time');
    if (lastActiveTime == null) return true;
    
    final lastActive = DateTime.fromMillisecondsSinceEpoch(lastActiveTime);
    final timeDifference = DateTime.now().difference(lastActive);
    
    // Lock if app was inactive for more than 5 minutes
    return timeDifference.inMinutes > 5;
  }

  /// Authenticate user with biometrics
  Future<bool> authenticateWithBiometrics({
    String reason = 'Please authenticate to access your financial data',
  }) async {
    try {
      if (!_isBiometricAvailable.value) {
        Get.snackbar(
          'Biometric Not Available',
          'Biometric authentication is not available on this device',
          snackPosition: SnackPosition.BOTTOM,
        );
        return false;
      }

      final isAuthenticated = await _localAuth.authenticate(
        localizedReason: reason,
        options: const AuthenticationOptions(
          biometricOnly: false,
          stickyAuth: true,
        ),
      );

      if (isAuthenticated) {
        _isAppLocked.value = false;
        _updateLastActiveTime();
      }

      return isAuthenticated;
    } catch (e) {
      print('Biometric authentication error: $e');
      Get.snackbar(
        'Authentication Error',
        'Failed to authenticate: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    }
  }

  /// Enable biometric authentication
  Future<bool> enableBiometricAuth() async {
    if (!_isBiometricAvailable.value) {
      Get.snackbar(
        'Not Available',
        'Biometric authentication is not available on this device',
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    }

    final isAuthenticated = await authenticateWithBiometrics(
      reason: 'Authenticate to enable biometric login',
    );

    if (isAuthenticated) {
      _isBiometricEnabled.value = true;
      await _storage.write('biometric_enabled', true);
      
      Get.snackbar(
        'Biometric Enabled',
        'Biometric authentication has been enabled',
        snackPosition: SnackPosition.BOTTOM,
      );
      
      return true;
    }

    return false;
  }

  /// Disable biometric authentication
  Future<void> disableBiometricAuth() async {
    _isBiometricEnabled.value = false;
    await _storage.write('biometric_enabled', false);
    
    Get.snackbar(
      'Biometric Disabled',
      'Biometric authentication has been disabled',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  /// Set security level
  Future<void> setSecurityLevel(String level) async {
    _securityLevel.value = level;
    await _storage.write('security_level', level);
    
    // Update security measures based on level
    _applySecurityLevel(level);
  }

  /// Apply security measures based on level
  void _applySecurityLevel(String level) {
    switch (level) {
      case 'low':
        // Minimal security measures
        break;
      case 'medium':
        // Standard security measures
        break;
      case 'high':
        // Maximum security measures
        _enableHighSecurityMode();
        break;
    }
  }

  /// Enable high security mode
  void _enableHighSecurityMode() {
    // Additional security measures for high security level
    // Could include more frequent authentication, data masking, etc.
  }

  /// Encrypt sensitive data
  String encryptData(String data) {
    try {
      final encrypted = _encrypter.encrypt(data, iv: _iv);
      return '${encrypted.base64}:${_iv.base64}';
    } catch (e) {
      print('Encryption error: $e');
      return data; // Return original data if encryption fails
    }
  }

  /// Decrypt sensitive data
  String decryptData(String encryptedData) {
    try {
      final parts = encryptedData.split(':');
      if (parts.length != 2) return encryptedData;
      
      final encrypted = Encrypted.fromBase64(parts[0]);
      final iv = IV.fromBase64(parts[1]);
      
      return _encrypter.decrypt(encrypted, iv: iv);
    } catch (e) {
      print('Decryption error: $e');
      return encryptedData; // Return encrypted data if decryption fails
    }
  }

  /// Hash sensitive data (one-way)
  String hashData(String data) {
    final bytes = utf8.encode(data);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Verify hashed data
  bool verifyHashedData(String data, String hash) {
    return hashData(data) == hash;
  }

  /// Secure storage for sensitive data
  Future<void> secureStore(String key, dynamic value) async {
    String dataToStore;
    
    if (value is String) {
      dataToStore = value;
    } else {
      dataToStore = jsonEncode(value);
    }
    
    final encryptedData = encryptData(dataToStore);
    await _storage.write('secure_$key', encryptedData);
  }

  /// Retrieve from secure storage
  Future<T?> secureRetrieve<T>(String key) async {
    final encryptedData = _storage.read('secure_$key');
    if (encryptedData == null) return null;
    
    final decryptedData = decryptData(encryptedData);
    
    if (T == String) {
      return decryptedData as T;
    } else {
      try {
        return jsonDecode(decryptedData) as T;
      } catch (e) {
        print('Error parsing secure data: $e');
        return null;
      }
    }
  }

  /// Remove from secure storage
  Future<void> secureRemove(String key) async {
    await _storage.remove('secure_$key');
  }

  /// Update last active time
  void _updateLastActiveTime() {
    _storage.write('last_active_time', DateTime.now().millisecondsSinceEpoch);
  }

  /// Lock the app
  void lockApp() {
    _isAppLocked.value = true;
  }

  /// Unlock the app
  Future<bool> unlockApp() async {
    if (_isBiometricEnabled.value) {
      return await authenticateWithBiometrics();
    } else {
      // For now, just unlock if biometric is not enabled
      _isAppLocked.value = false;
      _updateLastActiveTime();
      return true;
    }
  }

  /// Check if data should be masked based on security level
  bool shouldMaskData() {
    return _securityLevel.value == 'high';
  }

  /// Mask sensitive data for display
  String maskData(String data, {int visibleChars = 4}) {
    if (!shouldMaskData() || data.length <= visibleChars) {
      return data;
    }
    
    final masked = '*' * (data.length - visibleChars);
    return masked + data.substring(data.length - visibleChars);
  }

  /// Generate secure PIN
  String generateSecurePIN({int length = 6}) {
    final random = Random.secure();
    String pin = '';
    
    for (int i = 0; i < length; i++) {
      pin += random.nextInt(10).toString();
    }
    
    return pin;
  }

  /// Validate PIN strength
  bool isStrongPIN(String pin) {
    if (pin.length < 6) return false;
    
    // Check for sequential numbers
    bool hasSequential = false;
    for (int i = 0; i < pin.length - 2; i++) {
      final current = int.tryParse(pin[i]) ?? 0;
      final next1 = int.tryParse(pin[i + 1]) ?? 0;
      final next2 = int.tryParse(pin[i + 2]) ?? 0;
      
      if (next1 == current + 1 && next2 == current + 2) {
        hasSequential = true;
        break;
      }
    }
    
    // Check for repeated digits
    final uniqueDigits = pin.split('').toSet().length;
    final hasVariety = uniqueDigits >= 3;
    
    return !hasSequential && hasVariety;
  }

  /// Clear all security data (for logout)
  Future<void> clearSecurityData() async {
    await _storage.remove('last_active_time');
    _isAppLocked.value = true;
  }

  /// Get security status summary
  Map<String, dynamic> getSecurityStatus() {
    return {
      'biometricAvailable': _isBiometricAvailable.value,
      'biometricEnabled': _isBiometricEnabled.value,
      'securityLevel': _securityLevel.value,
      'appLocked': _isAppLocked.value,
      'encryptionEnabled': true,
      'lastActiveTime': _storage.read('last_active_time'),
    };
  }

  /// Perform security audit
  List<String> performSecurityAudit() {
    final recommendations = <String>[];
    
    if (!_isBiometricEnabled.value && _isBiometricAvailable.value) {
      recommendations.add('Enable biometric authentication for better security');
    }
    
    if (_securityLevel.value == 'low') {
      recommendations.add('Consider increasing security level to medium or high');
    }
    
    // Add more security recommendations based on usage patterns
    
    return recommendations;
  }
}
