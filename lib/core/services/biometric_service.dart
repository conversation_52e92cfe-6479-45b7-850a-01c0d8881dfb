import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:local_auth/local_auth.dart';
import 'package:local_auth/error_codes.dart' as auth_error;
import 'account_service.dart';

class BiometricService extends GetxService {
  static BiometricService get to => Get.find();

  final LocalAuthentication _localAuth = LocalAuthentication();
  final GetStorage _storage = GetStorage();
  
  final RxBool _isAvailable = false.obs;
  final RxBool _isEnabled = false.obs;
  final RxBool _isAuthenticating = false.obs;
  final RxList<BiometricType> _availableTypes = <BiometricType>[].obs;

  bool get isAvailable => _isAvailable.value;
  bool get isEnabled => _isEnabled.value;
  bool get isAuthenticating => _isAuthenticating.value;
  List<BiometricType> get availableTypes => _availableTypes;

  @override
  Future<void> onInit() async {
    super.onInit();
    await _checkBiometricAvailability();
    await _loadBiometricSettings();
  }

  // Check if biometric authentication is available on device
  Future<void> _checkBiometricAvailability() async {
    try {
      final isAvailable = await _localAuth.canCheckBiometrics;
      final isDeviceSupported = await _localAuth.isDeviceSupported();
      
      _isAvailable.value = isAvailable && isDeviceSupported;
      
      if (_isAvailable.value) {
        final availableBiometrics = await _localAuth.getAvailableBiometrics();
        _availableTypes.value = availableBiometrics;
      }
    } catch (e) {
      print('Error checking biometric availability: $e');
      _isAvailable.value = false;
    }
  }

  // Load biometric settings from storage
  Future<void> _loadBiometricSettings() async {
    try {
      final accountId = AccountService.to.currentAccountId;
      if (accountId.isNotEmpty) {
        final isEnabled = _storage.read('biometric_enabled_$accountId') ?? false;
        _isEnabled.value = isEnabled;
      }
    } catch (e) {
      print('Error loading biometric settings: $e');
      _isEnabled.value = false;
    }
  }

  // Enable biometric authentication for current account
  Future<bool> enableBiometric() async {
    if (!_isAvailable.value) {
      Get.snackbar(
        'Not Available',
        'Biometric authentication is not available on this device',
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return false;
    }

    try {
      // First authenticate to confirm user identity
      final isAuthenticated = await authenticate(
        reason: 'Enable biometric authentication for secure access',
        requireConfirmation: true,
      );

      if (isAuthenticated) {
        final accountId = AccountService.to.currentAccountId;
        if (accountId.isNotEmpty) {
          _storage.write('biometric_enabled_$accountId', true);
          _isEnabled.value = true;
          
          Get.snackbar(
            'Success',
            'Biometric authentication enabled successfully',
            backgroundColor: Colors.green,
            colorText: Colors.white,
          );
          return true;
        }
      }
      return false;
    } catch (e) {
      print('Error enabling biometric: $e');
      Get.snackbar(
        'Error',
        'Failed to enable biometric authentication',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }
  }

  // Disable biometric authentication for current account
  Future<bool> disableBiometric() async {
    try {
      final accountId = AccountService.to.currentAccountId;
      if (accountId.isNotEmpty) {
        _storage.write('biometric_enabled_$accountId', false);
        _isEnabled.value = false;
        
        Get.snackbar(
          'Success',
          'Biometric authentication disabled',
          backgroundColor: Colors.blue,
          colorText: Colors.white,
        );
        return true;
      }
      return false;
    } catch (e) {
      print('Error disabling biometric: $e');
      return false;
    }
  }

  // Authenticate using biometrics
  Future<bool> authenticate({
    required String reason,
    bool requireConfirmation = false,
    bool stickyAuth = true,
  }) async {
    if (!_isAvailable.value) {
      return false;
    }

    try {
      _isAuthenticating.value = true;

      final isAuthenticated = await _localAuth.authenticate(
        localizedReason: reason,
        options: AuthenticationOptions(
          biometricOnly: false,
          stickyAuth: stickyAuth,
          sensitiveTransaction: requireConfirmation,
        ),
      );

      return isAuthenticated;
    } on PlatformException catch (e) {
      return _handleAuthenticationError(e);
    } catch (e) {
      print('Unexpected error during authentication: $e');
      return false;
    } finally {
      _isAuthenticating.value = false;
    }
  }

  // Handle authentication errors
  bool _handleAuthenticationError(PlatformException e) {
    String message;
    
    switch (e.code) {
      case auth_error.notAvailable:
        message = 'Biometric authentication is not available';
        break;
      case auth_error.notEnrolled:
        message = 'No biometrics enrolled. Please set up biometric authentication in device settings';
        break;
      case auth_error.lockedOut:
        message = 'Biometric authentication is temporarily locked. Please try again later';
        break;
      case auth_error.permanentlyLockedOut:
        message = 'Biometric authentication is permanently locked. Please use device credentials';
        break;
      case auth_error.biometricOnlyNotSupported:
        message = 'Biometric-only authentication is not supported on this device';
        break;
      default:
        message = 'Authentication failed: ${e.message}';
    }

    Get.snackbar(
      'Authentication Error',
      message,
      backgroundColor: Colors.red,
      colorText: Colors.white,
      duration: const Duration(seconds: 4),
    );

    return false;
  }

  // Get available biometric types as user-friendly strings
  List<String> get availableTypesNames {
    return _availableTypes.map((type) {
      switch (type) {
        case BiometricType.face:
          return 'Face ID';
        case BiometricType.fingerprint:
          return 'Fingerprint';
        case BiometricType.iris:
          return 'Iris';
        case BiometricType.strong:
          return 'Strong Biometric';
        case BiometricType.weak:
          return 'Weak Biometric';
        default:
          return 'Biometric';
      }
    }).toList();
  }

  // Get primary biometric type
  String get primaryBiometricType {
    if (_availableTypes.isEmpty) return 'None';
    
    // Prioritize Face ID and Fingerprint
    if (_availableTypes.contains(BiometricType.face)) {
      return 'Face ID';
    } else if (_availableTypes.contains(BiometricType.fingerprint)) {
      return 'Fingerprint';
    } else if (_availableTypes.contains(BiometricType.iris)) {
      return 'Iris';
    } else {
      return 'Biometric';
    }
  }

  // Get appropriate icon for biometric type
  IconData get biometricIcon {
    if (_availableTypes.contains(BiometricType.face)) {
      return Icons.face_rounded;
    } else if (_availableTypes.contains(BiometricType.fingerprint)) {
      return Icons.fingerprint_rounded;
    } else if (_availableTypes.contains(BiometricType.iris)) {
      return Icons.visibility_rounded;
    } else {
      return Icons.security_rounded;
    }
  }

  // Quick authentication for app unlock
  Future<bool> quickAuthenticate() async {
    if (!_isEnabled.value || !_isAvailable.value) {
      return false;
    }

    return await authenticate(
      reason: 'Authenticate to access your financial data',
      requireConfirmation: false,
      stickyAuth: false,
    );
  }

  // Secure authentication for sensitive operations
  Future<bool> secureAuthenticate({required String operation}) async {
    if (!_isAvailable.value) {
      return false;
    }

    return await authenticate(
      reason: 'Authenticate to $operation',
      requireConfirmation: true,
      stickyAuth: true,
    );
  }

  // Check if biometric is enabled for current account
  bool isBiometricEnabledForAccount(String accountId) {
    return _storage.read('biometric_enabled_$accountId') ?? false;
  }

  // Clear biometric settings for account
  Future<void> clearBiometricForAccount(String accountId) async {
    _storage.remove('biometric_enabled_$accountId');
    
    // If this is the current account, update the state
    if (AccountService.to.currentAccountId == accountId) {
      _isEnabled.value = false;
    }
  }

  // Get biometric status summary
  String get statusSummary {
    if (!_isAvailable.value) {
      return 'Not available on this device';
    } else if (_isEnabled.value) {
      return 'Enabled for ${primaryBiometricType}';
    } else {
      return 'Available but not enabled';
    }
  }

  // Show biometric setup dialog
  Future<void> showBiometricSetupDialog() async {
    if (!_isAvailable.value) {
      Get.dialog(
        AlertDialog(
          title: const Text('Biometric Not Available'),
          content: const Text(
            'Biometric authentication is not available on this device. '
            'Please ensure your device supports biometric authentication and it is set up in device settings.',
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('OK'),
            ),
          ],
        ),
      );
      return;
    }

    Get.dialog(
      AlertDialog(
        title: Text('Enable ${primaryBiometricType}?'),
        content: Text(
          'Would you like to enable ${primaryBiometricType} authentication for quick and secure access to your financial data?',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Not Now'),
          ),
          ElevatedButton(
            onPressed: () async {
              Get.back();
              await enableBiometric();
            },
            child: const Text('Enable'),
          ),
        ],
      ),
    );
  }

  // Refresh biometric availability (call when app resumes)
  Future<void> refresh() async {
    await _checkBiometricAvailability();
    await _loadBiometricSettings();
  }

  /// Check if biometric authentication is available on the device
  Future<bool> isBiometricAvailable() async {
    try {
      final isAvailable = await _localAuth.canCheckBiometrics;
      final isDeviceSupported = await _localAuth.isDeviceSupported();
      return isAvailable && isDeviceSupported;
    } catch (e) {
      print('Error checking biometric availability: $e');
      return false;
    }
  }
}
