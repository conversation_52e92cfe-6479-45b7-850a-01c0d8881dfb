import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:drift/drift.dart' as drift;
import '../database/database.dart';
import '../models/account_type.dart';
import 'database_service.dart';
import 'auth_service.dart';

class AccountService extends GetxService {
  static AccountService get to => Get.find();

  AppDatabase get _database => DatabaseService.to.database;
  final GetStorage _storage = GetStorage();
  final Rx<UserAccount?> _currentAccount = Rx<UserAccount?>(null);
  final RxList<UserAccount> _allAccounts = <UserAccount>[].obs;
  final RxBool _isLoading = false.obs;
  final RxBool _isSyncing = false.obs;

  UserAccount? get currentAccount => _currentAccount.value;
  Rx<UserAccount?> get currentAccountObs => _currentAccount;
  List<UserAccount> get allAccounts => _allAccounts;
  bool get isLoading => _isLoading.value;
  bool get isSyncing => _isSyncing.value;
  bool get hasActiveAccount => _currentAccount.value != null;
  
  String get currentAccountId => _currentAccount.value?.id ?? '';
  AccountType get currentAccountType => 
    _currentAccount.value?.accountType == 'business' 
      ? AccountType.business 
      : AccountType.personal;

  @override
  Future<void> onInit() async {
    super.onInit();
    await _loadAccounts();
    await _loadActiveAccount();
  }

  Future<void> _loadAccounts() async {
    _isLoading.value = true;
    try {
      final accounts = await _database.getAllAccounts();
      _allAccounts.assignAll(accounts);
    } catch (e) {
      print('Error loading accounts: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  Future<void> _loadActiveAccount() async {
    try {
      final activeAccount = await _database.getActiveAccount();
      _currentAccount.value = activeAccount;
      print('Active account loaded: ${activeAccount?.id ?? 'none'}');
    } catch (e) {
      print('Error loading active account: $e');
    }
  }

  Future<bool> switchAccount(String accountId) async {
    try {
      _isLoading.value = true;
      
      await _database.switchAccount(accountId);
      await _loadActiveAccount();
      await _loadAccounts();
      
      // Save last active account to GetStorage
      _storage.write('last_active_account', accountId);
      
      return true;
    } catch (e) {
      print('Error switching account: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<String?> createPersonalAccount({
    required String name,
    String? email,
    bool makeActive = false,
  }) async {
    try {
      _isLoading.value = true;

      // Use current user's email if not provided - all accounts share the same profile
      final currentUser = Get.find<AuthService>().currentUser;
      final accountEmail = email ?? currentUser?.email ?? '';

      final accountId = 'personal_${DateTime.now().millisecondsSinceEpoch}';

      await _database.createAccount(
        id: accountId,
        name: name,
        email: accountEmail,
        accountType: 'personal',
        makeActive: makeActive,
      );

      await _loadAccounts();
      if (makeActive) {
        await _loadActiveAccount();
      }

      return accountId;
    } catch (e) {
      print('Error creating personal account: $e');
      return null;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<String?> createBusinessAccount({
    required String name,
    String? email,
    bool makeActive = false,
  }) async {
    try {
      _isLoading.value = true;

      // Use current user's email if not provided - business accounts share the same profile
      final currentUser = Get.find<AuthService>().currentUser;
      final businessEmail = email ?? currentUser?.email ?? '';

      final accountId = 'business_${DateTime.now().millisecondsSinceEpoch}';

      await _database.createAccount(
        id: accountId,
        name: name,
        email: businessEmail,
        accountType: 'business',
        makeActive: makeActive,
      );

      await _loadAccounts();
      if (makeActive) {
        await _loadActiveAccount();
      }

      return accountId;
    } catch (e) {
      print('Error creating business account: $e');
      return null;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> updateAccountProfile({
    required String accountId,
    String? name,
    String? email,
    String? profileImageUrl,
    String? currency,
    String? timezone,
  }) async {
    try {
      final updates = UserAccountsCompanion(
        name: name != null ? drift.Value(name) : const drift.Value.absent(),
        email: email != null ? drift.Value(email) : const drift.Value.absent(),
        profileImageUrl: profileImageUrl != null ? drift.Value(profileImageUrl) : const drift.Value.absent(),
        currency: currency != null ? drift.Value(currency) : const drift.Value.absent(),
        timezone: timezone != null ? drift.Value(timezone) : const drift.Value.absent(),
        updatedAt: drift.Value(DateTime.now()),
      );

      await (_database.update(_database.userAccounts)
        ..where((tbl) => tbl.id.equals(accountId)))
        .write(updates);
      
      await _loadAccounts();
      await _loadActiveAccount();
      
      return true;
    } catch (e) {
      print('Error updating account profile: $e');
      return false;
    }
  }

  Future<bool> deleteAccount(String accountId) async {
    try {
      _isLoading.value = true;
      
      // Don't allow deleting the last account
      if (_allAccounts.length <= 1) {
        return false;
      }
      
      // If deleting active account, switch to another one first
      if (_currentAccount.value?.id == accountId) {
        final otherAccount = _allAccounts.firstWhere((acc) => acc.id != accountId);
        await switchAccount(otherAccount.id);
      }
      
      // Delete account and all related data
      await _database.transaction(() async {
        await (_database.delete(_database.personalTransactions)
          ..where((tbl) => tbl.accountId.equals(accountId))).go();
        await (_database.delete(_database.accountSettings)
          ..where((tbl) => tbl.accountId.equals(accountId))).go();
        await (_database.delete(_database.syncStatus)
          ..where((tbl) => tbl.accountId.equals(accountId))).go();
        await (_database.delete(_database.userAccounts)
          ..where((tbl) => tbl.id.equals(accountId))).go();
      });
      
      await _loadAccounts();
      
      return true;
    } catch (e) {
      print('Error deleting account: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<void> updateLastLoginTime(String accountId) async {
    try {
      await (_database.update(_database.userAccounts)
        ..where((tbl) => tbl.id.equals(accountId)))
        .write(UserAccountsCompanion(
          lastLoginAt: drift.Value(DateTime.now()),
          updatedAt: drift.Value(DateTime.now()),
        ));
    } catch (e) {
      print('Error updating last login time: $e');
    }
  }

  Future<bool> upgradeToPremium(String accountId, DateTime expiresAt) async {
    try {
      await (_database.update(_database.userAccounts)
        ..where((tbl) => tbl.id.equals(accountId)))
        .write(UserAccountsCompanion(
          isPremium: const drift.Value(true),
          premiumExpiresAt: drift.Value(expiresAt),
          updatedAt: drift.Value(DateTime.now()),
        ));
      
      await _loadAccounts();
      await _loadActiveAccount();
      
      return true;
    } catch (e) {
      print('Error upgrading to premium: $e');
      return false;
    }
  }

  bool isPremiumFeatureAvailable() {
    final account = _currentAccount.value;
    if (account == null) return false;
    
    if (!account.isPremium) return false;
    
    final expiresAt = account.premiumExpiresAt;
    if (expiresAt == null) return false;
    
    return DateTime.now().isBefore(expiresAt);
  }

  Future<void> restoreLastActiveAccount() async {
    try {
      final lastActiveAccountId = _storage.read('last_active_account');

      if (lastActiveAccountId != null) {
        final account = await _database.getAccountById(lastActiveAccountId);
        if (account != null) {
          await switchAccount(lastActiveAccountId);
        }
      }
    } catch (e) {
      print('Error restoring last active account: $e');
    }
  }

  // Account settings methods
  Future<String?> getAccountSetting(String key) async {
    if (_currentAccount.value == null) return null;
    
    try {
      final setting = await _database.getAccountSetting(_currentAccount.value!.id, key);
      return setting?.value;
    } catch (e) {
      print('Error getting account setting: $e');
      return null;
    }
  }

  Future<bool> setAccountSetting(String key, String value) async {
    if (_currentAccount.value == null) return false;
    
    try {
      await _database.setAccountSetting(_currentAccount.value!.id, key, value);
      return true;
    } catch (e) {
      print('Error setting account setting: $e');
      return false;
    }
  }

  Future<Map<String, String>> getAllAccountSettings() async {
    if (_currentAccount.value == null) return {};

    try {
      return await _database.getAllAccountSettings(_currentAccount.value!.id);
    } catch (e) {
      print('Error getting all account settings: $e');
      return {};
    }
  }

  // Appwrite Integration Methods

  /// Load user accounts for a specific user (simplified for local storage)
  Future<void> loadUserAccounts(String userId) async {
    try {
      _isSyncing.value = true;

      // For now, just reload local accounts
      // In a real implementation, this would sync with backend
      await _loadAccounts();
      await _loadActiveAccount();

    } catch (e) {
      print('Error loading user accounts: $e');
    } finally {
      _isSyncing.value = false;
    }
  }

  /// Sync account data (simplified for local storage)
  Future<void> _syncAccountFromBackend(Map<String, dynamic> data) async {
    // This method is simplified for local storage
    // In a real implementation, this would sync data from backend
    print('Account sync placeholder: ${data['name']}');
  }

  /// Sync account to backend (simplified for local storage)
  Future<bool> syncAccountToBackend(UserAccount account, String userId) async {
    try {
      // Simulate backend sync
      await Future.delayed(const Duration(milliseconds: 500));

      print('Account synced to backend: ${account.name}');
      return true;
    } catch (e) {
      print('Error syncing account to backend: $e');
      return false;
    }
  }

  /// Create a new account
  Future<UserAccount> createAccount({
    required String name,
    required String email,
    required AccountType accountType,
    bool makeActive = false,
  }) async {
    try {
      final accountData = UserAccountsCompanion(
        id: drift.Value(DateTime.now().millisecondsSinceEpoch.toString()),
        name: drift.Value(name),
        email: drift.Value(email),
        accountType: drift.Value(accountType.value),
        isActive: drift.Value(makeActive),
        isPremium: const drift.Value(false),
        createdAt: drift.Value(DateTime.now()),
        updatedAt: drift.Value(DateTime.now()),
      );

      final account = await _database.into(_database.userAccounts).insertReturning(accountData);

      // Add to local list
      _allAccounts.add(account);

      // Make active if requested or if it's the first account
      if (makeActive || _allAccounts.length == 1) {
        await switchAccount(account.id);
      }

      return account;
    } catch (e) {
      throw Exception('Failed to create account: $e');
    }
  }

  /// Update an existing account
  Future<UserAccount> updateAccount(
    String accountId, {
    String? name,
    String? email,
    String? accountType,
    String? profileImageUrl,
  }) async {
    try {
      final updateData = UserAccountsCompanion(
        name: name != null ? drift.Value(name) : const drift.Value.absent(),
        email: email != null ? drift.Value(email) : const drift.Value.absent(),
        accountType: accountType != null ? drift.Value(accountType) : const drift.Value.absent(),
        profileImageUrl: profileImageUrl != null ? drift.Value(profileImageUrl) : const drift.Value.absent(),
        updatedAt: drift.Value(DateTime.now()),
      );

      await (_database.update(_database.userAccounts)
            ..where((tbl) => tbl.id.equals(accountId)))
          .write(updateData);

      // Reload accounts to get updated data
      await _loadAccounts();

      // Find and return the updated account
      final updatedAccount = _allAccounts.firstWhere((account) => account.id == accountId);

      // Update current account if it's the one being updated
      if (_currentAccount.value?.id == accountId) {
        _currentAccount.value = updatedAccount;
      }

      return updatedAccount;
    } catch (e) {
      throw Exception('Failed to update account: $e');
    }
  }



  /// Get accounts by type
  List<UserAccount> getAccountsByType(AccountType type) {
    return _allAccounts.where((account) => account.accountType == type.value).toList();
  }

  /// Get business accounts
  List<UserAccount> get businessAccounts => getAccountsByType(AccountType.business);

  /// Get personal accounts
  List<UserAccount> get personalAccounts => getAccountsByType(AccountType.personal);

  /// Check if user has multiple accounts
  bool get hasMultipleAccounts => _allAccounts.length > 1;

  /// Get account statistics
  Map<String, dynamic> getAccountStats() {
    return {
      'total_accounts': _allAccounts.length,
      'business_accounts': businessAccounts.length,
      'personal_accounts': personalAccounts.length,
      'premium_accounts': _allAccounts.where((acc) => acc.isPremium).length,
      'current_account_type': currentAccountType.value,
    };
  }

  /// Clear all local data (used during sign out)
  Future<void> clearData() async {
    try {
      _currentAccount.value = null;
      _allAccounts.clear();

      // Clear local database
      await _database.transaction(() async {
        await _database.delete(_database.personalTransactions).go();
        await _database.delete(_database.accountSettings).go();
        await _database.delete(_database.syncStatus).go();
        await _database.delete(_database.userAccounts).go();
      });

      // Clear storage
      await _storage.remove('current_account_id');
    } catch (e) {
      print('Error clearing account data: $e');
    }
  }
}
