import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:appwrite/appwrite.dart';
import 'package:appwrite/enums.dart';
import '../config/appwrite_config.dart';
import '../models/app_user.dart';
import '../routes/app_routes.dart';
import 'appwrite_service.dart';
import 'account_service.dart';

class AuthService extends GetxService {
  static AuthService get to => Get.find();

  final AppwriteService _appwriteService = AppwriteService.to;
  final GetStorage _storage = GetStorage();

  final Rx<AppUser?> _currentUser = Rx<AppUser?>(null);
  final RxBool _isAuthenticated = false.obs;
  final RxBool _isLoading = false.obs;
  
  // Getters
  AppUser? get currentUser => _currentUser.value;
  bool get isAuthenticated => _isAuthenticated.value;
  bool get isLoading => _isLoading.value;
  String get userId => _currentUser.value?.id ?? '';
  
  @override
  Future<void> onInit() async {
    super.onInit();
    await _checkAuthStatus();
  }
  
  Future<void> _checkAuthStatus() async {
    try {
      _isLoading.value = true;

      // Try to get current session from Appwrite
      try {
        final session = await _appwriteService.account.getSession(sessionId: 'current');
        if (session.$id.isNotEmpty) {
          // Get user details
          final user = await _appwriteService.account.get();
          _currentUser.value = AppUser(
            id: user.$id,
            name: user.name,
            email: user.email,
            emailVerification: user.emailVerification,
            phoneVerification: user.phoneVerification,
            labels: user.labels,
            registration: DateTime.parse(user.registration),
            status: user.status ? 'active' : 'inactive',
          );
          _isAuthenticated.value = true;

          // Save to local storage for offline access
          _storage.write('is_logged_in', true);
          _storage.write('user_id', user.$id);
          _storage.write('user_email', user.email);
          _storage.write('user_name', user.name);

          // Ensure user has at least one account
          await _ensureUserHasAccount(user.name, user.email);
          return;
        }
      } catch (e) {
        // No active session, check local storage
        print('No active Appwrite session: $e');
      }

      // Fallback to local storage check
      final isLoggedIn = _storage.read('is_logged_in') ?? false;
      if (isLoggedIn) {
        final email = _storage.read('user_email') ?? '';
        final name = _storage.read('user_name') ?? '';
        final userId = _storage.read('user_id') ?? '';

        if (email.isNotEmpty && userId.isNotEmpty) {
          _currentUser.value = AppUser(
            id: userId,
            name: name,
            email: email,
            emailVerification: true,
            phoneVerification: false,
            labels: [],
            registration: DateTime.now(),
            status: 'active',
          );
          _isAuthenticated.value = true;

          // Ensure user has at least one account
          await _ensureUserHasAccount(name, email);
        }
      }
    } catch (e) {
      print('No active session found: $e');
      _isAuthenticated.value = false;
    } finally {
      _isLoading.value = false;
    }
  }
  
  Future<void> _loadCurrentUser() async {
    // This method is simplified for local authentication
    // In a real implementation, this would load user data from the backend
  }
  
  // Email/Password Authentication
  Future<AuthResult> signUpWithEmail({
    required String email,
    required String password,
    required String name,
  }) async {
    try {
      _isLoading.value = true;

      // Create account with Appwrite
      final user = await _appwriteService.account.create(
        userId: ID.unique(),
        email: email,
        password: password,
        name: name,
      );

      // Create session
      await _appwriteService.account.createEmailPasswordSession(
        email: email,
        password: password,
      );

      // Update user object
      _currentUser.value = AppUser(
        id: user.$id,
        name: user.name,
        email: user.email,
        emailVerification: user.emailVerification,
        phoneVerification: user.phoneVerification,
        labels: user.labels,
        registration: DateTime.parse(user.registration),
        status: user.status ? 'active' : 'inactive',
      );
      _isAuthenticated.value = true;

      // Save to local storage
      _storage.write('is_logged_in', true);
      _storage.write('user_id', user.$id);
      _storage.write('user_email', user.email);
      _storage.write('user_name', user.name);

      // Create default personal account
      await AccountService.to.createPersonalAccount(
        name: '$name\'s Personal Account',
        email: email,
        makeActive: true,
      );

      return AuthResult.success('Account created successfully!');
    } catch (e) {
      return AuthResult.error('Failed to create account: $e');
    } finally {
      _isLoading.value = false;
    }
  }
  
  Future<AuthResult> signInWithEmail({
    required String email,
    required String password,
  }) async {
    try {
      _isLoading.value = true;

      // Create session with Appwrite
      await _appwriteService.account.createEmailPasswordSession(
        email: email,
        password: password,
      );

      // Get user details
      final user = await _appwriteService.account.get();

      // Update user object
      _currentUser.value = AppUser(
        id: user.$id,
        name: user.name,
        email: user.email,
        emailVerification: user.emailVerification,
        phoneVerification: user.phoneVerification,
        labels: user.labels,
        registration: DateTime.parse(user.registration),
        status: user.status ? 'active' : 'inactive',
      );
      _isAuthenticated.value = true;

      // Save to local storage
      _storage.write('is_logged_in', true);
      _storage.write('user_id', user.$id);
      _storage.write('user_email', user.email);
      _storage.write('user_name', user.name);

      // Ensure user has at least one account after login
      await _ensureUserHasAccount(user.name, user.email);

      return AuthResult.success('Signed in successfully!');
    } catch (e) {
      return AuthResult.error('Failed to sign in: $e');
    } finally {
      _isLoading.value = false;
    }
  }
  
  // OAuth Authentication (Google, Apple, etc.)
  Future<AuthResult> signInWithOAuth(String provider) async {
    try {
      _isLoading.value = true;

      // Create OAuth session with Appwrite
      await _appwriteService.account.createOAuth2Session(
        provider: OAuthProvider.values.firstWhere(
          (p) => p.name.toLowerCase() == provider.toLowerCase(),
          orElse: () => OAuthProvider.google,
        ),
        success: '${AppwriteConfig.appName}://auth/success',
        failure: '${AppwriteConfig.appName}://auth/failure',
      );

      // Get user details after successful OAuth
      final user = await _appwriteService.account.get();

      // Update user object
      _currentUser.value = AppUser(
        id: user.$id,
        name: user.name,
        email: user.email,
        emailVerification: user.emailVerification,
        phoneVerification: user.phoneVerification,
        labels: user.labels,
        registration: DateTime.parse(user.registration),
        status: user.status ? 'active' : 'inactive',
      );

      _isAuthenticated.value = true;

      // Save to local storage for offline access
      _storage.write('is_logged_in', true);
      _storage.write('user_id', user.$id);
      _storage.write('user_email', user.email);
      _storage.write('user_name', user.name);

      // Ensure user has at least one account after OAuth login
      await _ensureUserHasAccount(user.name, user.email);

      return AuthResult.success('Successfully signed in with $provider');

    } catch (e) {
      return AuthResult.error('OAuth authentication failed: ${_appwriteService.getErrorMessage(e)}');
    } finally {
      _isLoading.value = false;
    }
  }
  
  // Password Reset
  Future<AuthResult> resetPassword(String email) async {
    try {
      _isLoading.value = true;

      // Simulate password reset (in real app, this would call backend)
      await Future.delayed(const Duration(seconds: 1));

      return AuthResult.success('Password reset email sent!');
    } catch (e) {
      return AuthResult.error('An unexpected error occurred: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// Ensure user has at least one account after login
  Future<void> _ensureUserHasAccount(String userName, String userEmail) async {
    try {
      final accountService = AccountService.to;

      // Check if user already has accounts
      await accountService.onInit(); // Reload accounts

      if (!accountService.hasActiveAccount) {
        // Create a default personal account
        final accountId = await accountService.createPersonalAccount(
          name: '$userName\'s Personal Account',
          email: userEmail,
          makeActive: true,
        );

        if (accountId != null) {
          print('Created default personal account: $accountId');
        } else {
          print('Failed to create default account');
        }
      }
    } catch (e) {
      print('Error ensuring user has account: $e');
    }
  }

  // Sign Out
  Future<void> signOut() async {
    try {
      _isLoading.value = true;

      // Delete current session from Appwrite
      try {
        await _appwriteService.account.deleteSession(sessionId: 'current');
      } catch (e) {
        print('Error deleting Appwrite session: $e');
      }

      // Clear local data
      _currentUser.value = null;
      _isAuthenticated.value = false;

      // Clear local preferences
      _storage.write('is_logged_in', false);
      _storage.remove('user_id');
      _storage.remove('user_email');
      _storage.remove('user_name');

      // Clear account service data
      await AccountService.to.clearData();

      // Navigate to login screen
      Get.offAllNamed(AppRoutes.login);

    } catch (e) {
      print('Error signing out: $e');
    } finally {
      _isLoading.value = false;
    }
  }
  
  // Update Profile
  Future<AuthResult> updateProfile({
    String? name,
    String? email,
  }) async {
    try {
      _isLoading.value = true;

      // Simulate profile update (in real app, this would call backend)
      await Future.delayed(const Duration(seconds: 1));

      // Update local user data
      if (_currentUser.value != null) {
        if (name != null) {
          _storage.write('user_name', name);
          _currentUser.value = _currentUser.value!.copyWith(name: name);
        }

        if (email != null) {
          _storage.write('user_email', email);
          _currentUser.value = _currentUser.value!.copyWith(email: email);
        }
      }

      return AuthResult.success('Profile updated successfully!');
    } catch (e) {
      return AuthResult.error('An unexpected error occurred: $e');
    } finally {
      _isLoading.value = false;
    }
  }
  
  // Delete Account
  Future<AuthResult> deleteAccount() async {
    try {
      _isLoading.value = true;

      // For demo purposes, just sign out and clear local data
      await signOut();

      return AuthResult.success('Account data cleared successfully!');
    } catch (e) {
      return AuthResult.error('An unexpected error occurred: $e');
    } finally {
      _isLoading.value = false;
    }
  }
}

// Auth Result Helper Class
class AuthResult {
  final bool isSuccess;
  final String message;
  
  AuthResult._(this.isSuccess, this.message);
  
  factory AuthResult.success(String message) => AuthResult._(true, message);
  factory AuthResult.error(String message) => AuthResult._(false, message);
}
