import 'dart:convert';
import 'dart:io';
import 'package:drift/drift.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:file_picker/file_picker.dart';
import 'package:csv/csv.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import '../database/database.dart';
import '../database/tables/personal_transactions.dart';
import '../models/transaction.dart';
import 'account_service.dart';
import 'personal_transaction_service.dart';

enum ExportFormat { csv, json, pdf }
enum ExportType { transactions, summary, full }

class DataExportService extends GetxService {
  static DataExportService get to => Get.find();

  final RxBool _isExporting = false.obs;
  final RxBool _isImporting = false.obs;
  final RxDouble _exportProgress = 0.0.obs;
  final RxDouble _importProgress = 0.0.obs;

  bool get isExporting => _isExporting.value;
  bool get isImporting => _isImporting.value;
  double get exportProgress => _exportProgress.value;
  double get importProgress => _importProgress.value;

  // Export data in specified format
  Future<bool> exportData({
    required ExportFormat format,
    required ExportType type,
    DateTime? startDate,
    DateTime? endDate,
    String? category,
    String? transactionType,
  }) async {
    try {
      _isExporting.value = true;
      _exportProgress.value = 0.0;

      final accountId = AccountService.to.currentAccountId;
      if (accountId.isEmpty) {
        throw Exception('No active account found');
      }

      // Get data based on type
      List<PersonalTransaction> transactions = [];
      Map<String, dynamic> summaryData = {};

      _exportProgress.value = 0.2;

      switch (type) {
        case ExportType.transactions:
          transactions = await _getFilteredTransactions(
            accountId: accountId,
            startDate: startDate,
            endDate: endDate,
          );
          break;
        case ExportType.summary:
          summaryData = await _generateSummaryData(
            accountId: accountId,
            startDate: startDate,
            endDate: endDate,
          );
          break;
        case ExportType.full:
          transactions = await _getFilteredTransactions(accountId: accountId);
          summaryData = await _generateSummaryData(accountId: accountId);
          break;
      }

      _exportProgress.value = 0.5;

      // Generate file based on format
      String? filePath;
      switch (format) {
        case ExportFormat.csv:
          filePath = await _exportToCsv(transactions, type);
          break;
        case ExportFormat.json:
          filePath = await _exportToJson(transactions, summaryData, type);
          break;
        case ExportFormat.pdf:
          filePath = await _exportToPdf(transactions, summaryData, type);
          break;
      }

      _exportProgress.value = 0.8;

      if (filePath != null) {
        // File saved successfully
        _exportProgress.value = 1.0;

        Get.snackbar(
          'Export Successful',
          'Data exported to: $filePath',
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
        return true;
      }

      return false;
    } catch (e) {
      print('Error exporting data: $e');
      Get.snackbar(
        'Export Failed',
        'Failed to export data: ${e.toString()}',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    } finally {
      _isExporting.value = false;
      _exportProgress.value = 0.0;
    }
  }

  // Import data from file
  Future<bool> importData() async {
    try {
      _isImporting.value = true;
      _importProgress.value = 0.0;

      // Pick file
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['csv', 'json'],
      );

      if (result == null || result.files.single.path == null) {
        return false;
      }

      final file = File(result.files.single.path!);
      final extension = result.files.single.extension?.toLowerCase();

      _importProgress.value = 0.2;

      List<PersonalTransaction> transactions = [];

      switch (extension) {
        case 'csv':
          transactions = await _importFromCsv(file);
          break;
        case 'json':
          transactions = await _importFromJson(file);
          break;
        default:
          throw Exception('Unsupported file format');
      }

      _importProgress.value = 0.6;

      // Import transactions
      final accountId = AccountService.to.currentAccountId;
      if (accountId.isEmpty) {
        throw Exception('No active account found');
      }

      int imported = 0;
      for (final transaction in transactions) {
        try {
          await PersonalTransactionService.to.addTransaction(
            title: transaction.title,
            description: transaction.description,
            amount: transaction.amount,
            type: TransactionType.values.firstWhere(
              (e) => e.name == transaction.type,
              orElse: () => TransactionType.expense,
            ),
            category: TransactionCategory.values.firstWhere(
              (e) => e.name == transaction.category,
              orElse: () => TransactionCategory.shopping,
            ),
            date: transaction.date,
            paymentMethod: transaction.paymentMethod,
          );
          imported++;
        } catch (e) {
          print('Error importing transaction: $e');
        }
      }

      _importProgress.value = 1.0;

      Get.snackbar(
        'Import Successful',
        'Imported $imported transactions',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );

      return true;
    } catch (e) {
      print('Error importing data: $e');
      Get.snackbar(
        'Import Failed',
        'Failed to import data: ${e.toString()}',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    } finally {
      _isImporting.value = false;
      _importProgress.value = 0.0;
    }
  }

  // Get filtered transactions
  Future<List<PersonalTransaction>> _getFilteredTransactions({
    required String accountId,
    DateTime? startDate,
    DateTime? endDate,
    String? category,
    String? transactionType,
  }) async {
    final database = Get.find<AppDatabase>();
    
    var query = database.select(database.personalTransactions)
      ..where((tbl) => tbl.accountId.equals(accountId));

    if (startDate != null && endDate != null) {
      query.where((tbl) => tbl.date.isBetweenValues(startDate, endDate));
    }

    if (category != null && category.isNotEmpty) {
      query.where((tbl) => tbl.category.equals(category));
    }

    if (transactionType != null && transactionType.isNotEmpty) {
      query.where((tbl) => tbl.type.equals(transactionType));
    }

    query.orderBy([(tbl) => OrderingTerm.desc(tbl.date)]);

    return await query.get();
  }

  // Generate summary data
  Future<Map<String, dynamic>> _generateSummaryData({
    required String accountId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final database = Get.find<AppDatabase>();
    
    final totalIncome = await database.getTotalIncomeForAccount(
      accountId,
      startDate: startDate,
      endDate: endDate,
    );
    
    final totalExpenses = await database.getTotalExpensesForAccount(
      accountId,
      startDate: startDate,
      endDate: endDate,
    );

    return {
      'account_id': accountId,
      'period_start': startDate?.toIso8601String(),
      'period_end': endDate?.toIso8601String(),
      'total_income': totalIncome,
      'total_expenses': totalExpenses,
      'net_amount': totalIncome - totalExpenses,
      'generated_at': DateTime.now().toIso8601String(),
    };
  }

  // Export to CSV
  Future<String?> _exportToCsv(List<PersonalTransaction> transactions, ExportType type) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final fileName = 'rekodi_${type.name}_${DateTime.now().millisecondsSinceEpoch}.csv';
      final file = File('${directory.path}/$fileName');

      List<List<dynamic>> csvData = [
        ['Date', 'Title', 'Description', 'Amount', 'Type', 'Category', 'Payment Method']
      ];

      for (final transaction in transactions) {
        csvData.add([
          transaction.date.toIso8601String().split('T')[0],
          transaction.title,
          transaction.description ?? '',
          transaction.amount,
          transaction.type,
          transaction.category,
          transaction.paymentMethod ?? '',
        ]);
      }

      final csvString = const ListToCsvConverter().convert(csvData);
      await file.writeAsString(csvString);

      return file.path;
    } catch (e) {
      print('Error exporting to CSV: $e');
      return null;
    }
  }

  // Export to JSON
  Future<String?> _exportToJson(
    List<PersonalTransaction> transactions,
    Map<String, dynamic> summaryData,
    ExportType type,
  ) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final fileName = 'rekodi_${type.name}_${DateTime.now().millisecondsSinceEpoch}.json';
      final file = File('${directory.path}/$fileName');

      Map<String, dynamic> exportData = {
        'export_info': {
          'type': type.name,
          'generated_at': DateTime.now().toIso8601String(),
          'app_version': '1.0.0',
        },
      };

      if (type == ExportType.summary || type == ExportType.full) {
        exportData['summary'] = summaryData;
      }

      if (type == ExportType.transactions || type == ExportType.full) {
        exportData['transactions'] = transactions.map((t) => {
          'id': t.id,
          'title': t.title,
          'description': t.description,
          'amount': t.amount,
          'type': t.type,
          'category': t.category,
          'date': t.date.toIso8601String(),
          'payment_method': t.paymentMethod,
          'created_at': t.createdAt.toIso8601String(),
        }).toList();
      }

      final jsonString = const JsonEncoder.withIndent('  ').convert(exportData);
      await file.writeAsString(jsonString);

      return file.path;
    } catch (e) {
      print('Error exporting to JSON: $e');
      return null;
    }
  }

  // Export to PDF
  Future<String?> _exportToPdf(
    List<PersonalTransaction> transactions,
    Map<String, dynamic> summaryData,
    ExportType type,
  ) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final fileName = 'rekodi_${type.name}_${DateTime.now().millisecondsSinceEpoch}.pdf';
      final file = File('${directory.path}/$fileName');

      final pdf = pw.Document();

      // Add summary page if needed
      if (type == ExportType.summary || type == ExportType.full) {
        pdf.addPage(
          pw.Page(
            pageFormat: PdfPageFormat.a4,
            build: (pw.Context context) {
              return pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Header(
                    level: 0,
                    child: pw.Text('Financial Summary Report'),
                  ),
                  pw.SizedBox(height: 20),
                  pw.Text('Total Income: \$${summaryData['total_income']?.toStringAsFixed(2) ?? '0.00'}'),
                  pw.Text('Total Expenses: \$${summaryData['total_expenses']?.toStringAsFixed(2) ?? '0.00'}'),
                  pw.Text('Net Amount: \$${summaryData['net_amount']?.toStringAsFixed(2) ?? '0.00'}'),
                  pw.SizedBox(height: 20),
                  pw.Text('Generated: ${DateTime.now().toString().split('.')[0]}'),
                ],
              );
            },
          ),
        );
      }

      // Add transactions if needed
      if (type == ExportType.transactions || type == ExportType.full) {
        pdf.addPage(
          pw.MultiPage(
            pageFormat: PdfPageFormat.a4,
            build: (pw.Context context) {
              return [
                pw.Header(
                  level: 0,
                  child: pw.Text('Transaction History'),
                ),
                pw.SizedBox(height: 20),
                pw.Table.fromTextArray(
                  headers: ['Date', 'Title', 'Amount', 'Type', 'Category'],
                  data: transactions.map((t) => [
                    t.date.toIso8601String().split('T')[0],
                    t.title,
                    '\$${t.amount.toStringAsFixed(2)}',
                    t.type,
                    t.category,
                  ]).toList(),
                ),
              ];
            },
          ),
        );
      }

      await file.writeAsBytes(await pdf.save());
      return file.path;
    } catch (e) {
      print('Error exporting to PDF: $e');
      return null;
    }
  }

  // Import from CSV
  Future<List<PersonalTransaction>> _importFromCsv(File file) async {
    final csvString = await file.readAsString();
    final csvData = const CsvToListConverter().convert(csvString);
    
    List<PersonalTransaction> transactions = [];
    
    // Skip header row
    for (int i = 1; i < csvData.length; i++) {
      final row = csvData[i];
      if (row.length >= 6) {
        try {
          final transaction = PersonalTransaction(
            id: (DateTime.now().millisecondsSinceEpoch + i).toString(),
            accountId: AccountService.to.currentAccountId,
            title: row[1].toString(),
            description: row[2].toString().isEmpty ? null : row[2].toString(),
            amount: double.parse(row[3].toString()),
            type: row[4].toString(),
            category: row[5].toString(),
            date: DateTime.parse(row[0].toString()),
            paymentMethod: row.length > 6 ? row[6].toString() : null,
            transactionCost: 0.0,
            isRecurring: false,
            isSynced: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );
          transactions.add(transaction);
        } catch (e) {
          print('Error parsing CSV row $i: $e');
        }
      }
    }
    
    return transactions;
  }

  // Import from JSON
  Future<List<PersonalTransaction>> _importFromJson(File file) async {
    final jsonString = await file.readAsString();
    final jsonData = json.decode(jsonString);
    
    List<PersonalTransaction> transactions = [];
    
    if (jsonData['transactions'] != null) {
      for (final transactionData in jsonData['transactions']) {
        try {
          final transaction = PersonalTransaction(
            id: (DateTime.now().millisecondsSinceEpoch + transactions.length).toString(),
            accountId: AccountService.to.currentAccountId,
            title: transactionData['title'],
            description: transactionData['description'],
            amount: transactionData['amount'].toDouble(),
            type: transactionData['type'],
            category: transactionData['category'],
            date: DateTime.parse(transactionData['date']),
            paymentMethod: transactionData['payment_method'],
            transactionCost: transactionData['transaction_cost']?.toDouble() ?? 0.0,
            isRecurring: false,
            isSynced: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );
          transactions.add(transaction);
        } catch (e) {
          print('Error parsing JSON transaction: $e');
        }
      }
    }
    
    return transactions;
  }

  // Get export formats
  List<String> get supportedExportFormats => ['CSV', 'JSON', 'PDF'];

  // Get export types
  List<String> get supportedExportTypes => ['Transactions', 'Summary', 'Full Export'];

  // Public method to export transactions to CSV
  Future<String?> exportTransactionsToCSV({
    DateTime? startDate,
    DateTime? endDate,
    String? category,
    String? transactionType,
  }) async {
    try {
      _isExporting.value = true;
      _exportProgress.value = 0.0;

      final accountId = AccountService.to.currentAccountId;
      if (accountId.isEmpty) {
        throw Exception('No active account found');
      }

      // Get filtered transactions
      final transactions = await _getFilteredTransactions(
        accountId: accountId,
        startDate: startDate,
        endDate: endDate,
        category: category,
        transactionType: transactionType,
      );

      _exportProgress.value = 0.5;

      // Export to CSV
      final filePath = await _exportToCsv(transactions, ExportType.transactions);

      _exportProgress.value = 1.0;

      return filePath;
    } catch (e) {
      print('Error exporting transactions to CSV: $e');
      return null;
    } finally {
      _isExporting.value = false;
    }
  }
}
