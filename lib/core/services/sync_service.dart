import 'package:get/get.dart';
import 'package:appwrite/appwrite.dart';
import '../config/appwrite_config.dart';
import 'appwrite_service.dart';
import 'auth_service.dart';
import 'account_service.dart';
import 'personal_transaction_service.dart';
import 'database_service.dart';

class SyncService extends GetxService {
  static SyncService get to => Get.find();

  // Lazy getters to avoid accessing services before they're initialized
  AppwriteService get _appwriteService => AppwriteService.to;
  AuthService get _authService => AuthService.to;
  AccountService get _accountService => AccountService.to;
  PersonalTransactionService get _transactionService => PersonalTransactionService.to;
  DatabaseService get _databaseService => DatabaseService.to;

  final RxBool _isSyncing = false.obs;
  final RxString _syncStatus = 'idle'.obs;
  final Rx<DateTime> _lastSyncTime = DateTime.now().obs;

  bool get isSyncing => _isSyncing.value;
  String get syncStatus => _syncStatus.value;
  DateTime get lastSyncTime => _lastSyncTime.value;

  @override
  Future<void> onInit() async {
    super.onInit();
    try {
      // Start periodic sync if user is authenticated
      if (_authService.isAuthenticated) {
        _startPeriodicSync();
      }
    } catch (e) {
      print('Error initializing SyncService: $e');
    }
  }

  /// Start periodic sync every 5 minutes
  void _startPeriodicSync() {
    // Cancel any existing timer
    _stopPeriodicSync();
    
    // Start new periodic sync
    ever(_authService.isAuthenticated.obs, (isAuthenticated) {
      if (isAuthenticated) {
        _scheduleNextSync();
      } else {
        _stopPeriodicSync();
      }
    });
  }

  void _stopPeriodicSync() {
    // Implementation for stopping periodic sync
    print('Periodic sync stopped');
  }

  void _scheduleNextSync() {
    Future.delayed(AppwriteConfig.syncInterval, () {
      if (_authService.isAuthenticated && _appwriteService.isOnline) {
        syncAll();
      }
      _scheduleNextSync();
    });
  }

  /// Sync all data (accounts, transactions, settings)
  Future<bool> syncAll() async {
    if (_isSyncing.value || !_authService.isAuthenticated || !_appwriteService.isOnline) {
      return false;
    }

    try {
      _isSyncing.value = true;
      _syncStatus.value = 'syncing';

      final userId = _authService.currentUser?.id;
      if (userId == null) return false;

      // Sync user profile
      await _syncUserProfile(userId);

      // Sync accounts
      await _syncAccounts(userId);

      // Sync transactions
      await _syncTransactions();

      // Update sync status
      await _updateSyncStatus();

      _syncStatus.value = 'completed';
      _lastSyncTime.value = DateTime.now();

      print('Full sync completed successfully');
      return true;

    } catch (e) {
      _syncStatus.value = 'error';
      print('Sync failed: $e');
      return false;
    } finally {
      _isSyncing.value = false;
    }
  }

  /// Sync user profile data
  Future<void> _syncUserProfile(String userId) async {
    try {
      // Check if user profile exists in Appwrite
      final userDoc = await _appwriteService.getDocument(
        collectionId: AppwriteConfig.usersCollectionId,
        documentId: userId,
      );

      if (userDoc == null) {
        // Create user profile in Appwrite
        await _appwriteService.createDocument(
          collectionId: AppwriteConfig.usersCollectionId,
          documentId: userId,
          data: {
            'name': _authService.currentUser?.name ?? '',
            'email': _authService.currentUser?.email ?? '',
            'emailVerification': _authService.currentUser?.emailVerification ?? false,
            'phoneVerification': _authService.currentUser?.phoneVerification ?? false,
            'status': _authService.currentUser?.status ?? 'active',
            'createdAt': DateTime.now().toIso8601String(),
            'updatedAt': DateTime.now().toIso8601String(),
          },
        );
      }
    } catch (e) {
      print('Error syncing user profile: $e');
    }
  }

  /// Sync accounts data
  Future<void> _syncAccounts(String userId) async {
    try {
      // Get local accounts
      final localAccounts = await _databaseService.database.getAllAccounts();

      // Sync each account to Appwrite
      for (final account in localAccounts) {
        await _syncAccountToAppwrite(account, userId);
      }

      // Load accounts from Appwrite
      await _loadAccountsFromAppwrite(userId);

    } catch (e) {
      print('Error syncing accounts: $e');
    }
  }

  /// Sync single account to Appwrite
  Future<void> _syncAccountToAppwrite(dynamic account, String userId) async {
    try {
      final accountData = {
        'userId': userId,
        'name': account.name,
        'email': account.email,
        'accountType': account.accountType,
        'isActive': account.isActive,
        'createdAt': account.createdAt.toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
      };

      // Check if account exists
      final existingDoc = await _appwriteService.getDocument(
        collectionId: AppwriteConfig.accountsCollectionId,
        documentId: account.id,
      );

      if (existingDoc == null) {
        // Create new account
        await _appwriteService.createDocument(
          collectionId: AppwriteConfig.accountsCollectionId,
          documentId: account.id,
          data: accountData,
        );
      } else {
        // Update existing account
        await _appwriteService.updateDocument(
          collectionId: AppwriteConfig.accountsCollectionId,
          documentId: account.id,
          data: accountData,
        );
      }
    } catch (e) {
      print('Error syncing account to Appwrite: $e');
    }
  }

  /// Load accounts from Appwrite
  Future<void> _loadAccountsFromAppwrite(String userId) async {
    try {
      final response = await _appwriteService.listDocuments(
        collectionId: AppwriteConfig.accountsCollectionId,
        queries: [
          Query.equal('userId', userId),
        ],
      );

      if (response != null) {
        for (final doc in response.documents) {
          await _syncAccountFromAppwrite(doc.data);
        }
      }
    } catch (e) {
      print('Error loading accounts from Appwrite: $e');
    }
  }

  /// Sync account from Appwrite to local database
  Future<void> _syncAccountFromAppwrite(Map<String, dynamic> data) async {
    try {
      // Check if account exists locally
      final existingAccount = await _databaseService.database.getAccountById(data['id']);

      if (existingAccount == null) {
        // Create new local account
        await _databaseService.database.createAccount(
          id: data['id'],
          name: data['name'],
          email: data['email'],
          accountType: data['accountType'],
          makeActive: data['isActive'] ?? false,
        );
      }
      // Note: We don't update existing accounts to avoid conflicts
      // In a production app, you'd implement proper conflict resolution
    } catch (e) {
      print('Error syncing account from Appwrite: $e');
    }
  }

  /// Sync transactions
  Future<void> _syncTransactions() async {
    try {
      // Sync local transactions to Appwrite
      await _transactionService.syncAllTransactionsToAppwrite();

      // Load transactions from Appwrite
      await _transactionService.loadTransactionsFromAppwrite();

    } catch (e) {
      print('Error syncing transactions: $e');
    }
  }

  /// Update sync status in local database
  Future<void> _updateSyncStatus() async {
    try {
      final accountId = _accountService.currentAccountId;
      if (accountId.isNotEmpty) {
        await _databaseService.database.updateSyncStatus(
          accountId,
          lastSyncAt: DateTime.now(),
          hasUnsyncedChanges: false,
        );
      }
    } catch (e) {
      print('Error updating sync status: $e');
    }
  }

  /// Force sync now
  Future<bool> forceSyncNow() async {
    if (!_appwriteService.isOnline) {
      Get.snackbar(
        'Offline',
        'Cannot sync while offline. Please check your internet connection.',
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    }

    final success = await syncAll();
    
    if (success) {
      Get.snackbar(
        'Sync Complete',
        'All data has been synchronized successfully.',
        snackPosition: SnackPosition.BOTTOM,
      );
    } else {
      Get.snackbar(
        'Sync Failed',
        'Failed to synchronize data. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
      );
    }

    return success;
  }

  /// Check if there are unsynced changes
  Future<bool> hasUnsyncedChanges() async {
    try {
      final accountId = _accountService.currentAccountId;
      if (accountId.isEmpty) return false;

      final syncStatus = await (_databaseService.database.select(_databaseService.database.syncStatus)
          ..where((tbl) => tbl.accountId.equals(accountId)))
          .getSingleOrNull();

      return syncStatus?.hasUnsyncedChanges ?? false;
    } catch (e) {
      print('Error checking unsynced changes: $e');
      return false;
    }
  }
}
