import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'account_service.dart';
import 'personal_transaction_service.dart';

enum NotificationType {
  budgetAlert,
  recurringReminder,
  expenseLimit,
  incomeReceived,
  weeklyReport,
  monthlyReport,
  goalAchieved,
  billReminder,
}

class NotificationService extends GetxService {
  static NotificationService get to => Get.find();

  final GetStorage _storage = GetStorage();
  final FlutterLocalNotificationsPlugin _notifications = FlutterLocalNotificationsPlugin();

  final RxBool _isInitialized = false.obs;
  final RxBool _notificationsEnabled = true.obs;
  final RxMap<NotificationType, bool> _notificationSettings = <NotificationType, bool>{}.obs;

  bool get isInitialized => _isInitialized.value;
  bool get notificationsEnabled => _notificationsEnabled.value;
  Map<NotificationType, bool> get notificationSettings => _notificationSettings;

  @override
  Future<void> onInit() async {
    super.onInit();
    await _initializeNotifications();
    await _loadNotificationSettings();
  }

  // Initialize notification system (simplified)
  Future<void> _initializeNotifications() async {
    try {
      _isInitialized.value = true;
      print('Notification service initialized (simplified mode)');
    } catch (e) {
      print('Error initializing notifications: $e');
    }
  }

  // Load notification settings
  Future<void> _loadNotificationSettings() async {
    try {
      final accountId = AccountService.to.currentAccountId;
      if (accountId.isNotEmpty) {
        _notificationsEnabled.value = _storage.read('notifications_enabled_$accountId') ?? true;
        
        // Load individual notification type settings
        for (final type in NotificationType.values) {
          final key = 'notification_${type.name}_$accountId';
          _notificationSettings[type] = _storage.read(key) ?? true;
        }
      }
    } catch (e) {
      print('Error loading notification settings: $e');
    }
  }

  // Save notification settings
  Future<void> _saveNotificationSettings() async {
    try {
      final accountId = AccountService.to.currentAccountId;
      if (accountId.isNotEmpty) {
        _storage.write('notifications_enabled_$accountId', _notificationsEnabled.value);
        
        for (final entry in _notificationSettings.entries) {
          final key = 'notification_${entry.key.name}_$accountId';
          _storage.write(key, entry.value);
        }
      }
    } catch (e) {
      print('Error saving notification settings: $e');
    }
  }

  // Handle notification tap
  void _onNotificationTapped(NotificationResponse response) {
    try {
      if (response.payload != null) {
        final payload = json.decode(response.payload!);
        final type = payload['type'] as String?;
        final data = payload['data'] as Map<String, dynamic>?;

        // Navigate based on notification type
        switch (type) {
          case 'budget_alert':
          case 'expense_limit':
            Get.toNamed('/analytics');
            break;
          case 'recurring_reminder':
          case 'bill_reminder':
            Get.toNamed('/transactions/add');
            break;
          case 'weekly_report':
          case 'monthly_report':
            Get.toNamed('/analytics');
            break;
          default:
            Get.toNamed('/dashboard');
        }
      }
    } catch (e) {
      print('Error handling notification tap: $e');
    }
  }

  // Show immediate notification
  Future<void> showNotification({
    required String title,
    required String body,
    NotificationType? type,
    Map<String, dynamic>? data,
  }) async {
    if (!_notificationsEnabled.value || !_isInitialized.value) return;
    
    if (type != null && _notificationSettings[type] == false) return;

    try {
      const androidDetails = AndroidNotificationDetails(
        'rekodi_general',
        'General Notifications',
        channelDescription: 'General app notifications',
        importance: Importance.high,
        priority: Priority.high,
        icon: '@mipmap/ic_launcher',
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const details = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      final payload = json.encode({
        'type': type?.name,
        'data': data,
      });

      await _notifications.show(
        DateTime.now().millisecondsSinceEpoch % 100000,
        title,
        body,
        details,
        payload: payload,
      );
    } catch (e) {
      print('Error showing notification: $e');
    }
  }

  // Schedule notification
  Future<void> scheduleNotification({
    required String title,
    required String body,
    required DateTime scheduledDate,
    NotificationType? type,
    Map<String, dynamic>? data,
    int? id,
  }) async {
    if (!_notificationsEnabled.value || !_isInitialized.value) return;
    
    if (type != null && _notificationSettings[type] == false) return;

    try {
      const androidDetails = AndroidNotificationDetails(
        'rekodi_scheduled',
        'Scheduled Notifications',
        channelDescription: 'Scheduled reminders and alerts',
        importance: Importance.high,
        priority: Priority.high,
        icon: '@mipmap/ic_launcher',
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const details = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      final payload = json.encode({
        'type': type?.name,
        'data': data,
      });

      await _notifications.zonedSchedule(
        id ?? DateTime.now().millisecondsSinceEpoch % 100000,
        title,
        body,
        tz.TZDateTime.from(scheduledDate, tz.local),
        details,
        payload: payload,
        uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
      );
    } catch (e) {
      print('Error scheduling notification: $e');
    }
  }

  // Budget alert notification
  Future<void> showBudgetAlert({
    required String category,
    required double spent,
    required double budget,
  }) async {
    final percentage = (spent / budget * 100).round();
    
    await showNotification(
      title: 'Budget Alert: $category',
      body: 'You\'ve spent $percentage% of your budget (\$${spent.toStringAsFixed(2)} of \$${budget.toStringAsFixed(2)})',
      type: NotificationType.budgetAlert,
      data: {
        'category': category,
        'spent': spent,
        'budget': budget,
      },
    );
  }

  // Expense limit notification
  Future<void> showExpenseLimitAlert({
    required double dailyLimit,
    required double todaySpent,
  }) async {
    await showNotification(
      title: 'Daily Expense Limit Reached',
      body: 'You\'ve spent \$${todaySpent.toStringAsFixed(2)} today, reaching your \$${dailyLimit.toStringAsFixed(2)} limit',
      type: NotificationType.expenseLimit,
      data: {
        'daily_limit': dailyLimit,
        'today_spent': todaySpent,
      },
    );
  }

  // Recurring transaction reminder
  Future<void> scheduleRecurringReminder({
    required String title,
    required double amount,
    required DateTime dueDate,
    required String category,
  }) async {
    await scheduleNotification(
      title: 'Recurring Transaction Due',
      body: '$title - \$${amount.toStringAsFixed(2)} due today',
      scheduledDate: dueDate,
      type: NotificationType.recurringReminder,
      data: {
        'title': title,
        'amount': amount,
        'category': category,
      },
    );
  }

  // Bill reminder notification
  Future<void> scheduleBillReminder({
    required String billName,
    required double amount,
    required DateTime dueDate,
  }) async {
    // Schedule 3 days before
    final reminderDate = dueDate.subtract(const Duration(days: 3));
    
    await scheduleNotification(
      title: 'Bill Reminder',
      body: '$billName (\$${amount.toStringAsFixed(2)}) is due in 3 days',
      scheduledDate: reminderDate,
      type: NotificationType.billReminder,
      data: {
        'bill_name': billName,
        'amount': amount,
        'due_date': dueDate.toIso8601String(),
      },
    );
  }

  // Weekly report notification
  Future<void> scheduleWeeklyReport() async {
    final now = DateTime.now();
    final nextSunday = now.add(Duration(days: 7 - now.weekday));
    final reportTime = DateTime(nextSunday.year, nextSunday.month, nextSunday.day, 9, 0);

    await scheduleNotification(
      title: 'Weekly Financial Report',
      body: 'Your weekly spending summary is ready to view',
      scheduledDate: reportTime,
      type: NotificationType.weeklyReport,
      id: 1001, // Fixed ID for weekly reports
    );
  }

  // Monthly report notification
  Future<void> scheduleMonthlyReport() async {
    final now = DateTime.now();
    final nextMonth = DateTime(now.year, now.month + 1, 1, 9, 0);

    await scheduleNotification(
      title: 'Monthly Financial Report',
      body: 'Your monthly financial summary is ready to view',
      scheduledDate: nextMonth,
      type: NotificationType.monthlyReport,
      id: 1002, // Fixed ID for monthly reports
    );
  }

  // Goal achieved notification
  Future<void> showGoalAchievedNotification({
    required String goalName,
    required double targetAmount,
  }) async {
    await showNotification(
      title: 'Goal Achieved! 🎉',
      body: 'Congratulations! You\'ve reached your $goalName goal of \$${targetAmount.toStringAsFixed(2)}',
      type: NotificationType.goalAchieved,
      data: {
        'goal_name': goalName,
        'target_amount': targetAmount,
      },
    );
  }

  // Income received notification
  Future<void> showIncomeReceivedNotification({
    required String source,
    required double amount,
  }) async {
    await showNotification(
      title: 'Income Received',
      body: '\$${amount.toStringAsFixed(2)} received from $source',
      type: NotificationType.incomeReceived,
      data: {
        'source': source,
        'amount': amount,
      },
    );
  }

  // Enable/disable notifications
  Future<void> setNotificationsEnabled(bool enabled) async {
    _notificationsEnabled.value = enabled;
    await _saveNotificationSettings();
    
    if (!enabled) {
      await _notifications.cancelAll();
    } else {
      // Reschedule recurring notifications
      await scheduleWeeklyReport();
      await scheduleMonthlyReport();
    }
  }

  // Enable/disable specific notification type
  Future<void> setNotificationTypeEnabled(NotificationType type, bool enabled) async {
    _notificationSettings[type] = enabled;
    await _saveNotificationSettings();
  }

  // Cancel all notifications
  Future<void> cancelAllNotifications() async {
    await _notifications.cancelAll();
  }

  // Cancel specific notification
  Future<void> cancelNotification(int id) async {
    await _notifications.cancel(id);
  }

  // Get pending notifications
  Future<List<PendingNotificationRequest>> getPendingNotifications() async {
    return await _notifications.pendingNotificationRequests();
  }

  // Check if notifications are enabled for type
  bool isNotificationTypeEnabled(NotificationType type) {
    return _notificationsEnabled.value && (_notificationSettings[type] ?? true);
  }

  // Get notification type display name
  String getNotificationTypeDisplayName(NotificationType type) {
    switch (type) {
      case NotificationType.budgetAlert:
        return 'Budget Alerts';
      case NotificationType.recurringReminder:
        return 'Recurring Reminders';
      case NotificationType.expenseLimit:
        return 'Expense Limits';
      case NotificationType.incomeReceived:
        return 'Income Notifications';
      case NotificationType.weeklyReport:
        return 'Weekly Reports';
      case NotificationType.monthlyReport:
        return 'Monthly Reports';
      case NotificationType.goalAchieved:
        return 'Goal Achievements';
      case NotificationType.billReminder:
        return 'Bill Reminders';
    }
  }

  // Get notification type description
  String getNotificationTypeDescription(NotificationType type) {
    switch (type) {
      case NotificationType.budgetAlert:
        return 'Get notified when you approach or exceed budget limits';
      case NotificationType.recurringReminder:
        return 'Reminders for recurring transactions and subscriptions';
      case NotificationType.expenseLimit:
        return 'Alerts when daily or monthly spending limits are reached';
      case NotificationType.incomeReceived:
        return 'Notifications when income is recorded';
      case NotificationType.weeklyReport:
        return 'Weekly summary of your financial activity';
      case NotificationType.monthlyReport:
        return 'Monthly financial reports and insights';
      case NotificationType.goalAchieved:
        return 'Celebrate when you achieve your financial goals';
      case NotificationType.billReminder:
        return 'Reminders for upcoming bill payments';
    }
  }
}
