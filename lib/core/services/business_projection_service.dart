import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import '../models/business_projection.dart';
import '../models/business_product.dart';
import 'account_service.dart';
import 'business_product_service.dart';

class BusinessProjectionService extends GetxService {
  static BusinessProjectionService get to => Get.find();

  final GetStorage _storage = GetStorage();
  final RxList<BusinessProjection> _projections = <BusinessProjection>[].obs;
  final RxBool _isLoading = false.obs;

  // Getters
  List<BusinessProjection> get projections => _projections;
  bool get isLoading => _isLoading.value;

  // Services
  AccountService get _accountService => AccountService.to;
  BusinessProductService get _productService => BusinessProductService.to;

  @override
  Future<void> onInit() async {
    super.onInit();
    await _loadProjections();
    _initializeSampleProjections();
  }

  /// Load projections from storage
  Future<void> _loadProjections() async {
    try {
      _isLoading.value = true;
      final accountId = _accountService.currentAccountId;
      if (accountId.isEmpty) return;

      final projectionsData = _storage.read('business_projections_$accountId') as List?;
      if (projectionsData != null) {
        _projections.value = projectionsData
            .map((data) => BusinessProjection.fromJson(Map<String, dynamic>.from(data)))
            .toList();
      }
    } catch (e) {
      print('Error loading projections: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// Save projections to storage
  Future<void> _saveProjections() async {
    try {
      final accountId = _accountService.currentAccountId;
      if (accountId.isEmpty) return;

      final projectionsData = _projections.map((p) => p.toJson()).toList();
      await _storage.write('business_projections_$accountId', projectionsData);
    } catch (e) {
      print('Error saving projections: $e');
    }
  }

  /// Add a new projection
  Future<bool> addProjection({
    required String name,
    String? description,
    required ProjectionPeriod period,
    required double targetSales,
    required double targetProfit,
    required double targetExpenses,
    Map<String, double>? categoryTargets,
  }) async {
    try {
      _isLoading.value = true;
      
      final accountId = _accountService.currentAccountId;
      if (accountId.isEmpty) return false;

      // Check if projection already exists for this period
      final existingProjection = _projections.firstWhereOrNull(
        (projection) => projection.period == period && projection.isActive,
      );
      
      if (existingProjection != null) {
        Get.snackbar(
          'Projection Exists',
          'An active projection for this period already exists',
          backgroundColor: Get.theme.colorScheme.error,
          colorText: Get.theme.colorScheme.onError,
        );
        return false;
      }

      final now = DateTime.now();
      final endDate = now.add(period.duration);

      final newProjection = BusinessProjection(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        accountId: accountId,
        name: name,
        description: description,
        period: period,
        targetSales: targetSales,
        targetProfit: targetProfit,
        targetExpenses: targetExpenses,
        startDate: now,
        endDate: endDate,
        categoryTargets: categoryTargets ?? {},
        createdAt: now,
      );

      _projections.add(newProjection);
      await _saveProjections();
      
      Get.snackbar(
        'Success',
        'Growth projection created successfully',
        backgroundColor: Get.theme.colorScheme.primary,
        colorText: Get.theme.colorScheme.onPrimary,
      );
      
      return true;
    } catch (e) {
      print('Error adding projection: $e');
      Get.snackbar(
        'Error',
        'Failed to create projection',
        backgroundColor: Get.theme.colorScheme.error,
        colorText: Get.theme.colorScheme.onError,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Update an existing projection
  Future<bool> updateProjection({
    required String id,
    String? name,
    String? description,
    double? targetSales,
    double? targetProfit,
    double? targetExpenses,
    Map<String, double>? categoryTargets,
  }) async {
    try {
      _isLoading.value = true;
      
      final index = _projections.indexWhere((projection) => projection.id == id);
      if (index == -1) {
        Get.snackbar(
          'Error',
          'Projection not found',
          backgroundColor: Get.theme.colorScheme.error,
          colorText: Get.theme.colorScheme.onError,
        );
        return false;
      }

      final projection = _projections[index];
      _projections[index] = projection.copyWith(
        name: name,
        description: description,
        targetSales: targetSales,
        targetProfit: targetProfit,
        targetExpenses: targetExpenses,
        categoryTargets: categoryTargets,
        updatedAt: DateTime.now(),
      );

      await _saveProjections();
      
      Get.snackbar(
        'Success',
        'Projection updated successfully',
        backgroundColor: Get.theme.colorScheme.primary,
        colorText: Get.theme.colorScheme.onPrimary,
      );
      
      return true;
    } catch (e) {
      print('Error updating projection: $e');
      Get.snackbar(
        'Error',
        'Failed to update projection',
        backgroundColor: Get.theme.colorScheme.error,
        colorText: Get.theme.colorScheme.onError,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Delete a projection
  Future<bool> deleteProjection(String id) async {
    try {
      _isLoading.value = true;
      
      _projections.removeWhere((projection) => projection.id == id);
      await _saveProjections();
      
      Get.snackbar(
        'Success',
        'Projection deleted successfully',
        backgroundColor: Get.theme.colorScheme.primary,
        colorText: Get.theme.colorScheme.onPrimary,
      );
      
      return true;
    } catch (e) {
      print('Error deleting projection: $e');
      Get.snackbar(
        'Error',
        'Failed to delete projection',
        backgroundColor: Get.theme.colorScheme.error,
        colorText: Get.theme.colorScheme.onError,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Get projections by period
  List<BusinessProjection> getProjectionsByPeriod(ProjectionPeriod period) {
    return _projections.where((projection) => projection.period == period).toList();
  }

  /// Get active projections
  List<BusinessProjection> getActiveProjections() {
    return _projections.where((projection) => projection.isActive).toList();
  }

  /// Update actual values based on business transactions
  Future<void> updateActualValues() async {
    try {
      final transactions = _productService.transactions;
      
      for (int i = 0; i < _projections.length; i++) {
        final projection = _projections[i];
        if (!projection.isActive) continue;

        // Filter transactions within projection period
        final periodTransactions = transactions.where((t) =>
            t.transactionDate.isAfter(projection.startDate) &&
            t.transactionDate.isBefore(projection.endDate)
        ).toList();

        // Calculate actual sales
        final actualSales = periodTransactions
            .where((t) => t.isSale)
            .fold(0.0, (sum, t) => sum + t.totalAmount);

        // Calculate actual expenses (purchases)
        final actualExpenses = periodTransactions
            .where((t) => t.isPurchase)
            .fold(0.0, (sum, t) => sum + t.totalAmount);

        // Calculate actual profit (sales - expenses)
        final actualProfit = actualSales - actualExpenses;

        // Update projection
        _projections[i] = projection.copyWith(
          actualSales: actualSales,
          actualExpenses: actualExpenses,
          actualProfit: actualProfit,
          updatedAt: DateTime.now(),
        );
      }

      await _saveProjections();
    } catch (e) {
      print('Error updating actual values: $e');
    }
  }

  /// Get total target sales for a period
  double getTotalTargetSales(ProjectionPeriod period) {
    return getProjectionsByPeriod(period)
        .fold(0.0, (sum, projection) => sum + projection.targetSales);
  }

  /// Get total actual sales for a period
  double getTotalActualSales(ProjectionPeriod period) {
    return getProjectionsByPeriod(period)
        .fold(0.0, (sum, projection) => sum + projection.actualSales);
  }

  /// Initialize sample projections for demo
  void _initializeSampleProjections() {
    final currentAccountId = _accountService.currentAccountId;
    if (currentAccountId.isEmpty || _projections.isNotEmpty) return;

    final now = DateTime.now();
    _projections.value = [
      BusinessProjection(
        id: '1',
        accountId: currentAccountId,
        name: 'Q1 Growth Target',
        description: 'First quarter growth projection',
        period: ProjectionPeriod.quarterly,
        targetSales: 50000.0,
        actualSales: 35000.0,
        targetProfit: 15000.0,
        actualProfit: 10500.0,
        targetExpenses: 35000.0,
        actualExpenses: 24500.0,
        startDate: now.subtract(const Duration(days: 60)),
        endDate: now.add(const Duration(days: 30)),
        categoryTargets: {
          'Electronics': 20000.0,
          'Clothing': 15000.0,
          'Services': 15000.0,
        },
        categoryActuals: {
          'Electronics': 14000.0,
          'Clothing': 11000.0,
          'Services': 10000.0,
        },
        createdAt: now.subtract(const Duration(days: 60)),
      ),
      BusinessProjection(
        id: '2',
        accountId: currentAccountId,
        name: 'Monthly Target',
        description: 'Current month sales target',
        period: ProjectionPeriod.monthly,
        targetSales: 15000.0,
        actualSales: 12000.0,
        targetProfit: 4500.0,
        actualProfit: 3600.0,
        targetExpenses: 10500.0,
        actualExpenses: 8400.0,
        startDate: now.subtract(const Duration(days: 20)),
        endDate: now.add(const Duration(days: 10)),
        createdAt: now.subtract(const Duration(days: 20)),
      ),
    ];
  }

  /// Switch account context
  Future<void> switchAccount(String accountId) async {
    await _loadProjections();
  }
}
