import 'dart:async';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import '../models/transaction.dart';
import '../models/sms_transaction.dart';
import '../models/classification_rule.dart';
import '../models/confidence_review.dart';
import 'transaction_classifier_service.dart';
import 'sms_parser_service.dart';
import 'local_storage_service.dart';



class TransactionConfidenceService extends GetxService {
  static TransactionConfidenceService get to => Get.find();
  
  final GetStorage _storage = GetStorage();
  final LocalStorageService _localStorageService = Get.find();
  final TransactionClassifierService _classifierService = Get.find();
  final SmsParserService _smsParserService = Get.find();
  
  final RxList<ConfidenceReview> _pendingReviews = <ConfidenceReview>[].obs;
  final RxList<ConfidenceReview> _reviewHistory = <ConfidenceReview>[].obs;
  final RxDouble _confidenceThreshold = 0.7.obs;
  final RxBool _autoAcceptHighConfidence = true.obs;
  final RxBool _autoRejectLowConfidence = false.obs;
  final RxInt _maxPendingReviews = 50.obs;
  final RxInt _totalReviewsProcessed = 0.obs;
  final RxInt _correctPredictions = 0.obs;
  final RxDouble _systemAccuracy = 0.0.obs;
  
  // Getters
  List<ConfidenceReview> get pendingReviews => _pendingReviews;
  List<ConfidenceReview> get reviewHistory => _reviewHistory;
  double get confidenceThreshold => _confidenceThreshold.value;
  bool get autoAcceptHighConfidence => _autoAcceptHighConfidence.value;
  bool get autoRejectLowConfidence => _autoRejectLowConfidence.value;
  int get maxPendingReviews => _maxPendingReviews.value;
  int get totalReviewsProcessed => _totalReviewsProcessed.value;
  int get correctPredictions => _correctPredictions.value;
  double get systemAccuracy => _systemAccuracy.value;

  @override
  void onInit() {
    super.onInit();
    _loadSettings();
    _loadReviews();
    _calculateSystemAccuracy();
  }

  /// Load settings from storage
  void _loadSettings() {
    _confidenceThreshold.value = _storage.read('confidence_threshold') ?? 0.7;
    _autoAcceptHighConfidence.value = _storage.read('auto_accept_high_confidence') ?? true;
    _autoRejectLowConfidence.value = _storage.read('auto_reject_low_confidence') ?? false;
    _maxPendingReviews.value = _storage.read('max_pending_reviews') ?? 50;
    _totalReviewsProcessed.value = _storage.read('total_reviews_processed') ?? 0;
    _correctPredictions.value = _storage.read('correct_predictions') ?? 0;
  }

  /// Save settings to storage
  void _saveSettings() {
    _storage.write('confidence_threshold', _confidenceThreshold.value);
    _storage.write('auto_accept_high_confidence', _autoAcceptHighConfidence.value);
    _storage.write('auto_reject_low_confidence', _autoRejectLowConfidence.value);
    _storage.write('max_pending_reviews', _maxPendingReviews.value);
    _storage.write('total_reviews_processed', _totalReviewsProcessed.value);
    _storage.write('correct_predictions', _correctPredictions.value);
  }

  /// Load reviews from storage
  void _loadReviews() {
    final pendingData = _storage.read<List>('pending_reviews');
    if (pendingData != null) {
      _pendingReviews.assignAll(
        pendingData.map((data) => ConfidenceReview.fromJson(data)).toList(),
      );
    }

    final historyData = _storage.read<List>('review_history');
    if (historyData != null) {
      _reviewHistory.assignAll(
        historyData.map((data) => ConfidenceReview.fromJson(data)).toList(),
      );
    }
  }

  /// Save reviews to storage
  void _saveReviews() {
    _storage.write('pending_reviews', _pendingReviews.map((r) => r.toJson()).toList());
    _storage.write('review_history', _reviewHistory.map((r) => r.toJson()).toList());
  }

  /// Process a transaction for confidence review
  Future<bool> processTransactionForReview({
    required Transaction? transaction,
    required SmsTransaction? smsTransaction,
    required TransactionClassification classification,
  }) async {
    if (transaction == null && smsTransaction == null) return false;

    final confidence = classification.confidence;
    final confidenceLevel = _getConfidenceLevel(confidence);
    
    // Auto-accept high confidence transactions
    if (_autoAcceptHighConfidence.value && confidence >= 0.9) {
      await _autoAcceptTransaction(transaction, smsTransaction, classification);
      return true;
    }
    
    // Auto-reject very low confidence transactions
    if (_autoRejectLowConfidence.value && confidence < 0.3) {
      await _autoRejectTransaction(transaction, smsTransaction, classification);
      return false;
    }
    
    // Add to review queue if below threshold
    if (confidence < _confidenceThreshold.value) {
      await _addToReviewQueue(transaction, smsTransaction, classification);
      return false;
    }
    
    // Auto-accept if above threshold
    await _autoAcceptTransaction(transaction, smsTransaction, classification);
    return true;
  }

  /// Add transaction to review queue
  Future<void> _addToReviewQueue(
    Transaction? transaction,
    SmsTransaction? smsTransaction,
    TransactionClassification classification,
  ) async {
    // Remove oldest reviews if queue is full
    while (_pendingReviews.length >= _maxPendingReviews.value) {
      _pendingReviews.removeAt(0);
    }

    final review = ConfidenceReview(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      transactionId: transaction?.id ?? smsTransaction?.id ?? '',
      originalTransaction: transaction,
      originalSmsTransaction: smsTransaction,
      classification: classification,
      confidenceLevel: _getConfidenceLevel(classification.confidence),
      createdAt: DateTime.now(),
      status: ReviewStatus.pending,
    );

    _pendingReviews.add(review);
    _saveReviews();
    
    // Notify user about new review
    _notifyNewReview(review);
  }

  /// Auto-accept high confidence transaction
  Future<void> _autoAcceptTransaction(
    Transaction? transaction,
    SmsTransaction? smsTransaction,
    TransactionClassification classification,
  ) async {
    final review = ConfidenceReview(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      transactionId: transaction?.id ?? smsTransaction?.id ?? '',
      originalTransaction: transaction,
      originalSmsTransaction: smsTransaction,
      classification: classification,
      confidenceLevel: _getConfidenceLevel(classification.confidence),
      createdAt: DateTime.now(),
      status: ReviewStatus.autoAccepted,
      reviewAction: ReviewAction.accept,
      reviewedAt: DateTime.now(),
    );

    _reviewHistory.add(review);
    _totalReviewsProcessed.value++;
    _correctPredictions.value++; // Assume auto-accepted are correct
    _calculateSystemAccuracy();
    _saveSettings();
    _saveReviews();
  }

  /// Auto-reject low confidence transaction
  Future<void> _autoRejectTransaction(
    Transaction? transaction,
    SmsTransaction? smsTransaction,
    TransactionClassification classification,
  ) async {
    final review = ConfidenceReview(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      transactionId: transaction?.id ?? smsTransaction?.id ?? '',
      originalTransaction: transaction,
      originalSmsTransaction: smsTransaction,
      classification: classification,
      confidenceLevel: _getConfidenceLevel(classification.confidence),
      createdAt: DateTime.now(),
      status: ReviewStatus.autoRejected,
      reviewAction: ReviewAction.reject,
      reviewedAt: DateTime.now(),
    );

    _reviewHistory.add(review);
    _totalReviewsProcessed.value++;
    _saveSettings();
    _saveReviews();
  }

  /// Review a pending transaction
  Future<void> reviewTransaction({
    required String reviewId,
    required ReviewAction action,
    String? correctedCategory,
    TransactionType? correctedType,
    double? correctedAmount,
    String? notes,
  }) async {
    final reviewIndex = _pendingReviews.indexWhere((r) => r.id == reviewId);
    if (reviewIndex == -1) return;

    final review = _pendingReviews[reviewIndex];
    final wasCorrect = action == ReviewAction.accept;
    
    // Update review
    final updatedReview = review.copyWith(
      status: ReviewStatus.reviewed,
      reviewAction: action,
      reviewedAt: DateTime.now(),
      correctedCategory: correctedCategory,
      correctedType: correctedType,
      correctedAmount: correctedAmount,
      reviewNotes: notes,
    );

    // Remove from pending and add to history
    _pendingReviews.removeAt(reviewIndex);
    _reviewHistory.add(updatedReview);

    // Update statistics
    _totalReviewsProcessed.value++;
    if (wasCorrect) {
      _correctPredictions.value++;
    }

    // Train classifier with feedback
    if (action == ReviewAction.accept || action == ReviewAction.modify) {
      await _trainClassifierFromReview(updatedReview);
    }

    _calculateSystemAccuracy();
    _saveSettings();
    _saveReviews();
  }

  /// Train classifier from review feedback
  Future<void> _trainClassifierFromReview(ConfidenceReview review) async {
    final features = <String, dynamic>{};
    
    if (review.originalTransaction != null) {
      final transaction = review.originalTransaction!;
      features.addAll({
        'amount': review.correctedAmount ?? transaction.amount,
        'description': transaction.description,
        'category': transaction.category,
        'type': transaction.type.name,
      });
    } else if (review.originalSmsTransaction != null) {
      final smsTransaction = review.originalSmsTransaction!;
      features.addAll({
        'amount': review.correctedAmount ?? smsTransaction.amount,
        'description': smsTransaction.description,
        'category': smsTransaction.category,
        'type': smsTransaction.transactionType ?? 'unknown',
        'sender': smsTransaction.sender,
        'merchant': smsTransaction.merchantName ?? '',
      });
    }

    _classifierService.trainWithFeedback(
      transactionId: review.transactionId,
      correctCategory: review.correctedCategory ?? review.classification.category,
      correctType: review.correctedType ?? review.classification.suggestedType,
      features: features,
      wasCorrect: review.reviewAction == ReviewAction.accept,
    );
  }

  /// Get confidence level from confidence score
  ConfidenceLevel _getConfidenceLevel(double confidence) {
    if (confidence >= 0.9) return ConfidenceLevel.veryHigh;
    if (confidence >= 0.7) return ConfidenceLevel.high;
    if (confidence >= 0.5) return ConfidenceLevel.medium;
    if (confidence >= 0.3) return ConfidenceLevel.low;
    return ConfidenceLevel.veryLow;
  }

  /// Calculate system accuracy
  void _calculateSystemAccuracy() {
    if (_totalReviewsProcessed.value > 0) {
      _systemAccuracy.value = _correctPredictions.value / _totalReviewsProcessed.value;
    }
  }

  /// Notify user about new review
  void _notifyNewReview(ConfidenceReview review) {
    Get.snackbar(
      'Review Required',
      'New transaction needs your review (${(review.classification.confidence * 100).toInt()}% confidence)',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 3),
    );
  }

  /// Update confidence settings
  void updateConfidenceSettings({
    double? confidenceThreshold,
    bool? autoAcceptHighConfidence,
    bool? autoRejectLowConfidence,
    int? maxPendingReviews,
  }) {
    if (confidenceThreshold != null) {
      _confidenceThreshold.value = confidenceThreshold;
    }
    if (autoAcceptHighConfidence != null) {
      _autoAcceptHighConfidence.value = autoAcceptHighConfidence;
    }
    if (autoRejectLowConfidence != null) {
      _autoRejectLowConfidence.value = autoRejectLowConfidence;
    }
    if (maxPendingReviews != null) {
      _maxPendingReviews.value = maxPendingReviews;
    }
    
    _saveSettings();
  }

  /// Get reviews by confidence level
  List<ConfidenceReview> getReviewsByConfidenceLevel(ConfidenceLevel level) {
    return _reviewHistory.where((review) => review.confidenceLevel == level).toList();
  }

  /// Get reviews by status
  List<ConfidenceReview> getReviewsByStatus(ReviewStatus status) {
    return _reviewHistory.where((review) => review.status == status).toList();
  }

  /// Get confidence statistics
  Map<String, dynamic> getConfidenceStatistics() {
    final totalReviews = _reviewHistory.length + _pendingReviews.length;
    final confidenceLevelCounts = <ConfidenceLevel, int>{};
    final actionCounts = <ReviewAction, int>{};
    
    for (final review in _reviewHistory) {
      confidenceLevelCounts[review.confidenceLevel] = 
          (confidenceLevelCounts[review.confidenceLevel] ?? 0) + 1;
      
      if (review.reviewAction != null) {
        actionCounts[review.reviewAction!] = 
            (actionCounts[review.reviewAction!] ?? 0) + 1;
      }
    }

    return {
      'total_reviews': totalReviews,
      'pending_reviews': _pendingReviews.length,
      'completed_reviews': _reviewHistory.length,
      'system_accuracy': _systemAccuracy.value,
      'confidence_threshold': _confidenceThreshold.value,
      'auto_accept_enabled': _autoAcceptHighConfidence.value,
      'auto_reject_enabled': _autoRejectLowConfidence.value,
      'confidence_level_distribution': confidenceLevelCounts.map(
        (key, value) => MapEntry(key.name, value),
      ),
      'action_distribution': actionCounts.map(
        (key, value) => MapEntry(key.name, value),
      ),
      'average_confidence': _reviewHistory.isNotEmpty
          ? _reviewHistory.map((r) => r.classification.confidence).reduce((a, b) => a + b) / _reviewHistory.length
          : 0.0,
    };
  }

  /// Clear all reviews
  void clearAllReviews() {
    _pendingReviews.clear();
    _reviewHistory.clear();
    _totalReviewsProcessed.value = 0;
    _correctPredictions.value = 0;
    _systemAccuracy.value = 0.0;
    _saveSettings();
    _saveReviews();
  }

  /// Clear completed reviews only
  void clearCompletedReviews() {
    _reviewHistory.clear();
    _saveReviews();
  }

  /// Bulk review actions
  Future<void> bulkReviewAction({
    required List<String> reviewIds,
    required ReviewAction action,
  }) async {
    for (final reviewId in reviewIds) {
      await reviewTransaction(reviewId: reviewId, action: action);
    }
  }

  /// Get pending reviews count by confidence level
  Map<ConfidenceLevel, int> getPendingReviewsByConfidence() {
    final counts = <ConfidenceLevel, int>{};
    for (final review in _pendingReviews) {
      counts[review.confidenceLevel] = (counts[review.confidenceLevel] ?? 0) + 1;
    }
    return counts;
  }

  /// Check if review queue is full
  bool get isReviewQueueFull => _pendingReviews.length >= _maxPendingReviews.value;

  /// Get next review to process
  ConfidenceReview? get nextReview => _pendingReviews.isNotEmpty ? _pendingReviews.first : null;
}
