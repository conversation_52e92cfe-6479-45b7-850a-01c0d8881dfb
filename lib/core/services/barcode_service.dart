import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_mlkit_barcode_scanning/google_mlkit_barcode_scanning.dart';
import 'package:camera/camera.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/scanned_barcode.dart';

class BarcodeService extends GetxService {
  static BarcodeService get to => Get.find();

  final RxBool _isScanning = false.obs;
  final RxBool _isProcessing = false.obs;
  final RxString _lastScannedCode = ''.obs;
  
  BarcodeScanner? _barcodeScanner;
  CameraController? _cameraController;
  List<CameraDescription>? _cameras;

  bool get isScanning => _isScanning.value;
  bool get isProcessing => _isProcessing.value;
  String get lastScannedCode => _lastScannedCode.value;

  @override
  void onInit() {
    super.onInit();
    _initializeScanner();
  }

  @override
  void onClose() {
    _barcodeScanner?.close();
    _cameraController?.dispose();
    super.onClose();
  }

  void _initializeScanner() {
    _barcodeScanner = BarcodeScanner(
      formats: [
        BarcodeFormat.qrCode,
        BarcodeFormat.ean13,
        BarcodeFormat.ean8,
        BarcodeFormat.code128,
        BarcodeFormat.code39,
        BarcodeFormat.code93,
        BarcodeFormat.codabar,
        BarcodeFormat.dataMatrix,
        BarcodeFormat.pdf417,
        BarcodeFormat.aztec,
      ],
    );
  }

  // Initialize camera for barcode scanning
  Future<bool> initializeCamera() async {
    try {
      // Request camera permission
      final status = await Permission.camera.request();
      if (!status.isGranted) {
        Get.snackbar(
          'Permission Required',
          'Camera permission is required for barcode scanning',
          backgroundColor: Colors.orange,
          colorText: Colors.white,
        );
        return false;
      }

      // Get available cameras
      _cameras = await availableCameras();
      if (_cameras == null || _cameras!.isEmpty) {
        Get.snackbar(
          'Camera Error',
          'No cameras available on this device',
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return false;
      }

      // Initialize camera controller
      _cameraController = CameraController(
        _cameras!.first,
        ResolutionPreset.high,
        enableAudio: false,
      );

      await _cameraController!.initialize();
      return true;
    } catch (e) {
      print('Error initializing camera: $e');
      Get.snackbar(
        'Camera Error',
        'Failed to initialize camera: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }
  }

  // Start barcode scanning
  Future<void> startScanning() async {
    if (_isScanning.value) return;

    try {
      _isScanning.value = true;
      
      if (_cameraController == null || !_cameraController!.value.isInitialized) {
        final initialized = await initializeCamera();
        if (!initialized) return;
      }

      // Start image stream for barcode detection
      _cameraController!.startImageStream((CameraImage image) {
        if (!_isProcessing.value) {
          _processImage(image);
        }
      });

    } catch (e) {
      print('Error starting barcode scanning: $e');
      Get.snackbar(
        'Scan Error',
        'Failed to start barcode scanning',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      _isScanning.value = false;
    }
  }

  // Stop barcode scanning
  Future<void> stopScanning() async {
    try {
      _isScanning.value = false;
      _isProcessing.value = false;
      
      if (_cameraController != null && _cameraController!.value.isStreamingImages) {
        await _cameraController!.stopImageStream();
      }
    } catch (e) {
      print('Error stopping barcode scanning: $e');
    }
  }

  // Process camera image for barcode detection
  Future<void> _processImage(CameraImage image) async {
    if (_isProcessing.value) return;

    try {
      _isProcessing.value = true;

      // Convert CameraImage to InputImage
      final inputImage = _convertCameraImage(image);
      if (inputImage == null) return;

      // Process image with ML Kit
      final barcodes = await _barcodeScanner!.processImage(inputImage);
      
      if (barcodes.isNotEmpty) {
        final barcode = barcodes.first;
        await _handleDetectedBarcode(barcode);
      }

    } catch (e) {
      print('Error processing barcode image: $e');
    } finally {
      _isProcessing.value = false;
    }
  }

  // Convert CameraImage to InputImage
  InputImage? _convertCameraImage(CameraImage image) {
    try {
      // This is a simplified conversion - in production you'd need proper format handling
      final bytes = image.planes.first.bytes;
      final imageSize = Size(image.width.toDouble(), image.height.toDouble());
      
      return InputImage.fromBytes(
        bytes: bytes,
        metadata: InputImageMetadata(
          size: imageSize,
          rotation: InputImageRotation.rotation0deg,
          format: InputImageFormat.nv21,
          bytesPerRow: image.planes.first.bytesPerRow,
        ),
      );
    } catch (e) {
      print('Error converting camera image: $e');
      return null;
    }
  }

  // Handle detected barcode
  Future<void> _handleDetectedBarcode(Barcode barcode) async {
    final code = barcode.rawValue;
    if (code == null || code.isEmpty || code == _lastScannedCode.value) return;

    _lastScannedCode.value = code;
    
    // Stop scanning to prevent multiple detections
    await stopScanning();

    // Create scanned barcode object
    final scannedBarcode = ScannedBarcode(
      id: 'barcode_${DateTime.now().millisecondsSinceEpoch}',
      rawValue: code,
      displayValue: barcode.displayValue ?? code,
      format: barcode.format,
      type: barcode.type,
      boundingBox: barcode.boundingBox,
      cornerPoints: barcode.cornerPoints?.map((point) => Offset(point.x.toDouble(), point.y.toDouble())).toList(),
      scannedAt: DateTime.now(),
    );

    // Show success feedback
    Get.snackbar(
      'Barcode Scanned',
      'Code: $code',
      backgroundColor: Colors.green,
      colorText: Colors.white,
      duration: const Duration(seconds: 2),
    );

    // Handle the scanned barcode
    _handleScannedBarcode(scannedBarcode);
  }

  // Handle scanned barcode result
  void _handleScannedBarcode(ScannedBarcode barcode) {
    // Navigate back and pass the result
    Get.back(result: barcode);
  }

  // Show barcode scanner screen
  Future<ScannedBarcode?> showBarcodeScanner() async {
    try {
      final result = await Get.toNamed('/barcode-scanner');
      return result as ScannedBarcode?;
    } catch (e) {
      print('Error showing barcode scanner: $e');
      return null;
    }
  }

  // Quick scan method for simple use cases
  Future<String?> quickScan() async {
    try {
      final scannedBarcode = await showBarcodeScanner();
      return scannedBarcode?.rawValue;
    } catch (e) {
      print('Error in quick scan: $e');
      return null;
    }
  }

  // Get camera controller for UI
  CameraController? get cameraController => _cameraController;

  // Check if camera is available
  bool get isCameraAvailable => _cameraController?.value.isInitialized ?? false;

  // Get supported barcode formats
  List<BarcodeFormat> get supportedFormats => [
    BarcodeFormat.qrCode,
    BarcodeFormat.ean13,
    BarcodeFormat.ean8,
    BarcodeFormat.code128,
    BarcodeFormat.code39,
    BarcodeFormat.code93,
    BarcodeFormat.codabar,
    BarcodeFormat.dataMatrix,
    BarcodeFormat.pdf417,
    BarcodeFormat.aztec,
  ];

  // Format barcode type for display
  String formatBarcodeType(BarcodeType type) {
    switch (type) {
      case BarcodeType.wifi:
        return 'WiFi';
      case BarcodeType.url:
        return 'URL';
      case BarcodeType.sms:
        return 'SMS';
      case BarcodeType.phone:
        return 'Phone';
      case BarcodeType.email:
        return 'Email';
      case BarcodeType.contactInfo:
        return 'Contact';
      case BarcodeType.calendarEvent:
        return 'Calendar';
      case BarcodeType.driverLicense:
        return 'Driver License';
      case BarcodeType.unknown:
        return 'Location';
      case BarcodeType.isbn:
        return 'ISBN';
      case BarcodeType.product:
        return 'Product';
      case BarcodeType.text:
        return 'Text';
      default:
        return 'Unknown';
    }
  }

  // Format barcode format for display
  String formatBarcodeFormat(BarcodeFormat format) {
    switch (format) {
      case BarcodeFormat.qrCode:
        return 'QR Code';
      case BarcodeFormat.ean13:
        return 'EAN-13';
      case BarcodeFormat.ean8:
        return 'EAN-8';
      case BarcodeFormat.code128:
        return 'Code 128';
      case BarcodeFormat.code39:
        return 'Code 39';
      case BarcodeFormat.code93:
        return 'Code 93';
      case BarcodeFormat.codabar:
        return 'Codabar';
      case BarcodeFormat.dataMatrix:
        return 'Data Matrix';
      case BarcodeFormat.pdf417:
        return 'PDF417';
      case BarcodeFormat.aztec:
        return 'Aztec';
      default:
        return 'Unknown';
    }
  }
}
