import 'dart:math' as math;

/// Utility class for chart scaling and formatting
class ChartUtils {
  /// Calculate optimal Y-axis interval based on the maximum value
  static double calculateOptimalInterval(double maxValue) {
    if (maxValue <= 0) return 10;
    
    // Calculate the order of magnitude
    final magnitude = math.pow(10, (math.log(maxValue) / math.ln10).floor());
    final normalizedMax = maxValue / magnitude;
    
    double interval;
    
    // Choose interval based on normalized maximum
    if (normalizedMax <= 1) {
      interval = magnitude * 0.2;
    } else if (normalizedMax <= 2) {
      interval = magnitude * 0.5;
    } else if (normalizedMax <= 5) {
      interval = magnitude * 1;
    } else {
      interval = magnitude * 2;
    }
    
    // Ensure we have at least 3-8 intervals for good readability
    final numIntervals = maxValue / interval;
    if (numIntervals < 3) {
      interval = maxValue / 4;
    } else if (numIntervals > 8) {
      interval = maxValue / 6;
    }
    
    return interval;
  }

  /// Calculate optimal Y-axis maximum value with padding
  static double calculateOptimalMaxY(double dataMaxValue, {double paddingFactor = 0.1}) {
    if (dataMaxValue <= 0) return 100;
    
    final interval = calculateOptimalInterval(dataMaxValue);
    final paddedMax = dataMaxValue * (1 + paddingFactor);
    
    // Round up to the nearest interval
    return (paddedMax / interval).ceil() * interval;
  }

  /// Format amount for chart display with appropriate units
  static String formatChartAmount(double amount, {bool compact = true}) {
    if (amount == 0) return '0';
    
    if (compact) {
      if (amount >= 1000000000) {
        return '${(amount / 1000000000).toStringAsFixed(1)}B';
      } else if (amount >= 1000000) {
        return '${(amount / 1000000).toStringAsFixed(1)}M';
      } else if (amount >= 1000) {
        return '${(amount / 1000).toStringAsFixed(1)}K';
      } else if (amount >= 100) {
        return amount.toStringAsFixed(0);
      } else if (amount >= 10) {
        return amount.toStringAsFixed(1);
      } else {
        return amount.toStringAsFixed(2);
      }
    } else {
      // Full format for tooltips
      if (amount >= 1000000) {
        return amount.toStringAsFixed(0).replaceAllMapped(
          RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
          (Match m) => '${m[1]},',
        );
      } else {
        return amount.toStringAsFixed(2);
      }
    }
  }

  /// Get optimal number of Y-axis labels
  static int getOptimalLabelCount(double maxValue) {
    if (maxValue <= 100) return 5;
    if (maxValue <= 1000) return 6;
    if (maxValue <= 10000) return 5;
    if (maxValue <= 100000) return 6;
    return 5;
  }

  /// Generate Y-axis tick values
  static List<double> generateYAxisTicks(double maxValue, double interval) {
    final ticks = <double>[];
    double current = 0;
    
    while (current <= maxValue) {
      ticks.add(current);
      current += interval;
    }
    
    return ticks;
  }

  /// Calculate chart dimensions based on data
  static ChartDimensions calculateChartDimensions(
    List<double> values, {
    double minHeight = 200,
    double maxHeight = 400,
    double paddingFactor = 0.1,
  }) {
    if (values.isEmpty) {
      return ChartDimensions(
        maxY: 100,
        interval: 20,
        height: minHeight,
        labelCount: 5,
      );
    }

    final maxValue = values.reduce(math.max);
    final optimalMaxY = calculateOptimalMaxY(maxValue, paddingFactor: paddingFactor);
    final interval = calculateOptimalInterval(maxValue);
    final labelCount = getOptimalLabelCount(maxValue);
    
    // Adjust height based on value range
    double height = minHeight;
    if (maxValue > 10000) {
      height = math.min(maxHeight, minHeight + (maxValue / 10000) * 50);
    }
    
    return ChartDimensions(
      maxY: optimalMaxY,
      interval: interval,
      height: height,
      labelCount: labelCount,
    );
  }

  /// Format percentage values
  static String formatPercentage(double value, {int decimals = 1}) {
    return '${value.toStringAsFixed(decimals)}%';
  }

  /// Get color based on value trend
  static String getTrendColor(double current, double previous) {
    if (current > previous) return '#4CAF50'; // Green for positive
    if (current < previous) return '#F44336'; // Red for negative
    return '#FF9800'; // Orange for neutral
  }

  /// Calculate trend percentage
  static double calculateTrendPercentage(double current, double previous) {
    if (previous == 0) return current > 0 ? 100 : 0;
    return ((current - previous) / previous) * 100;
  }

  /// Smooth data points for better chart visualization
  static List<double> smoothData(List<double> data, {int windowSize = 3}) {
    if (data.length <= windowSize) return data;
    
    final smoothed = <double>[];
    
    for (int i = 0; i < data.length; i++) {
      double sum = 0;
      int count = 0;
      
      final start = math.max(0, i - windowSize ~/ 2);
      final end = math.min(data.length - 1, i + windowSize ~/ 2);
      
      for (int j = start; j <= end; j++) {
        sum += data[j];
        count++;
      }
      
      smoothed.add(sum / count);
    }
    
    return smoothed;
  }

  /// Detect outliers in data
  static List<int> detectOutliers(List<double> data, {double threshold = 2.0}) {
    if (data.length < 4) return [];
    
    final mean = data.reduce((a, b) => a + b) / data.length;
    final variance = data.map((x) => math.pow(x - mean, 2)).reduce((a, b) => a + b) / data.length;
    final stdDev = math.sqrt(variance);
    
    final outliers = <int>[];
    for (int i = 0; i < data.length; i++) {
      if ((data[i] - mean).abs() > threshold * stdDev) {
        outliers.add(i);
      }
    }
    
    return outliers;
  }
}

/// Chart dimensions configuration
class ChartDimensions {
  final double maxY;
  final double interval;
  final double height;
  final int labelCount;

  const ChartDimensions({
    required this.maxY,
    required this.interval,
    required this.height,
    required this.labelCount,
  });
}

/// Chart color schemes
class ChartColors {
  static const String primaryGreen = '#4CAF50';
  static const String primaryRed = '#F44336';
  static const String primaryBlue = '#2196F3';
  static const String primaryOrange = '#FF9800';
  static const String primaryPurple = '#9C27B0';
  static const String primaryTeal = '#009688';
  
  static const List<String> categoryColors = [
    primaryBlue,
    primaryGreen,
    primaryOrange,
    primaryRed,
    primaryPurple,
    primaryTeal,
    '#795548', // Brown
    '#607D8B', // Blue Grey
    '#E91E63', // Pink
    '#CDDC39', // Lime
  ];
  
  static String getCategoryColor(int index) {
    return categoryColors[index % categoryColors.length];
  }
}
