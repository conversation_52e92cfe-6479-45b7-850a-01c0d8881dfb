import 'package:flutter/material.dart';

/// Unified payment method system for all transaction types
class PaymentMethods {
  static const String cash = 'cash';
  static const String mPesa = 'm_pesa';
  static const String airtelMoney = 'airtel_money';
  static const String tkash = 't_kash';
  static const String bankTransfer = 'bank_transfer';
  static const String creditCard = 'credit_card';
  static const String debitCard = 'debit_card';
  static const String digitalWallet = 'digital_wallet';
  static const String bitcoin = 'bitcoin';
  static const String ethereum = 'ethereum';
  static const String usdt = 'usdt';
  static const String binanceCoin = 'binance_coin';
  static const String other = 'other';

  /// Get all available payment methods
  static List<PaymentMethodInfo> getAllPaymentMethods() {
    return [
      PaymentMethodInfo(
        id: cash,
        name: 'Cash',
        icon: Icons.money_rounded,
        category: PaymentMethodCategory.traditional,
        description: 'Physical cash payment',
      ),
      PaymentMethodInfo(
        id: mPesa,
        name: 'M-Pesa',
        icon: Icons.phone_android_rounded,
        category: PaymentMethodCategory.mobileMoney,
        description: 'Safaricom M-Pesa mobile money',
      ),
      PaymentMethodInfo(
        id: airtelMoney,
        name: 'Airtel Money',
        icon: Icons.phone_android_rounded,
        category: PaymentMethodCategory.mobileMoney,
        description: 'Airtel mobile money service',
      ),
      PaymentMethodInfo(
        id: tkash,
        name: 'T-Kash',
        icon: Icons.phone_android_rounded,
        category: PaymentMethodCategory.mobileMoney,
        description: 'Telkom T-Kash mobile money',
      ),
      PaymentMethodInfo(
        id: bankTransfer,
        name: 'Bank Transfer',
        icon: Icons.account_balance_rounded,
        category: PaymentMethodCategory.banking,
        description: 'Direct bank transfer',
      ),
      PaymentMethodInfo(
        id: creditCard,
        name: 'Credit Card',
        icon: Icons.credit_card_rounded,
        category: PaymentMethodCategory.cards,
        description: 'Credit card payment',
      ),
      PaymentMethodInfo(
        id: debitCard,
        name: 'Debit Card',
        icon: Icons.credit_card_rounded,
        category: PaymentMethodCategory.cards,
        description: 'Debit card payment',
      ),
      PaymentMethodInfo(
        id: digitalWallet,
        name: 'Digital Wallet',
        icon: Icons.account_balance_wallet_rounded,
        category: PaymentMethodCategory.digital,
        description: 'Digital wallet services',
      ),
      PaymentMethodInfo(
        id: bitcoin,
        name: 'Bitcoin',
        icon: Icons.currency_bitcoin_rounded,
        category: PaymentMethodCategory.crypto,
        description: 'Bitcoin cryptocurrency',
      ),
      PaymentMethodInfo(
        id: ethereum,
        name: 'Ethereum',
        icon: Icons.currency_bitcoin_rounded,
        category: PaymentMethodCategory.crypto,
        description: 'Ethereum cryptocurrency',
      ),
      PaymentMethodInfo(
        id: usdt,
        name: 'USDT',
        icon: Icons.currency_bitcoin_rounded,
        category: PaymentMethodCategory.crypto,
        description: 'Tether USDT stablecoin',
      ),
      PaymentMethodInfo(
        id: binanceCoin,
        name: 'Binance Coin',
        icon: Icons.currency_bitcoin_rounded,
        category: PaymentMethodCategory.crypto,
        description: 'Binance BNB token',
      ),
      PaymentMethodInfo(
        id: other,
        name: 'Other',
        icon: Icons.more_horiz_rounded,
        category: PaymentMethodCategory.other,
        description: 'Other payment methods',
      ),
    ];
  }

  /// Get payment methods by category
  static List<PaymentMethodInfo> getPaymentMethodsByCategory(PaymentMethodCategory category) {
    return getAllPaymentMethods().where((method) => method.category == category).toList();
  }

  /// Get mobile money payment methods
  static List<PaymentMethodInfo> getMobileMoneyMethods() {
    return getPaymentMethodsByCategory(PaymentMethodCategory.mobileMoney);
  }

  /// Get crypto payment methods
  static List<PaymentMethodInfo> getCryptoMethods() {
    return getPaymentMethodsByCategory(PaymentMethodCategory.crypto);
  }

  /// Get traditional payment methods
  static List<PaymentMethodInfo> getTraditionalMethods() {
    return [
      ...getPaymentMethodsByCategory(PaymentMethodCategory.traditional),
      ...getPaymentMethodsByCategory(PaymentMethodCategory.banking),
      ...getPaymentMethodsByCategory(PaymentMethodCategory.cards),
    ];
  }

  /// Get payment method info by ID
  static PaymentMethodInfo? getPaymentMethodById(String id) {
    try {
      return getAllPaymentMethods().firstWhere((method) => method.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Get popular payment methods for Kenya
  static List<PaymentMethodInfo> getPopularMethods() {
    return [
      getPaymentMethodById(cash)!,
      getPaymentMethodById(mPesa)!,
      getPaymentMethodById(bankTransfer)!,
      getPaymentMethodById(creditCard)!,
      getPaymentMethodById(digitalWallet)!,
    ];
  }
}

/// Payment method categories
enum PaymentMethodCategory {
  traditional,
  mobileMoney,
  banking,
  cards,
  digital,
  crypto,
  other,
}

/// Payment method information
class PaymentMethodInfo {
  final String id;
  final String name;
  final IconData icon;
  final PaymentMethodCategory category;
  final String description;

  const PaymentMethodInfo({
    required this.id,
    required this.name,
    required this.icon,
    required this.category,
    required this.description,
  });

  @override
  String toString() => name;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaymentMethodInfo &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}

/// Extension for payment method category display
extension PaymentMethodCategoryExtension on PaymentMethodCategory {
  String get displayName {
    switch (this) {
      case PaymentMethodCategory.traditional:
        return 'Traditional';
      case PaymentMethodCategory.mobileMoney:
        return 'Mobile Money';
      case PaymentMethodCategory.banking:
        return 'Banking';
      case PaymentMethodCategory.cards:
        return 'Cards';
      case PaymentMethodCategory.digital:
        return 'Digital Wallets';
      case PaymentMethodCategory.crypto:
        return 'Cryptocurrency';
      case PaymentMethodCategory.other:
        return 'Other';
    }
  }

  IconData get icon {
    switch (this) {
      case PaymentMethodCategory.traditional:
        return Icons.money_rounded;
      case PaymentMethodCategory.mobileMoney:
        return Icons.phone_android_rounded;
      case PaymentMethodCategory.banking:
        return Icons.account_balance_rounded;
      case PaymentMethodCategory.cards:
        return Icons.credit_card_rounded;
      case PaymentMethodCategory.digital:
        return Icons.account_balance_wallet_rounded;
      case PaymentMethodCategory.crypto:
        return Icons.currency_bitcoin_rounded;
      case PaymentMethodCategory.other:
        return Icons.more_horiz_rounded;
    }
  }
}
