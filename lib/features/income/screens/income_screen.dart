import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/services/theme_service.dart';
import '../../../core/services/account_service.dart';
import '../../../core/services/personal_transaction_service.dart';
import '../../../core/models/transaction.dart';
import '../../../core/database/database.dart';
import '../../common/widgets/common_app_bar.dart';
import '../../common/widgets/account_drawer.dart';
import '../../../shared/widgets/persistent_date_filter.dart';

class IncomeScreen extends StatefulWidget {
  const IncomeScreen({super.key});

  @override
  State<IncomeScreen> createState() => _IncomeScreenState();
}

class _IncomeScreenState extends State<IncomeScreen> {
  String _selectedPeriod = 'This Month';
  DateTime? _selectedDate;
  DateTimeRange? _selectedDateRange;
  final List<String> _periods = ['Today', 'This Week', 'This Month', 'This Year', 'All Time', 'Custom'];

  List<PersonalTransaction> _filteredTransactions = [];
  Map<String, double> _categoryTotals = {};
  double _totalIncome = 0.0;
  double _averageIncome = 0.0;

  @override
  void initState() {
    super.initState();
    _loadIncomeData();
  }

  void _loadIncomeData() {
    final transactionService = PersonalTransactionService.to;
    final allTransactions = transactionService.transactions;

    // Filter income transactions
    final incomeTransactions = allTransactions
        .where((t) => t.type == 'income')
        .toList();

    // Filter by selected period
    _filteredTransactions = _filterTransactionsByPeriod(incomeTransactions);

    // Calculate totals and analytics
    _calculateAnalytics();

    setState(() {});
  }

  List<PersonalTransaction> _filterTransactionsByPeriod(List<PersonalTransaction> transactions) {
    // Handle custom date selection first
    if (_selectedDate != null) {
      final selectedDay = DateTime(_selectedDate!.year, _selectedDate!.month, _selectedDate!.day);
      final nextDay = selectedDay.add(const Duration(days: 1));
      return transactions.where((t) =>
        t.date.isAfter(selectedDay.subtract(const Duration(milliseconds: 1))) &&
        t.date.isBefore(nextDay)
      ).toList();
    }

    if (_selectedDateRange != null) {
      return transactions.where((t) =>
        t.date.isAfter(_selectedDateRange!.start.subtract(const Duration(milliseconds: 1))) &&
        t.date.isBefore(_selectedDateRange!.end.add(const Duration(days: 1)))
      ).toList();
    }

    final now = DateTime.now();
    DateTime startDate;

    switch (_selectedPeriod) {
      case 'Today':
        startDate = DateTime(now.year, now.month, now.day);
        break;
      case 'This Week':
        startDate = now.subtract(Duration(days: now.weekday - 1));
        startDate = DateTime(startDate.year, startDate.month, startDate.day);
        break;
      case 'This Month':
        startDate = DateTime(now.year, now.month, 1);
        break;
      case 'This Year':
        startDate = DateTime(now.year, 1, 1);
        break;
      case 'All Time':
        return transactions;
      case 'Custom':
        return transactions; // Already handled above
      default:
        startDate = DateTime(now.year, now.month, 1);
    }

    return transactions.where((t) => t.date.isAfter(startDate) || t.date.isAtSameMomentAs(startDate)).toList();
  }

  void _calculateAnalytics() {
    _totalIncome = _filteredTransactions.fold(0.0, (sum, t) => sum + t.amount);

    // Calculate category totals
    _categoryTotals.clear();
    for (final transaction in _filteredTransactions) {
      final categoryName = _getCategoryDisplayName(transaction.category);
      _categoryTotals[categoryName] = (_categoryTotals[categoryName] ?? 0.0) + transaction.amount;
    }

    // Calculate average income
    if (_filteredTransactions.isNotEmpty) {
      switch (_selectedPeriod) {
        case 'Today':
          _averageIncome = _totalIncome;
          break;
        case 'This Week':
          _averageIncome = _totalIncome / 7;
          break;
        case 'This Month':
          final daysInMonth = DateTime.now().day;
          _averageIncome = _totalIncome / daysInMonth;
          break;
        case 'This Year':
          final dayOfYear = DateTime.now().difference(DateTime(DateTime.now().year, 1, 1)).inDays + 1;
          _averageIncome = _totalIncome / dayOfYear;
          break;
        default:
          _averageIncome = _totalIncome / (_filteredTransactions.length > 0 ? _filteredTransactions.length : 1);
      }
    } else {
      _averageIncome = 0.0;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final themeService = ThemeService.to;
      final accountService = AccountService.to;
      
      return Scaffold(
        backgroundColor: themeService.backgroundColor,
        appBar: CommonAppBar(
          title: 'Income',
          showAccountInfo: true,
          actions: [
            IconButton(
              onPressed: () {
                _showFilterOptions(context);
              },
              icon: const Icon(Icons.filter_list_rounded),
            ),
            IconButton(
              onPressed: () {
                _showSearchDialog(context);
              },
              icon: const Icon(Icons.search_rounded),
            ),
          ],
        ),
        endDrawer: const AccountDrawer(),
        body: RefreshIndicator(
          onRefresh: _refreshData,
          color: themeService.primaryColor,
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Persistent Date Filter
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: PersistentDateFilter(
                    selectedDate: _selectedDate,
                    selectedDateRange: _selectedDateRange,
                    onDateSelected: (date) {
                      setState(() {
                        _selectedDate = date;
                        _selectedDateRange = null;
                        _selectedPeriod = 'Custom';
                      });
                      _refreshData();
                    },
                    onDateRangeSelected: (range) {
                      setState(() {
                        _selectedDateRange = range;
                        _selectedDate = null;
                        _selectedPeriod = 'Custom';
                      });
                      _refreshData();
                    },
                    emptyStateMessage: _filteredTransactions.isEmpty
                        ? 'No income records found for the selected date'
                        : null,
                  ),
                ),

                // Period Selector
                _buildPeriodSelector(themeService),

                const SizedBox(height: 16),
                
                // Income Summary Cards
                _buildIncomeSummary(themeService),
                
                const SizedBox(height: 20),
                
                // Income Categories
                _buildIncomeCategories(themeService),
                
                const SizedBox(height: 20),
                
                // Recent Income Transactions
                _buildRecentIncomeTransactions(themeService),
                
                const SizedBox(height: 100), // Space for bottom nav
              ],
            ),
          ),
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: () {
            Get.toNamed('/add-transaction', arguments: {'isIncome': true});
          },
          backgroundColor: themeService.primaryColor,
          child: const Icon(Icons.add_rounded, color: Colors.white),
        ),
      );
    });
  }

  Widget _buildPeriodSelector(ThemeService themeService) {
    return Container(
      height: 50,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _periods.length,
        itemBuilder: (context, index) {
          final period = _periods[index];
          final isSelected = period == _selectedPeriod;
          
          return Container(
            margin: const EdgeInsets.only(right: 12),
            child: FilterChip(
              label: Text(period),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _selectedPeriod = period;
                  if (period != 'Custom') {
                    _selectedDate = null;
                    _selectedDateRange = null;
                  }
                  _loadIncomeData();
                });
              },
              backgroundColor: themeService.surfaceColor,
              selectedColor: themeService.primaryColor.withOpacity(0.2),
              labelStyle: TextStyle(
                color: isSelected ? themeService.primaryColor : themeService.textSecondaryColor,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildIncomeSummary(ThemeService themeService) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Expanded(
            child: _buildSummaryCard(
              title: 'Total Income',
              amount: '\$${_totalIncome.toStringAsFixed(2)}',
              subtitle: 'for $_selectedPeriod',
              color: Colors.green,
              icon: Icons.trending_up_rounded,
              themeService: themeService,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildSummaryCard(
              title: 'Average Income',
              amount: '\$${_averageIncome.toStringAsFixed(2)}',
              subtitle: _getAverageSubtitle(),
              color: Colors.blue,
              icon: Icons.analytics_rounded,
              themeService: themeService,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard({
    required String title,
    required String amount,
    required String subtitle,
    required Color color,
    required IconData icon,
    required ThemeService themeService,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: themeService.surfaceColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const Spacer(),
              Icon(Icons.more_vert_rounded, color: themeService.textSecondaryColor, size: 16),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: TextStyle(
              color: themeService.textSecondaryColor,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            amount,
            style: TextStyle(
              color: themeService.textPrimaryColor,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            subtitle,
            style: TextStyle(
              color: themeService.textSecondaryColor,
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIncomeCategories(ThemeService themeService) {
    if (_categoryTotals.isEmpty) {
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Income Categories',
              style: TextStyle(
                color: themeService.textPrimaryColor,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(32),
              decoration: BoxDecoration(
                color: themeService.surfaceColor,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.category_rounded,
                      size: 48,
                      color: themeService.textSecondaryColor,
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'No income data for $_selectedPeriod',
                      style: TextStyle(
                        color: themeService.textSecondaryColor,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      );
    }

    // Convert category totals to list with percentages
    final categories = _categoryTotals.entries.map((entry) {
      final percentage = _totalIncome > 0 ? (entry.value / _totalIncome * 100).round() : 0;
      return {
        'name': entry.key,
        'amount': '\$${entry.value.toStringAsFixed(2)}',
        'percentage': percentage,
        'color': _getCategoryColor(entry.key),
      };
    }).toList();

    // Sort by amount descending
    categories.sort((a, b) => (b['percentage'] as int).compareTo(a['percentage'] as int));

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Income Categories',
            style: TextStyle(
              color: themeService.textPrimaryColor,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          ...categories.map((category) => _buildCategoryItem(category, themeService)),
        ],
      ),
    );
  }

  Widget _buildCategoryItem(Map<String, dynamic> category, ThemeService themeService) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: themeService.surfaceColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: category['color'].withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              Icons.category_rounded,
              color: category['color'],
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  category['name'],
                  style: TextStyle(
                    color: themeService.textPrimaryColor,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                LinearProgressIndicator(
                  value: category['percentage'] / 100,
                  backgroundColor: Colors.grey[200],
                  valueColor: AlwaysStoppedAnimation<Color>(category['color']),
                ),
              ],
            ),
          ),
          const SizedBox(width: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                category['amount'],
                style: TextStyle(
                  color: themeService.textPrimaryColor,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '${category['percentage']}%',
                style: TextStyle(
                  color: themeService.textSecondaryColor,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRecentIncomeTransactions(ThemeService themeService) {
    final recentTransactions = _filteredTransactions.take(5).toList();

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Recent Income',
                style: TextStyle(
                  color: themeService.textPrimaryColor,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (recentTransactions.isNotEmpty)
                TextButton(
                  onPressed: () {
                    // Navigate to all income transactions
                    Get.toNamed('/transactions', arguments: {'filter': 'income'});
                  },
                  child: Text(
                    'View All',
                    style: TextStyle(color: themeService.primaryColor),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 12),
          if (recentTransactions.isEmpty)
            Container(
              padding: const EdgeInsets.all(32),
              decoration: BoxDecoration(
                color: themeService.surfaceColor,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.receipt_long_rounded,
                      size: 48,
                      color: themeService.textSecondaryColor,
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'No income transactions for $_selectedPeriod',
                      style: TextStyle(
                        color: themeService.textSecondaryColor,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            )
          else
            ...recentTransactions.map((transaction) => _buildTransactionItem(transaction, themeService)),
        ],
      ),
    );
  }

  Widget _buildTransactionItem(PersonalTransaction transaction, ThemeService themeService) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: themeService.surfaceColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.green.withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              Icons.trending_up_rounded,
              color: Colors.green,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  transaction.title,
                  style: TextStyle(
                    color: themeService.textPrimaryColor,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${_getCategoryDisplayName(transaction.category)} • ${_formatDate(transaction.date)}',
                  style: TextStyle(
                    color: themeService.textSecondaryColor,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Text(
            '\$${transaction.amount.toStringAsFixed(2)}',
            style: TextStyle(
              color: Colors.green,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  void _showFilterOptions(BuildContext context) {
    // TODO: Implement filter options
  }

  void _showSearchDialog(BuildContext context) {
    // TODO: Implement search functionality
  }

  Future<void> _refreshData() async {
    _loadIncomeData();
  }

  String _getAverageSubtitle() {
    switch (_selectedPeriod) {
      case 'Today':
        return 'today';
      case 'This Week':
        return 'per day this week';
      case 'This Month':
        return 'per day this month';
      case 'This Year':
        return 'per day this year';
      default:
        return 'per transaction';
    }
  }

  Color _getCategoryColor(String categoryName) {
    switch (categoryName.toLowerCase()) {
      case 'salary':
        return Colors.blue;
      case 'freelance':
        return Colors.green;
      case 'investment':
        return Colors.orange;
      case 'business':
        return Colors.purple;
      case 'other income':
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  // Helper function to get display name from category string
  String _getCategoryDisplayName(String categoryString) {
    try {
      final category = TransactionCategory.values.firstWhere(
        (e) => e.name == categoryString,
        orElse: () => TransactionCategory.other_income,
      );
      return category.displayName;
    } catch (e) {
      // Fallback for unknown categories
      return categoryString.replaceAll('_', ' ').split(' ').map((word) =>
          word[0].toUpperCase() + word.substring(1)).join(' ');
    }
  }
}
