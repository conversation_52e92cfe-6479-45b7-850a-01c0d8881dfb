import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/models/transaction.dart';
import '../../../core/models/sms_transaction.dart';
import '../../../core/models/classification_rule.dart';
import '../../../core/services/transaction_classifier_service.dart';
import '../../../core/services/sms_parser_service.dart';
import '../../../core/services/personal_transaction_service.dart';
import '../widgets/training_transaction_card.dart';
import '../widgets/training_progress_card.dart';
import '../widgets/training_stats_card.dart';

class TransactionTrainingScreen extends StatefulWidget {
  const TransactionTrainingScreen({super.key});

  @override
  State<TransactionTrainingScreen> createState() => _TransactionTrainingScreenState();
}

class _TransactionTrainingScreenState extends State<TransactionTrainingScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  
  final TransactionClassifierService _classifierService = Get.find();
  final SmsParserService _smsParserService = Get.find();
  final PersonalTransactionService _transactionService = Get.find();
  
  final RxList<Transaction> _unclassifiedTransactions = <Transaction>[].obs;
  final RxList<SmsTransaction> _unclassifiedSmsTransactions = <SmsTransaction>[].obs;
  final RxInt _currentIndex = 0.obs;
  final RxBool _isLoading = false.obs;
  final RxInt _trainedCount = 0.obs;
  final RxInt _totalCount = 0.obs;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadUnclassifiedTransactions();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadUnclassifiedTransactions() async {
    _isLoading.value = true;
    
    try {
      // Load regular transactions that need classification
      final personalTransactions = _transactionService.transactions;
      final convertedTransactions = personalTransactions
          .where((t) => t.category == 'other_expense' || t.category == 'other_income')
          .map((pt) => Transaction(
                id: pt.id,
                title: pt.title,
                description: pt.description,
                amount: pt.amount,
                type: TransactionType.values.firstWhere(
                  (e) => e.name == pt.type,
                  orElse: () => TransactionType.expense,
                ),
                category: TransactionCategory.values.firstWhere(
                  (e) => e.name == pt.category,
                  orElse: () => TransactionCategory.other_expense,
                ),
                date: pt.date,
                createdAt: pt.createdAt,
                updatedAt: pt.updatedAt,
              ))
          .toList();
      _unclassifiedTransactions.assignAll(convertedTransactions);
      
      // Load SMS transactions that need classification
      final smsTransactions = _smsParserService.parsedTransactions;
      _unclassifiedSmsTransactions.assignAll(
        smsTransactions.where((t) => !t.isProcessed).toList(),
      );
      
      _totalCount.value = _unclassifiedTransactions.length + _unclassifiedSmsTransactions.length;
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to load transactions: ${e.toString()}',
        backgroundColor: AppColors.error,
        colorText: Colors.white,
      );
    } finally {
      _isLoading.value = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'Train AI Classifier',
          style: TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.white,
        foregroundColor: AppColors.textPrimary,
        elevation: 0,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(1),
          child: Container(
            height: 1,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppColors.primary.withOpacity(0.3),
                  AppColors.secondary.withOpacity(0.3),
                ],
              ),
            ),
          ),
        ),
        actions: [
          IconButton(
            onPressed: _showTrainingStats,
            icon: const Icon(Icons.analytics_rounded),
            tooltip: 'Training Statistics',
          ),
          IconButton(
            onPressed: _showTrainingHelp,
            icon: const Icon(Icons.help_outline_rounded),
            tooltip: 'Help',
          ),
        ],
      ),
      body: Column(
        children: [
          // Progress indicator
          Obx(() => TrainingProgressCard(
            trainedCount: _trainedCount.value,
            totalCount: _totalCount.value,
            accuracy: _classifierService.accuracy,
          )),
          
          // Tab bar
          Container(
            color: Colors.white,
            child: TabBar(
              controller: _tabController,
              labelColor: AppColors.primary,
              unselectedLabelColor: Colors.grey[600],
              indicatorColor: AppColors.primary,
              tabs: [
                Tab(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.receipt_long_rounded, size: 20),
                      const SizedBox(width: 8),
                      Obx(() => Text('Transactions (${_unclassifiedTransactions.length})')),
                    ],
                  ),
                ),
                Tab(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.sms_rounded, size: 20),
                      const SizedBox(width: 8),
                      Obx(() => Text('SMS (${_unclassifiedSmsTransactions.length})')),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          // Content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildTransactionsTab(),
                _buildSmsTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: Obx(() {
        if (_isLoading.value || (_unclassifiedTransactions.isEmpty && _unclassifiedSmsTransactions.isEmpty)) {
          return const SizedBox.shrink();
        }
        
        return FloatingActionButton.extended(
          onPressed: _startBatchTraining,
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          icon: const Icon(Icons.auto_awesome_rounded),
          label: const Text('Batch Train'),
        );
      }),
    );
  }

  Widget _buildTransactionsTab() {
    return Obx(() {
      if (_isLoading.value) {
        return const Center(child: CircularProgressIndicator());
      }
      
      if (_unclassifiedTransactions.isEmpty) {
        return _buildEmptyState(
          icon: Icons.check_circle_outline_rounded,
          title: 'All Transactions Classified!',
          subtitle: 'Great job! All your transactions have been properly classified.',
        );
      }
      
      return AnimationLimiter(
        child: ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: _unclassifiedTransactions.length,
          itemBuilder: (context, index) {
            final transaction = _unclassifiedTransactions[index];
            return AnimationConfiguration.staggeredList(
              position: index,
              duration: const Duration(milliseconds: 375),
              child: SlideAnimation(
                verticalOffset: 50.0,
                child: FadeInAnimation(
                  child: TrainingTransactionCard(
                    transaction: transaction,
                    onClassified: (category, type) => _onTransactionClassified(
                      transaction,
                      category,
                      type,
                      index,
                    ),
                    onSkipped: () => _onTransactionSkipped(index),
                  ),
                ),
              ),
            );
          },
        ),
      );
    });
  }

  Widget _buildSmsTab() {
    return Obx(() {
      if (_isLoading.value) {
        return const Center(child: CircularProgressIndicator());
      }
      
      if (_unclassifiedSmsTransactions.isEmpty) {
        return _buildEmptyState(
          icon: Icons.mark_email_read_rounded,
          title: 'All SMS Processed!',
          subtitle: 'All SMS transactions have been classified and processed.',
        );
      }
      
      return AnimationLimiter(
        child: ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: _unclassifiedSmsTransactions.length,
          itemBuilder: (context, index) {
            final smsTransaction = _unclassifiedSmsTransactions[index];
            return AnimationConfiguration.staggeredList(
              position: index,
              duration: const Duration(milliseconds: 375),
              child: SlideAnimation(
                verticalOffset: 50.0,
                child: FadeInAnimation(
                  child: TrainingSmsTransactionCard(
                    smsTransaction: smsTransaction,
                    onClassified: (category, type) => _onSmsTransactionClassified(
                      smsTransaction,
                      category,
                      type,
                      index,
                    ),
                    onSkipped: () => _onSmsTransactionSkipped(index),
                  ),
                ),
              ),
            );
          },
        ),
      );
    });
  }

  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              icon,
              size: 64,
              color: AppColors.primary,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            title,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          ElevatedButton.icon(
            onPressed: _loadUnclassifiedTransactions,
            icon: const Icon(Icons.refresh_rounded),
            label: const Text('Refresh'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  void _onTransactionClassified(
    Transaction transaction,
    String category,
    TransactionType type,
    int index,
  ) {
    HapticFeedback.lightImpact();
    
    // Train the classifier
    _classifierService.trainWithFeedback(
      transactionId: transaction.id,
      correctCategory: category,
      correctType: type,
      features: {
        'amount': transaction.amount,
        'description': transaction.description,
        'category': transaction.category,
        'type': transaction.type.name,
      },
      wasCorrect: false, // Since it was unclassified
    );
    
    // Update the transaction
    final updatedTransaction = transaction.copyWith(
      category: TransactionCategory.values.firstWhere(
        (c) => c.name == category,
        orElse: () => TransactionCategory.other_expense,
      ),
      type: type,
    );
    _transactionService.updateTransaction(transactionId: transaction.id);
    
    // Remove from unclassified list
    _unclassifiedTransactions.removeAt(index);
    _trainedCount.value++;
    
    _showSuccessMessage('Transaction classified as $category');
  }

  void _onSmsTransactionClassified(
    SmsTransaction smsTransaction,
    String category,
    TransactionType type,
    int index,
  ) {
    HapticFeedback.lightImpact();
    
    // Train the classifier
    _classifierService.trainWithFeedback(
      transactionId: smsTransaction.id,
      correctCategory: category,
      correctType: type,
      features: {
        'amount': smsTransaction.amount,
        'description': smsTransaction.description ?? smsTransaction.rawMessage,
        'category': smsTransaction.category ?? 'unknown',
        'type': smsTransaction.transactionType ?? 'unknown',
        'sender': smsTransaction.sender,
        'merchant': smsTransaction.merchantName ?? '',
      },
      wasCorrect: false,
    );
    
    // Note: SMS transaction will be marked as processed by the service
    
    // Convert to regular transaction
    final transaction = Transaction(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: smsTransaction.merchantName ?? 'SMS Transaction',
      amount: smsTransaction.amount?? 0.00,
      description: smsTransaction.rawMessage,
      category: TransactionCategory.values.firstWhere(
        (c) => c.name == category,
        orElse: () => TransactionCategory.other_expense,
      ),
      type: type,
      date: smsTransaction.createdAt,
      createdAt: DateTime.now(),
    );
    
    _transactionService.addTransaction(
      title: transaction.title,
      description: transaction.description,
      amount: transaction.amount,
      type: transaction.type,
      category: transaction.category,
      date: transaction.date,
    );
    
    // Remove from unclassified list
    _unclassifiedSmsTransactions.removeAt(index);
    _trainedCount.value++;
    
    _showSuccessMessage('SMS transaction processed and classified');
  }

  void _onTransactionSkipped(int index) {
    _unclassifiedTransactions.removeAt(index);
  }

  void _onSmsTransactionSkipped(int index) {
    _unclassifiedSmsTransactions.removeAt(index);
  }

  void _startBatchTraining() {
    showDialog(
      context: context,
      builder: (context) => BatchTrainingDialog(
        unclassifiedTransactions: _unclassifiedTransactions,
        unclassifiedSmsTransactions: _unclassifiedSmsTransactions,
        onCompleted: () {
          _loadUnclassifiedTransactions();
        },
      ),
    );
  }

  void _showTrainingStats() {
    showDialog(
      context: context,
      builder: (context) => TrainingStatsDialog(
        stats: _classifierService.getClassificationStats(),
      ),
    );
  }

  void _showTrainingHelp() {
    showDialog(
      context: context,
      builder: (context) => const TrainingHelpDialog(),
    );
  }

  void _showSuccessMessage(String message) {
    Get.snackbar(
      'Success',
      message,
      backgroundColor: AppColors.success,
      colorText: Colors.white,
      duration: const Duration(seconds: 2),
      snackPosition: SnackPosition.BOTTOM,
      margin: const EdgeInsets.all(16),
    );
  }
}
