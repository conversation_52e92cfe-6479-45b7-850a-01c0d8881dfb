import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/models/transaction.dart';
import '../../../core/models/sms_transaction.dart';
import '../../../core/models/classification_rule.dart';
import '../../../core/services/transaction_classifier_service.dart';

class TrainingTransactionCard extends StatefulWidget {
  final Transaction transaction;
  final Function(String category, TransactionType type)? onClassified;
  final VoidCallback? onSkipped;

  const TrainingTransactionCard({
    super.key,
    required this.transaction,
    this.onClassified,
    this.onSkipped,
  });

  @override
  State<TrainingTransactionCard> createState() => _TrainingTransactionCardState();
}

class _TrainingTransactionCardState extends State<TrainingTransactionCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  
  final TransactionClassifierService _classifierService = Get.find();
  
  String _selectedCategory = '';
  TransactionType _selectedType = TransactionType.expense;
  bool _showCategorySelection = false;
  TransactionClassification? _aiSuggestion;

  final List<String> _commonCategories = [
    'Groceries',
    'Transportation',
    'Utilities',
    'Entertainment',
    'Healthcare',
    'Shopping',
    'Dining',
    'Education',
    'Salary',
    'Freelance',
    'Investment',
    'Other',
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _getAiSuggestion();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _getAiSuggestion() {
    _aiSuggestion = _classifierService.classifyTransaction(widget.transaction);
    if (_aiSuggestion != null && _aiSuggestion!.confidence > 0.5) {
      _selectedCategory = _aiSuggestion!.category;
      _selectedType = _aiSuggestion!.suggestedType;
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            margin: const EdgeInsets.only(bottom: 16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.08),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              children: [
                _buildTransactionHeader(),
                if (_aiSuggestion != null) _buildAiSuggestion(),
                if (_showCategorySelection) _buildCategorySelection(),
                _buildActions(),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildTransactionHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: widget.transaction.type == TransactionType.income
                      ? AppColors.success.withOpacity(0.1)
                      : AppColors.error.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  widget.transaction.type == TransactionType.income
                      ? Icons.trending_up_rounded
                      : Icons.trending_down_rounded,
                  color: widget.transaction.type == TransactionType.income
                      ? AppColors.success
                      : AppColors.error,
                  size: 20,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'KES ${widget.transaction.amount.toStringAsFixed(2)}',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: widget.transaction.type == TransactionType.income
                            ? AppColors.success
                            : AppColors.error,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      widget.transaction.description ?? 'No description',
                      style: const TextStyle(
                        fontSize: 14,
                        color: AppColors.textSecondary,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Text(
                  'Needs Training',
                  style: TextStyle(
                    color: Colors.orange,
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Icon(
                Icons.calendar_today_rounded,
                size: 16,
                color: Colors.grey[600],
              ),
              const SizedBox(width: 8),
              Text(
                _formatDate(widget.transaction.date),
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Text(
                  widget.transaction.category as String,
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.grey[700],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAiSuggestion() {
    if (_aiSuggestion == null) return const SizedBox.shrink();
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.primary.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.primary.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.auto_awesome_rounded,
                color: AppColors.primary,
                size: 18,
              ),
              const SizedBox(width: 8),
              Text(
                'AI Suggestion',
                style: TextStyle(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
              const Spacer(),
              _buildConfidenceIndicator(_aiSuggestion!.confidence),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _aiSuggestion!.category,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: _aiSuggestion!.suggestedType == TransactionType.income
                      ? AppColors.success.withOpacity(0.2)
                      : AppColors.error.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _aiSuggestion!.suggestedType.name.toUpperCase(),
                  style: TextStyle(
                    color: _aiSuggestion!.suggestedType == TransactionType.income
                        ? AppColors.success
                        : AppColors.error,
                    fontWeight: FontWeight.w600,
                    fontSize: 10,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Method: ${_aiSuggestion!.methodDisplayName}',
            style: TextStyle(
              color: AppColors.primary,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConfidenceIndicator(double confidence) {
    Color color;
    String label;
    
    if (confidence >= 0.8) {
      color = AppColors.success;
      label = 'High';
    } else if (confidence >= 0.6) {
      color = Colors.orange;
      label = 'Medium';
    } else {
      color = AppColors.error;
      label = 'Low';
    }

    return Row(
      children: [
        Container(
          width: 40,
          height: 4,
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(2),
          ),
          child: FractionallySizedBox(
            alignment: Alignment.centerLeft,
            widthFactor: confidence,
            child: Container(
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
        ),
        const SizedBox(width: 6),
        Text(
          label,
          style: TextStyle(
            color: color,
            fontWeight: FontWeight.w600,
            fontSize: 10,
          ),
        ),
      ],
    );
  }

  Widget _buildCategorySelection() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Select Category',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _commonCategories.map((category) {
              final isSelected = _selectedCategory == category;
              return GestureDetector(
                onTap: () {
                  HapticFeedback.lightImpact();
                  setState(() {
                    _selectedCategory = category;
                  });
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: isSelected ? AppColors.primary : Colors.grey[100],
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: isSelected ? AppColors.primary : Colors.grey[300]!,
                    ),
                  ),
                  child: Text(
                    category,
                    style: TextStyle(
                      color: isSelected ? Colors.white : AppColors.textPrimary,
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
          const SizedBox(height: 16),
          const Text(
            'Transaction Type',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    HapticFeedback.lightImpact();
                    setState(() {
                      _selectedType = TransactionType.income;
                    });
                  },
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: _selectedType == TransactionType.income
                          ? AppColors.success.withOpacity(0.1)
                          : Colors.grey[100],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: _selectedType == TransactionType.income
                            ? AppColors.success
                            : Colors.grey[300]!,
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.trending_up_rounded,
                          color: _selectedType == TransactionType.income
                              ? AppColors.success
                              : Colors.grey[600],
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Income',
                          style: TextStyle(
                            color: _selectedType == TransactionType.income
                                ? AppColors.success
                                : AppColors.textPrimary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    HapticFeedback.lightImpact();
                    setState(() {
                      _selectedType = TransactionType.expense;
                    });
                  },
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: _selectedType == TransactionType.expense
                          ? AppColors.error.withOpacity(0.1)
                          : Colors.grey[100],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: _selectedType == TransactionType.expense
                            ? AppColors.error
                            : Colors.grey[300]!,
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.trending_down_rounded,
                          color: _selectedType == TransactionType.expense
                              ? AppColors.error
                              : Colors.grey[600],
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Expense',
                          style: TextStyle(
                            color: _selectedType == TransactionType.expense
                                ? AppColors.error
                                : AppColors.textPrimary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActions() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          if (_aiSuggestion != null && _aiSuggestion!.confidence > 0.6)
            Expanded(
              child: TextButton.icon(
                onPressed: _acceptAiSuggestion,
                icon: const Icon(Icons.check_rounded),
                label: const Text('Accept AI'),
                style: TextButton.styleFrom(
                  foregroundColor: AppColors.success,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          if (_aiSuggestion != null && _aiSuggestion!.confidence > 0.6)
            const SizedBox(width: 8),
          Expanded(
            child: TextButton.icon(
              onPressed: _showManualSelection,
              icon: const Icon(Icons.edit_rounded),
              label: const Text('Manual'),
              style: TextButton.styleFrom(
                foregroundColor: AppColors.primary,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: TextButton.icon(
              onPressed: widget.onSkipped,
              icon: const Icon(Icons.skip_next_rounded),
              label: const Text('Skip'),
              style: TextButton.styleFrom(
                foregroundColor: Colors.grey[600],
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
          if (_showCategorySelection) ...[
            const SizedBox(width: 8),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _selectedCategory.isNotEmpty ? _confirmClassification : null,
                icon: const Icon(Icons.save_rounded),
                label: const Text('Save'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  void _acceptAiSuggestion() {
    if (_aiSuggestion != null) {
      _animationController.forward().then((_) {
        _animationController.reverse();
        if (widget.onClassified != null) {
          widget.onClassified!(
            _aiSuggestion!.category,
            _aiSuggestion!.suggestedType,
          );
        }
      });
    }
  }

  void _showManualSelection() {
    setState(() {
      _showCategorySelection = !_showCategorySelection;
    });
  }

  void _confirmClassification() {
    if (_selectedCategory.isNotEmpty) {
      _animationController.forward().then((_) {
        _animationController.reverse();
        if (widget.onClassified != null) {
          widget.onClassified!(_selectedCategory, _selectedType);
        }
      });
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

// SMS Transaction Card
class TrainingSmsTransactionCard extends StatefulWidget {
  final SmsTransaction smsTransaction;
  final Function(String category, TransactionType type)? onClassified;
  final VoidCallback? onSkipped;

  const TrainingSmsTransactionCard({
    super.key,
    required this.smsTransaction,
    this.onClassified,
    this.onSkipped,
  });

  @override
  State<TrainingSmsTransactionCard> createState() => _TrainingSmsTransactionCardState();
}

class _TrainingSmsTransactionCardState extends State<TrainingSmsTransactionCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  
  final TransactionClassifierService _classifierService = Get.find();
  
  String _selectedCategory = '';
  TransactionType _selectedType = TransactionType.expense;
  bool _showCategorySelection = false;
  TransactionClassification? _aiSuggestion;

  final List<String> _commonCategories = [
    'Mobile Money',
    'Bank Transfer',
    'Utilities',
    'Airtime',
    'Shopping',
    'Transportation',
    'Salary',
    'Other',
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _getAiSuggestion();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _getAiSuggestion() {
    _aiSuggestion = _classifierService.classifySmsTransaction(widget.smsTransaction);
    if (_aiSuggestion != null && _aiSuggestion!.confidence > 0.5) {
      _selectedCategory = _aiSuggestion!.category;
      _selectedType = _aiSuggestion!.suggestedType;
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            margin: const EdgeInsets.only(bottom: 16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.08),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              children: [
                _buildSmsHeader(),
                if (_aiSuggestion != null) _buildAiSuggestion(),
                if (_showCategorySelection) _buildCategorySelection(),
                _buildActions(),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSmsHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.secondary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.sms_rounded,
                  color: AppColors.secondary,
                  size: 20,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'KES ${widget.smsTransaction.amount?.toStringAsFixed(2)}',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: widget.smsTransaction.transactionType == TransactionType.income
                            ? AppColors.success
                            : AppColors.error,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'From: ${widget.smsTransaction.sender}',
                      style: const TextStyle(
                        fontSize: 12,
                        color: AppColors.textSecondary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: AppColors.secondary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Text(
                  'SMS',
                  style: TextStyle(
                    color: AppColors.secondary,
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              widget.smsTransaction.rawMessage,
              style: const TextStyle(
                fontSize: 12,
                color: AppColors.textSecondary,
              ),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(
                Icons.access_time_rounded,
                size: 16,
                color: Colors.grey[600],
              ),
              const SizedBox(width: 8),
              Text(
                _formatDate(widget.smsTransaction.createdAt),
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
              if (widget.smsTransaction.referenceNumber != null) ...[
                const Spacer(),
                Text(
                  'Ref: ${widget.smsTransaction.referenceNumber}',
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.grey[600],
                    fontFamily: 'monospace',
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAiSuggestion() {
    if (_aiSuggestion == null) return const SizedBox.shrink();
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.secondary.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.secondary.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.auto_awesome_rounded,
                color: AppColors.secondary,
                size: 18,
              ),
              const SizedBox(width: 8),
              Text(
                'AI Suggestion',
                style: TextStyle(
                  color: AppColors.secondary,
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
              const Spacer(),
              _buildConfidenceIndicator(_aiSuggestion!.confidence),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: AppColors.secondary,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _aiSuggestion!.category,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: _aiSuggestion!.suggestedType == TransactionType.income
                      ? AppColors.success.withOpacity(0.2)
                      : AppColors.error.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _aiSuggestion!.suggestedType.name.toUpperCase(),
                  style: TextStyle(
                    color: _aiSuggestion!.suggestedType == TransactionType.income
                        ? AppColors.success
                        : AppColors.error,
                    fontWeight: FontWeight.w600,
                    fontSize: 10,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildConfidenceIndicator(double confidence) {
    Color color;
    String label;
    
    if (confidence >= 0.8) {
      color = AppColors.success;
      label = 'High';
    } else if (confidence >= 0.6) {
      color = Colors.orange;
      label = 'Medium';
    } else {
      color = AppColors.error;
      label = 'Low';
    }

    return Row(
      children: [
        Container(
          width: 40,
          height: 4,
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(2),
          ),
          child: FractionallySizedBox(
            alignment: Alignment.centerLeft,
            widthFactor: confidence,
            child: Container(
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
        ),
        const SizedBox(width: 6),
        Text(
          label,
          style: TextStyle(
            color: color,
            fontWeight: FontWeight.w600,
            fontSize: 10,
          ),
        ),
      ],
    );
  }

  Widget _buildCategorySelection() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Select Category',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _commonCategories.map((category) {
              final isSelected = _selectedCategory == category;
              return GestureDetector(
                onTap: () {
                  HapticFeedback.lightImpact();
                  setState(() {
                    _selectedCategory = category;
                  });
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: isSelected ? AppColors.secondary : Colors.grey[100],
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: isSelected ? AppColors.secondary : Colors.grey[300]!,
                    ),
                  ),
                  child: Text(
                    category,
                    style: TextStyle(
                      color: isSelected ? Colors.white : AppColors.textPrimary,
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
          const SizedBox(height: 16),
          const Text(
            'Transaction Type',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    HapticFeedback.lightImpact();
                    setState(() {
                      _selectedType = TransactionType.income;
                    });
                  },
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: _selectedType == TransactionType.income
                          ? AppColors.success.withOpacity(0.1)
                          : Colors.grey[100],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: _selectedType == TransactionType.income
                            ? AppColors.success
                            : Colors.grey[300]!,
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.trending_up_rounded,
                          color: _selectedType == TransactionType.income
                              ? AppColors.success
                              : Colors.grey[600],
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Income',
                          style: TextStyle(
                            color: _selectedType == TransactionType.income
                                ? AppColors.success
                                : AppColors.textPrimary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    HapticFeedback.lightImpact();
                    setState(() {
                      _selectedType = TransactionType.expense;
                    });
                  },
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: _selectedType == TransactionType.expense
                          ? AppColors.error.withOpacity(0.1)
                          : Colors.grey[100],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: _selectedType == TransactionType.expense
                            ? AppColors.error
                            : Colors.grey[300]!,
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.trending_down_rounded,
                          color: _selectedType == TransactionType.expense
                              ? AppColors.error
                              : Colors.grey[600],
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Expense',
                          style: TextStyle(
                            color: _selectedType == TransactionType.expense
                                ? AppColors.error
                                : AppColors.textPrimary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActions() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          if (_aiSuggestion != null && _aiSuggestion!.confidence > 0.6)
            Expanded(
              child: TextButton.icon(
                onPressed: _acceptAiSuggestion,
                icon: const Icon(Icons.check_rounded),
                label: const Text('Accept AI'),
                style: TextButton.styleFrom(
                  foregroundColor: AppColors.success,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          if (_aiSuggestion != null && _aiSuggestion!.confidence > 0.6)
            const SizedBox(width: 8),
          Expanded(
            child: TextButton.icon(
              onPressed: _showManualSelection,
              icon: const Icon(Icons.edit_rounded),
              label: const Text('Manual'),
              style: TextButton.styleFrom(
                foregroundColor: AppColors.secondary,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: TextButton.icon(
              onPressed: widget.onSkipped,
              icon: const Icon(Icons.skip_next_rounded),
              label: const Text('Skip'),
              style: TextButton.styleFrom(
                foregroundColor: Colors.grey[600],
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
          if (_showCategorySelection) ...[
            const SizedBox(width: 8),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _selectedCategory.isNotEmpty ? _confirmClassification : null,
                icon: const Icon(Icons.save_rounded),
                label: const Text('Save'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.secondary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  void _acceptAiSuggestion() {
    if (_aiSuggestion != null) {
      _animationController.forward().then((_) {
        _animationController.reverse();
        if (widget.onClassified != null) {
          widget.onClassified!(
            _aiSuggestion!.category,
            _aiSuggestion!.suggestedType,
          );
        }
      });
    }
  }

  void _showManualSelection() {
    setState(() {
      _showCategorySelection = !_showCategorySelection;
    });
  }

  void _confirmClassification() {
    if (_selectedCategory.isNotEmpty) {
      _animationController.forward().then((_) {
        _animationController.reverse();
        if (widget.onClassified != null) {
          widget.onClassified!(_selectedCategory, _selectedType);
        }
      });
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }
}

// Additional helper widgets for training
class BatchTrainingDialog extends StatefulWidget {
  final List<Transaction> unclassifiedTransactions;
  final List<SmsTransaction> unclassifiedSmsTransactions;
  final VoidCallback? onCompleted;

  const BatchTrainingDialog({
    super.key,
    required this.unclassifiedTransactions,
    required this.unclassifiedSmsTransactions,
    this.onCompleted,
  });

  @override
  State<BatchTrainingDialog> createState() => _BatchTrainingDialogState();
}

class _BatchTrainingDialogState extends State<BatchTrainingDialog> {
  final TransactionClassifierService _classifierService = Get.find();
  bool _isProcessing = false;
  int _processedCount = 0;
  int _totalCount = 0;

  @override
  void initState() {
    super.initState();
    _totalCount = widget.unclassifiedTransactions.length + widget.unclassifiedSmsTransactions.length;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Batch Training'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (_isProcessing) ...[
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text('Processing $_processedCount of $_totalCount transactions...'),
          ] else ...[
            Text('This will automatically classify $_totalCount transactions using AI.'),
            const SizedBox(height: 16),
            const Text('Only high-confidence classifications will be applied.'),
          ],
        ],
      ),
      actions: [
        TextButton(
          onPressed: _isProcessing ? null : () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isProcessing ? null : _startBatchProcessing,
          child: const Text('Start'),
        ),
      ],
    );
  }

  void _startBatchProcessing() async {
    setState(() {
      _isProcessing = true;
      _processedCount = 0;
    });

    // Process regular transactions
    for (final transaction in widget.unclassifiedTransactions) {
      final classification = _classifierService.classifyTransaction(transaction);
      if (classification.confidence >= 0.8) {
        // Auto-apply high-confidence classifications
        _classifierService.trainWithFeedback(
          transactionId: transaction.id,
          correctCategory: classification.category,
          correctType: classification.suggestedType,
          features: {
            'amount': transaction.amount,
            'description': transaction.description,
            'category': transaction.category,
            'type': transaction.type.name,
          },
          wasCorrect: true,
        );
      }

      setState(() {
        _processedCount++;
      });

      await Future.delayed(const Duration(milliseconds: 100));
    }

    // Process SMS transactions
    for (final smsTransaction in widget.unclassifiedSmsTransactions) {
      final classification = _classifierService.classifySmsTransaction(smsTransaction);
      if (classification.confidence >= 0.8) {
        _classifierService.trainWithFeedback(
          transactionId: smsTransaction.id,
          correctCategory: classification.category,
          correctType: classification.suggestedType,
          features: {
            'amount': smsTransaction.amount,
            'description': smsTransaction.rawMessage,
            'category': smsTransaction.formattedAmount,
            'type': smsTransaction.transactionType ,
            'sender': smsTransaction.sender,
          },
          wasCorrect: true,
        );
      }

      setState(() {
        _processedCount++;
      });

      await Future.delayed(const Duration(milliseconds: 100));
    }

    if (widget.onCompleted != null) {
      widget.onCompleted!();
    }

    Navigator.pop(context);
  }
}

class TrainingStatsDialog extends StatelessWidget {
  final Map<String, dynamic> stats;

  const TrainingStatsDialog({
    super.key,
    required this.stats,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Training Statistics'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildStatRow('Total Classifications', stats['total_classifications'].toString()),
            _buildStatRow('Correct Classifications', stats['correct_classifications'].toString()),
            _buildStatRow('Accuracy', '${(stats['accuracy'] * 100).toStringAsFixed(1)}%'),
            _buildStatRow('Active Rules', stats['rules_count'].toString()),
            _buildStatRow('Learned Rules', stats['learned_rules_count'].toString()),
            _buildStatRow('Sender Mappings', stats['sender_mappings'].toString()),
            const SizedBox(height: 16),
            const Text(
              'Category Frequency',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            ...((stats['category_frequency'] as Map<String, int>).entries.map(
              (entry) => _buildStatRow(entry.key, entry.value.toString()),
            )),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Close'),
        ),
      ],
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }
}

class TrainingHelpDialog extends StatelessWidget {
  const TrainingHelpDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Training Help'),
      content: const SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'How Training Works',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
            ),
            SizedBox(height: 8),
            Text(
              '• The AI learns from your classifications to improve future predictions\n'
              '• High-confidence suggestions can be accepted quickly\n'
              '• Manual classification helps train the AI for similar transactions\n'
              '• SMS transactions are automatically parsed and classified',
            ),
            SizedBox(height: 16),
            Text(
              'Tips for Better Training',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
            ),
            SizedBox(height: 8),
            Text(
              '• Be consistent with category names\n'
              '• Use descriptive categories (e.g., "Groceries" instead of "Food")\n'
              '• Train regularly to improve accuracy\n'
              '• Review AI suggestions before accepting',
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Got it'),
        ),
      ],
    );
  }
}
