import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/services/theme_service.dart';
import '../../../core/services/recurring_transaction_service.dart';
import '../../../core/models/recurring_transaction.dart';
import '../../common/widgets/common_app_bar.dart';
import '../../common/widgets/account_drawer.dart';
import 'add_universal_transaction_screen.dart';

class RecurringTransactionsScreen extends StatefulWidget {
  const RecurringTransactionsScreen({super.key});

  @override
  State<RecurringTransactionsScreen> createState() => _RecurringTransactionsScreenState();
}

class _RecurringTransactionsScreenState extends State<RecurringTransactionsScreen> {
  String _selectedFilter = 'All';
  final List<String> _filters = ['All', 'Active', 'Due', 'Income', 'Expense', 'Sale', 'Purchase'];

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final themeService = ThemeService.to;
      final recurringService = RecurringTransactionService.to;
      
      return Scaffold(
        backgroundColor: themeService.backgroundColor,
        appBar: CommonAppBar(
          title: 'Recurring Transactions',
          showAccountInfo: true,
          actions: [
            IconButton(
              onPressed: () {
                _showProcessDueDialog();
              },
              icon: const Icon(Icons.play_arrow_rounded),
              tooltip: 'Process Due Transactions',
            ),
            IconButton(
              onPressed: () {
                Get.to(() => const AddUniversalTransactionScreen(isRecurring: true));
              },
              icon: const Icon(Icons.add_rounded),
            ),
          ],
        ),
        endDrawer: const AccountDrawer(),
        body: Column(
          children: [
            // Filter Chips
            _buildFilterChips(themeService),
            
            // Recurring Transactions List
            Expanded(
              child: RefreshIndicator(
                onRefresh: () async {
                  // Refresh data
                },
                child: recurringService.isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _buildRecurringTransactionsList(recurringService, themeService),
              ),
            ),
          ],
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: () {
            Get.to(() => const AddUniversalTransactionScreen(isRecurring: true));
          },
          backgroundColor: themeService.primaryColor,
          child: const Icon(Icons.add_rounded, color: Colors.white),
        ),
      );
    });
  }

  Widget _buildFilterChips(ThemeService themeService) {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _filters.length,
        itemBuilder: (context, index) {
          final filter = _filters[index];
          final isSelected = _selectedFilter == filter;
          
          return Container(
            margin: const EdgeInsets.only(right: 8),
            child: FilterChip(
              label: Text(filter),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _selectedFilter = filter;
                });
              },
              backgroundColor: themeService.surfaceColor,
              selectedColor: themeService.primaryColor.withOpacity(0.2),
              labelStyle: TextStyle(
                color: isSelected ? themeService.primaryColor : themeService.textSecondaryColor,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildRecurringTransactionsList(RecurringTransactionService service, ThemeService themeService) {
    final filteredTransactions = _getFilteredTransactions(service.recurringTransactions);
    
    if (filteredTransactions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.repeat_rounded,
              size: 64,
              color: themeService.textSecondaryColor,
            ),
            const SizedBox(height: 16),
            Text(
              'No recurring transactions',
              style: TextStyle(
                fontSize: 18,
                color: themeService.textSecondaryColor,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Create your first recurring transaction',
              style: TextStyle(
                fontSize: 14,
                color: themeService.textSecondaryColor,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: filteredTransactions.length,
      itemBuilder: (context, index) {
        final transaction = filteredTransactions[index];
        return _buildRecurringTransactionCard(transaction, themeService);
      },
    );
  }

  List<RecurringTransaction> _getFilteredTransactions(List<RecurringTransaction> transactions) {
    switch (_selectedFilter) {
      case 'Active':
        return transactions.where((t) => t.shouldBeActive()).toList();
      case 'Due':
        return transactions.where((t) => t.isDue()).toList();
      case 'Income':
        return transactions.where((t) => t.type.toLowerCase() == 'income').toList();
      case 'Expense':
        return transactions.where((t) => t.type.toLowerCase() == 'expense').toList();
      case 'Sale':
        return transactions.where((t) => t.type.toLowerCase() == 'sale').toList();
      case 'Purchase':
        return transactions.where((t) => t.type.toLowerCase() == 'purchase').toList();
      default:
        return transactions;
    }
  }

  Widget _buildRecurringTransactionCard(RecurringTransaction transaction, ThemeService themeService) {
    final isDue = transaction.isDue();
    final isActive = transaction.shouldBeActive();
    
    Color typeColor;
    switch (transaction.type.toLowerCase()) {
      case 'income':
      case 'sale':
        typeColor = Colors.green;
        break;
      case 'expense':
        typeColor = Colors.red;
        break;
      case 'purchase':
        typeColor = Colors.orange;
        break;
      default:
        typeColor = Colors.blue;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: themeService.surfaceColor,
        borderRadius: BorderRadius.circular(12),
        border: isDue ? Border.all(color: Colors.orange, width: 2) : null,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: typeColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            _getTypeIcon(transaction.type),
            color: typeColor,
            size: 24,
          ),
        ),
        title: Text(
          transaction.title,
          style: TextStyle(
            color: themeService.textPrimaryColor,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              '${transaction.recurrenceType.displayName} • ${transaction.category.replaceAll('_', ' ').capitalizeFirst ?? transaction.category}',
              style: TextStyle(
                color: themeService.textSecondaryColor,
                fontSize: 12,
              ),
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Text(
                  'Next: ${transaction.nextDueDate.day}/${transaction.nextDueDate.month}/${transaction.nextDueDate.year}',
                  style: TextStyle(
                    color: isDue ? Colors.orange : themeService.textSecondaryColor,
                    fontSize: 12,
                    fontWeight: isDue ? FontWeight.w600 : FontWeight.w400,
                  ),
                ),
                if (isDue) ...[
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.orange,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Text(
                      'DUE',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
                if (!isActive) ...[
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.grey,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Text(
                      'INACTIVE',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              '\$${transaction.amount.toStringAsFixed(2)}',
              style: TextStyle(
                color: typeColor,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            PopupMenuButton<String>(
              onSelected: (value) => _handleMenuAction(value, transaction),
              itemBuilder: (context) => [
                const PopupMenuItem(value: 'edit', child: Text('Edit')),
                const PopupMenuItem(value: 'toggle', child: Text('Toggle Active')),
                const PopupMenuItem(value: 'process', child: Text('Process Now')),
                const PopupMenuItem(value: 'delete', child: Text('Delete')),
              ],
              child: Icon(
                Icons.more_vert_rounded,
                color: themeService.textSecondaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getTypeIcon(String type) {
    switch (type.toLowerCase()) {
      case 'income':
        return Icons.trending_up_rounded;
      case 'expense':
        return Icons.trending_down_rounded;
      case 'sale':
        return Icons.point_of_sale_rounded;
      case 'purchase':
        return Icons.shopping_cart_rounded;
      default:
        return Icons.repeat_rounded;
    }
  }

  void _handleMenuAction(String action, RecurringTransaction transaction) {
    switch (action) {
      case 'edit':
        // TODO: Navigate to edit screen
        break;
      case 'toggle':
        RecurringTransactionService.to.updateRecurringTransactionWithParams(
          transaction.id,
          isActive: !transaction.isActive,
        );
        break;
      case 'process':
        _processTransaction(transaction);
        break;
      case 'delete':
        _showDeleteDialog(transaction);
        break;
    }
  }

  void _processTransaction(RecurringTransaction transaction) async {
    try {
      // Show processing dialog
      Get.dialog(
        const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('Processing transaction...'),
            ],
          ),
        ),
        barrierDismissible: false,
      );

      // Process the single recurring transaction
      final success = await RecurringTransactionService.to.processSpecificTransaction(transaction.id);

      // Close processing dialog
      Get.back();

      if (success) {
        Get.snackbar(
          'Success',
          '${transaction.title} processed successfully',
          backgroundColor: Colors.green,
          colorText: Colors.white,
          duration: const Duration(seconds: 2),
        );
      } else {
        Get.snackbar(
          'Error',
          'Failed to process ${transaction.title}',
          backgroundColor: Colors.red,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
        );
      }
    } catch (e) {
      // Close processing dialog if still open
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      Get.snackbar(
        'Error',
        'Failed to process transaction: ${e.toString()}',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );
    }
  }

  void _showDeleteDialog(RecurringTransaction transaction) {
    Get.dialog(
      AlertDialog(
        title: const Text('Delete Recurring Transaction'),
        content: Text('Are you sure you want to delete "${transaction.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              RecurringTransactionService.to.deleteRecurringTransaction(transaction.id);
              Get.back();
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _showProcessDueDialog() async {
    final dueCount = RecurringTransactionService.to.getDueTransactions().length;
    
    if (dueCount == 0) {
      Get.snackbar(
        'No Due Transactions',
        'There are no recurring transactions due for processing',
        backgroundColor: Colors.blue,
        colorText: Colors.white,
      );
      return;
    }

    Get.dialog(
      AlertDialog(
        title: const Text('Process Due Transactions'),
        content: Text('Process $dueCount due recurring transactions?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Get.back();
              final processed = await RecurringTransactionService.to.processDueTransactions();
              Get.snackbar(
                'Processed',
                'Processed $processed recurring transactions',
                backgroundColor: Colors.green,
                colorText: Colors.white,
              );
            },
            child: const Text('Process'),
          ),
        ],
      ),
    );
  }
}
