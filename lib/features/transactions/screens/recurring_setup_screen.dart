import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/models/recurring_transaction.dart';
import '../../../core/services/recurring_transaction_service.dart';
import '../../../shared/widgets/gradient_button.dart';
import '../../../shared/widgets/sophisticated_text_field.dart';

class RecurringSetupScreen extends StatefulWidget {
  final RecurringTransaction? existingTransaction;

  const RecurringSetupScreen({
    super.key,
    this.existingTransaction,
  });

  @override
  State<RecurringSetupScreen> createState() => _RecurringSetupScreenState();
}

class _RecurringSetupScreenState extends State<RecurringSetupScreen>
    with TickerProviderStateMixin {
  final RecurringTransactionService _recurringService = Get.find();
  
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();

  RecurrenceType _selectedRecurrence = RecurrenceType.monthly;
  String _selectedType = 'income';
  String _selectedCategory = 'salary';
  DateTime _startDate = DateTime.now();
  DateTime? _endDate;
  bool _hasEndDate = false;
  int _dayOfMonth = DateTime.now().day;
  int _dayOfWeek = DateTime.now().weekday;

  final List<String> _incomeCategories = [
    'salary', 'freelance', 'business', 'investment', 'rental', 'other'
  ];
  
  final List<String> _expenseCategories = [
    'rent', 'utilities', 'insurance', 'subscription', 'loan', 'other'
  ];

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadExistingData();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  void _loadExistingData() {
    if (widget.existingTransaction != null) {
      final transaction = widget.existingTransaction!;
      _titleController.text = transaction.title;
      _amountController.text = transaction.amount.toString();
      _notesController.text = transaction.description ?? '';
      _selectedRecurrence = transaction.recurrenceType;
      _selectedType = transaction.type;
      _selectedCategory = transaction.category;
      _startDate = transaction.startDate;
      _endDate = transaction.endDate;
      _hasEndDate = transaction.endDate != null;
      _dayOfMonth = transaction.dayOfMonth ?? DateTime.now().day;
      _dayOfWeek = transaction.dayOfWeek ?? DateTime.now().weekday;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _titleController.dispose();
    _amountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: AppColors.textPrimary),
          onPressed: () => Get.back(),
        ),
        title: Text(
          widget.existingTransaction != null ? 'Edit Recurring' : 'Setup Recurring',
          style: const TextStyle(
            color: AppColors.textPrimary,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildTransactionTypeSelector(),
                  const SizedBox(height: 24),
                  _buildTitleField(),
                  const SizedBox(height: 20),
                  _buildAmountField(),
                  const SizedBox(height: 20),
                  _buildCategorySelector(),
                  const SizedBox(height: 24),
                  _buildRecurrenceSelector(),
                  const SizedBox(height: 20),
                  _buildDateSettings(),
                  const SizedBox(height: 20),
                  _buildNotesField(),
                  const SizedBox(height: 32),
                  _buildSaveButton(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTransactionTypeSelector() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildTypeOption(
              'Income',
              'income',
              Icons.trending_up,
              AppColors.success,
            ),
          ),
          Expanded(
            child: _buildTypeOption(
              'Expense',
              'expense',
              Icons.trending_down,
              AppColors.error,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTypeOption(String label, String type, IconData icon, Color color) {
    final isSelected = _selectedType == type;
    
    return GestureDetector(
      onTap: () => setState(() => _selectedType = type),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          color: isSelected ? color.withOpacity(0.1) : Colors.transparent,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected ? color : AppColors.textSecondary,
              size: 24,
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? color : AppColors.textSecondary,
                fontSize: 14,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTitleField() {
    return SophisticatedTextField(
      controller: _titleController,
      label: 'Transaction Title',
      hint: 'e.g., Monthly Salary, Rent Payment',
      validator: (value) {
        if (value?.isEmpty ?? true) {
          return 'Please enter a title';
        }
        return null;
      },
    );
  }

  Widget _buildAmountField() {
    return SophisticatedTextField(
      controller: _amountController,
      label: 'Amount',
      hint: '0.00',
      keyboardType: TextInputType.number,
      validator: (value) {
        if (value?.isEmpty ?? true) {
          return 'Please enter an amount';
        }
        if (double.tryParse(value!) == null) {
          return 'Please enter a valid amount';
        }
        return null;
      },
    );
  }

  Widget _buildCategorySelector() {
    final categories = _selectedType == 'income'
        ? _incomeCategories 
        : _expenseCategories;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Category',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: categories.map((category) {
            final isSelected = _selectedCategory == category;
            return GestureDetector(
              onTap: () => setState(() => _selectedCategory = category),
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: isSelected ? AppColors.primary : Colors.white,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: isSelected ? AppColors.primary : AppColors.border,
                  ),
                ),
                child: Text(
                  category.toUpperCase(),
                  style: TextStyle(
                    color: isSelected ? Colors.white : AppColors.textSecondary,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildRecurrenceSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Recurrence',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppColors.border),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<RecurrenceType>(
              value: _selectedRecurrence,
              isExpanded: true,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              items: RecurrenceType.values.map((type) {
                return DropdownMenuItem(
                  value: type,
                  child: Text(
                    type.toString().split('.').last.toUpperCase(),
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() => _selectedRecurrence = value);
                }
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDateSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Schedule',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 12),
        _buildStartDateField(),
        const SizedBox(height: 16),
        _buildEndDateToggle(),
        if (_hasEndDate) ...[
          const SizedBox(height: 16),
          _buildEndDateField(),
        ],
      ],
    );
  }

  Widget _buildStartDateField() {
    return GestureDetector(
      onTap: _selectStartDate,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: AppColors.border),
        ),
        child: Row(
          children: [
            const Icon(Icons.calendar_today, color: AppColors.primary),
            const SizedBox(width: 12),
            Text(
              'Start Date: ${DateFormat('MMM dd, yyyy').format(_startDate)}',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEndDateToggle() {
    return Row(
      children: [
        Checkbox(
          value: _hasEndDate,
          onChanged: (value) => setState(() => _hasEndDate = value ?? false),
          activeColor: AppColors.primary,
        ),
        const Text(
          'Set end date',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildEndDateField() {
    return GestureDetector(
      onTap: _selectEndDate,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: AppColors.border),
        ),
        child: Row(
          children: [
            const Icon(Icons.event, color: AppColors.primary),
            const SizedBox(width: 12),
            Text(
              _endDate != null 
                  ? 'End Date: ${DateFormat('MMM dd, yyyy').format(_endDate!)}'
                  : 'Select end date',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesField() {
    return SophisticatedTextField(
      controller: _notesController,
      label: 'Notes (Optional)',
      hint: 'Add any additional notes...',
      maxLines: 3,
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      child: GradientButton(
        text: widget.existingTransaction != null ? 'Update Recurring' : 'Create Recurring',
        onPressed: _saveRecurringTransaction,
      ),
    );
  }

  void _selectStartDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _startDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365 * 5)),
    );
    if (date != null) {
      setState(() => _startDate = date);
    }
  }

  void _selectEndDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _endDate ?? _startDate.add(const Duration(days: 365)),
      firstDate: _startDate,
      lastDate: DateTime.now().add(const Duration(days: 365 * 10)),
    );
    if (date != null) {
      setState(() => _endDate = date);
    }
  }

  void _saveRecurringTransaction() async {
    if (!_formKey.currentState!.validate()) return;

    try {
      final recurringTransaction = RecurringTransaction(
        id: widget.existingTransaction?.id ?? 'recurring_${DateTime.now().millisecondsSinceEpoch}',
        accountId: widget.existingTransaction?.accountId ?? '',
        title: _titleController.text,
        amount: double.parse(_amountController.text),
        type: _selectedType,
        category: _selectedCategory,
        recurrenceType: _selectedRecurrence,
        startDate: _startDate,
        endDate: _hasEndDate ? _endDate : null,
        dayOfMonth: _selectedRecurrence == RecurrenceType.monthly ? _dayOfMonth : null,
        dayOfWeek: _selectedRecurrence == RecurrenceType.weekly ? _dayOfWeek : null,
        notes: _notesController.text.isEmpty ? null : _notesController.text,
        isActive: true,
        nextDueDate: _calculateNextDueDate(),
        createdAt: widget.existingTransaction?.createdAt ?? DateTime.now(),
      );

      if (widget.existingTransaction != null) {
        await _recurringService.updateRecurringTransaction(recurringTransaction);
        Get.snackbar(
          'Success',
          'Recurring transaction updated successfully',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.success,
          colorText: Colors.white,
        );
      } else {
        await _recurringService.addRecurringTransaction(recurringTransaction);
        Get.snackbar(
          'Success',
          'Recurring transaction created successfully',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.success,
          colorText: Colors.white,
        );
      }

      Get.back();
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to save recurring transaction: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error,
        colorText: Colors.white,
      );
    }
  }

  DateTime _calculateNextDueDate() {
    switch (_selectedRecurrence) {
      case RecurrenceType.daily:
        return _startDate.add(const Duration(days: 1));
      case RecurrenceType.weekly:
        return _startDate.add(const Duration(days: 7));
      case RecurrenceType.biweekly:
        return _startDate.add(const Duration(days: 14));
      case RecurrenceType.monthly:
        return DateTime(_startDate.year, _startDate.month + 1, _dayOfMonth);
      case RecurrenceType.quarterly:
        return DateTime(_startDate.year, _startDate.month + 3, _startDate.day);
      case RecurrenceType.yearly:
        return DateTime(_startDate.year + 1, _startDate.month, _startDate.day);
    }
  }
}
