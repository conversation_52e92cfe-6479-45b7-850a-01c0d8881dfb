import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../../core/theme/design_system.dart';
import '../../../core/theme/typography.dart';
import '../../../core/services/personal_transaction_service.dart';
import '../../../core/database/database.dart';
import '../../common/widgets/common_app_bar.dart';
import '../widgets/transaction_card.dart';
import '../widgets/date_range_picker_widget.dart';
import '../../../shared/widgets/universal_transaction_dialog.dart';

class TransactionsScreen extends StatefulWidget {
  const TransactionsScreen({super.key});

  @override
  State<TransactionsScreen> createState() => _TransactionsScreenState();
}

class _TransactionsScreenState extends State<TransactionsScreen> {
  final ScrollController _scrollController = ScrollController();
  String _selectedFilter = 'All';
  final List<String> _filterOptions = ['All', 'Income', 'Expense'];
  DateTimeRange? _selectedDateRange;
  bool _showDatePicker = false;

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DesignSystem.backgroundPrimary,
      appBar: CommonAppBar(
        title: 'Transactions',
        showAccountInfo: true,
        actions: [
          IconButton(
            onPressed: _toggleDatePicker,
            icon: Icon(
              _showDatePicker ? Icons.date_range : Icons.date_range_outlined,
              color: _showDatePicker ? DesignSystem.primaryTeal : DesignSystem.textPrimary,
            ),
          ),
          IconButton(
            onPressed: _showFilterOptions,
            icon: Icon(
              Icons.filter_list_rounded,
              color: DesignSystem.textPrimary,
            ),
          ),
          IconButton(
            onPressed: () {
              Get.toNamed('/add-transaction');
            },
            icon: Icon(
              Icons.add_rounded,
              color: DesignSystem.textPrimary,
            ),
          ),
        ],
      ),
      body: Obx(() {
        final transactionService = PersonalTransactionService.to;
        final transactions = _getFilteredTransactions(transactionService.transactions);

        if (transactionService.isLoading) {
          return Center(
            child: CircularProgressIndicator(
              color: DesignSystem.primaryTeal,
            ),
          );
        }

        if (transactions.isEmpty) {
          return _buildEmptyState();
        }

        final filteredTransactions = _getFilteredTransactions(transactions);
        final groupedTransactions = _groupTransactionsByDate(filteredTransactions);

        return RefreshIndicator(
          onRefresh: () async {
            await transactionService.refreshData();
          },
          color: DesignSystem.primaryTeal,
          child: CustomScrollView(
            controller: _scrollController,
            slivers: [
              // Date Range Picker
              if (_showDatePicker)
                SliverToBoxAdapter(
                  child: Padding(
                    padding: EdgeInsets.all(DesignSystem.spacingMedium),
                    child: DateRangePickerWidget(
                      initialDateRange: _selectedDateRange,
                      onDateRangeChanged: _onDateRangeChanged,
                      onDaySelected: _onDaySelected,
                    ),
                  ),
                ),

              // Filter chips
              SliverToBoxAdapter(
                child: _buildFilterChips(),
              ),

              // Transactions list
              ...groupedTransactions.entries.map((entry) {
                return _buildDateGroup(entry.key, entry.value);
              }).toList(),

              // Bottom spacing
              const SliverToBoxAdapter(
                child: SizedBox(height: 100),
              ),
            ],
          ),
        );
      }),
    );
  }

  List<PersonalTransaction> _getFilteredTransactions(List<PersonalTransaction> transactions) {
    var filtered = transactions;

    // Filter by type
    if (_selectedFilter != 'All') {
      filtered = filtered.where((t) => t.type == _selectedFilter.toLowerCase()).toList();
    }

    // Filter by date range
    if (_selectedDateRange != null) {
      filtered = filtered.where((t) {
        return t.date.isAfter(_selectedDateRange!.start.subtract(const Duration(days: 1))) &&
               t.date.isBefore(_selectedDateRange!.end.add(const Duration(days: 1)));
      }).toList();
    }

    return filtered;
  }

  Map<String, List<PersonalTransaction>> _groupTransactionsByDate(List<PersonalTransaction> transactions) {
    final Map<String, List<PersonalTransaction>> grouped = {};

    for (final transaction in transactions) {
      final dateKey = DateFormat('yyyy-MM-dd').format(transaction.date);
      grouped.putIfAbsent(dateKey, () => []).add(transaction);
    }

    // Sort by date descending
    final sortedEntries = grouped.entries.toList()
      ..sort((a, b) => b.key.compareTo(a.key));

    return Map.fromEntries(sortedEntries);
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.receipt_long_rounded,
            size: 80,
            color: DesignSystem.textSecondary,
          ),
          SizedBox(height: DesignSystem.spacingMedium),
          Text(
            'No Transactions Yet',
            style: AppTypography.titleLarge.copyWith(
              fontWeight: FontWeight.w600,
              color: DesignSystem.textPrimary,
            ),
          ),
          SizedBox(height: DesignSystem.spacingSmall),
          Text(
            'Start tracking your finances by adding your first transaction',
            style: AppTypography.bodyMedium.copyWith(
              color: DesignSystem.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: DesignSystem.spacingLarge),
          ElevatedButton.icon(
            onPressed: () {
              Get.toNamed('/add-transaction');
            },
            icon: const Icon(Icons.add_rounded),
            label: const Text('Add Transaction'),
            style: ElevatedButton.styleFrom(
              backgroundColor: DesignSystem.primaryTeal,
              foregroundColor: DesignSystem.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChips() {
    return Container(
      padding: EdgeInsets.all(DesignSystem.spacingMedium),
      child: Row(
        children: _filterOptions.map((filter) {
          final isSelected = _selectedFilter == filter;
          return Padding(
            padding: EdgeInsets.only(right: DesignSystem.spacingSmall),
            child: FilterChip(
              label: Text(filter),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _selectedFilter = filter;
                });
              },
              backgroundColor: DesignSystem.backgroundSecondary,
              selectedColor: DesignSystem.primaryTeal.withOpacity(0.2),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildDateGroup(String dateKey, List<PersonalTransaction> transactions) {
    final date = DateTime.parse(dateKey);
    final isToday = DateFormat('yyyy-MM-dd').format(DateTime.now()) == dateKey;
    final isYesterday = DateFormat('yyyy-MM-dd').format(DateTime.now().subtract(const Duration(days: 1))) == dateKey;

    String dateLabel;
    if (isToday) {
      dateLabel = 'Today';
    } else if (isYesterday) {
      dateLabel = 'Yesterday';
    } else {
      dateLabel = DateFormat('EEEE, MMM d').format(date);
    }

    return SliverMainAxisGroup(
      slivers: [
        // Date header
        SliverToBoxAdapter(
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: DesignSystem.spacingMedium,
              vertical: DesignSystem.spacingSmall,
            ),
            margin: EdgeInsets.only(top: DesignSystem.spacingMedium),
            child: Row(
              children: [
                Text(
                  dateLabel,
                  style: AppTypography.titleMedium.copyWith(
                    fontWeight: FontWeight.w600,
                    color: DesignSystem.textPrimary,
                  ),
                ),
                const Spacer(),
                Text(
                  DateFormat('MMM d, yyyy').format(date),
                  style: AppTypography.bodySmall.copyWith(
                    color: DesignSystem.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ),

        // Transactions for this date
        SliverList(
          delegate: SliverChildBuilderDelegate(
            (context, index) {
              final transaction = transactions[index];
              return Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: DesignSystem.spacingMedium,
                  vertical: DesignSystem.spacingXSmall,
                ),
                child: TransactionCard(
                  transaction: transaction,
                  onTap: () => _onTransactionTap(transaction),
                  onEdit: () => _onTransactionEdit(transaction),
                  onDelete: () => _onTransactionDelete(transaction),
                  onDuplicate: () => _onTransactionDuplicate(transaction),
                ),
              );
            },
            childCount: transactions.length,
          ),
        ),
      ],
    );
  }

  void _toggleDatePicker() {
    setState(() {
      _showDatePicker = !_showDatePicker;
    });
  }

  void _onDateRangeChanged(DateTimeRange? dateRange) {
    setState(() {
      _selectedDateRange = dateRange;
    });
  }

  void _onDaySelected(int day) {
    // Day selection is handled in the date range picker widget
    // This callback can be used for additional logic if needed
  }

  void _showFilterOptions() {
    // TODO: Implement advanced filter options
  }

  void _onTransactionTap(PersonalTransaction transaction) {
    // TODO: Show transaction details
  }

  void _onTransactionEdit(PersonalTransaction transaction) {
    UniversalTransactionDialog.showEdit(transaction);
  }

  void _onTransactionDuplicate(PersonalTransaction transaction) {
    UniversalTransactionDialog.showMultiple(
      templateTransaction: transaction,
    );
  }

  void _onTransactionDelete(PersonalTransaction transaction) {
    // TODO: Show delete confirmation
  }
}
