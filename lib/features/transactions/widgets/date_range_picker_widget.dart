import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../../core/theme/design_system.dart';
import '../../../core/theme/typography.dart';

class DateRangePickerWidget extends StatefulWidget {
  final DateTimeRange? initialDateRange;
  final Function(DateTimeRange?) onDateRangeChanged;
  final Function(int) onDaySelected;

  const DateRangePickerWidget({
    super.key,
    this.initialDateRange,
    required this.onDateRangeChanged,
    required this.onDaySelected,
  });

  @override
  State<DateRangePickerWidget> createState() => _DateRangePickerWidgetState();
}

class _DateRangePickerWidgetState extends State<DateRangePickerWidget> {
  DateTimeRange? _selectedDateRange;
  int? _selectedDay;
  late List<int> _dayTabs;

  @override
  void initState() {
    super.initState();
    _selectedDateRange = widget.initialDateRange;
    _generateDayTabs();
  }

  void _generateDayTabs() {
    final now = DateTime.now();
    final daysInMonth = DateTime(now.year, now.month + 1, 0).day;
    _dayTabs = List.generate(daysInMonth, (index) => daysInMonth - index);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(DesignSystem.spacingMedium),
      decoration: BoxDecoration(
        color: DesignSystem.white,
        borderRadius: BorderRadius.circular(DesignSystem.radiusLarge),
        boxShadow: DesignSystem.shadowMedium,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Date Range Picker Header
          Row(
            children: [
              Icon(
                Icons.date_range_rounded,
                color: DesignSystem.primaryTeal,
                size: 24,
              ),
              SizedBox(width: DesignSystem.spacingSmall),
              Text(
                'Filter by Date',
                style: AppTypography.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: DesignSystem.textPrimary,
                ),
              ),
              const Spacer(),
              if (_selectedDateRange != null)
                IconButton(
                  onPressed: _clearDateRange,
                  icon: Icon(
                    Icons.clear_rounded,
                    color: DesignSystem.textSecondary,
                    size: 20,
                  ),
                ),
            ],
          ),
          
          SizedBox(height: DesignSystem.spacingMedium),
          
          // Date Range Display/Picker
          InkWell(
            onTap: _showDateRangePicker,
            borderRadius: BorderRadius.circular(DesignSystem.radiusSmall),
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.all(DesignSystem.spacingMedium),
              decoration: BoxDecoration(
                border: Border.all(
                  color: DesignSystem.textSecondary.withOpacity(0.3),
                ),
                borderRadius: BorderRadius.circular(DesignSystem.radiusSmall),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.calendar_today_rounded,
                    color: DesignSystem.textSecondary,
                    size: 20,
                  ),
                  SizedBox(width: DesignSystem.spacingSmall),
                  Text(
                    _selectedDateRange != null
                        ? '${DateFormat('MMM dd').format(_selectedDateRange!.start)} - ${DateFormat('MMM dd, yyyy').format(_selectedDateRange!.end)}'
                        : 'Select date range',
                    style: AppTypography.bodyMedium.copyWith(
                      color: _selectedDateRange != null
                          ? DesignSystem.textPrimary
                          : DesignSystem.textSecondary,
                    ),
                  ),
                  const Spacer(),
                  Icon(
                    Icons.arrow_drop_down_rounded,
                    color: DesignSystem.textSecondary,
                  ),
                ],
              ),
            ),
          ),
          
          SizedBox(height: DesignSystem.spacingMedium),
          
          // Quick Day Selection
          Text(
            'Quick Select (Current Month)',
            style: AppTypography.bodyMedium.copyWith(
              fontWeight: FontWeight.w500,
              color: DesignSystem.textPrimary,
            ),
          ),
          
          SizedBox(height: DesignSystem.spacingSmall),
          
          // Day Tabs
          SizedBox(
            height: 40,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _dayTabs.length,
              itemBuilder: (context, index) {
                final day = _dayTabs[index];
                final isSelected = _selectedDay == day;
                final isToday = day == DateTime.now().day;
                
                return Padding(
                  padding: EdgeInsets.only(right: DesignSystem.spacingSmall),
                  child: InkWell(
                    onTap: () => _selectDay(day),
                    borderRadius: BorderRadius.circular(DesignSystem.radiusSmall),
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: DesignSystem.spacingMedium,
                        vertical: DesignSystem.spacingSmall,
                      ),
                      decoration: BoxDecoration(
                        color: isSelected
                            ? DesignSystem.primaryTeal
                            : isToday
                                ? DesignSystem.primaryTeal.withOpacity(0.1)
                                : DesignSystem.backgroundSecondary,
                        borderRadius: BorderRadius.circular(DesignSystem.radiusSmall),
                        border: isToday && !isSelected
                            ? Border.all(color: DesignSystem.primaryTeal, width: 1)
                            : null,
                      ),
                      child: Center(
                        child: Text(
                          day.toString(),
                          style: AppTypography.bodyMedium.copyWith(
                            color: isSelected
                                ? DesignSystem.white
                                : isToday
                                    ? DesignSystem.primaryTeal
                                    : DesignSystem.textPrimary,
                            fontWeight: isSelected || isToday
                                ? FontWeight.w600
                                : FontWeight.normal,
                          ),
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  void _showDateRangePicker() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _selectedDateRange,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: DesignSystem.primaryTeal,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _selectedDateRange = picked;
        _selectedDay = null; // Clear day selection when range is selected
      });
      widget.onDateRangeChanged(picked);
    }
  }

  void _selectDay(int day) {
    final now = DateTime.now();
    final selectedDate = DateTime(now.year, now.month, day);
    final dateRange = DateTimeRange(
      start: selectedDate,
      end: selectedDate.add(const Duration(hours: 23, minutes: 59, seconds: 59)),
    );

    setState(() {
      _selectedDay = day;
      _selectedDateRange = dateRange;
    });

    widget.onDateRangeChanged(dateRange);
    widget.onDaySelected(day);
  }

  void _clearDateRange() {
    setState(() {
      _selectedDateRange = null;
      _selectedDay = null;
    });
    widget.onDateRangeChanged(null);
  }
}
