import 'package:flutter/material.dart';
import '../../../core/constants/app_colors.dart';
import '../../common/widgets/common_app_bar.dart';

class AnalyticsScreen extends StatefulWidget {
  const AnalyticsScreen({super.key});

  @override
  State<AnalyticsScreen> createState() => _AnalyticsScreenState();
}

class _AnalyticsScreenState extends State<AnalyticsScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        title: 'Analytics',
        showAccountInfo: true,
        actions: [
          IconButton(
            onPressed: () {
              // TODO: Show date range picker
            },
            icon: const Icon(Icons.date_range_rounded),
          ),
          IconButton(
            onPressed: () {
              // TODO: Export reports
            },
            icon: const Icon(Icons.download_rounded),
          ),
        ],
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.analytics_rounded,
              size: 80,
              color: AppColors.textSecondary,
            ),
            SizedBox(height: 20),
            Text(
              'Analytics Screen',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            SizedBox(height: 10),
            Text(
              'Coming Soon!',
              style: TextStyle(
                fontSize: 16,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
