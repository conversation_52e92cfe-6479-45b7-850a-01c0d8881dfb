import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/services/notification_service.dart';

class NotificationSettingsModal extends StatefulWidget {
  const NotificationSettingsModal({super.key});

  @override
  State<NotificationSettingsModal> createState() => _NotificationSettingsModalState();
}

class _NotificationSettingsModalState extends State<NotificationSettingsModal>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;

  // Notification settings
  bool _transactionNotifications = true;
  bool _budgetAlerts = true;
  bool _recurringReminders = true;
  bool _weeklyReports = false;
  bool _monthlyReports = true;
  bool _securityAlerts = true;
  bool _promotionalNotifications = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _slideAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _loadNotificationSettings();
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _loadNotificationSettings() {
    // Load settings from NotificationService
    // This would typically load from storage
    setState(() {
      // Set default values or load from storage
    });
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Container(
          color: Colors.black.withOpacity(0.5 * _fadeAnimation.value),
          child: SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(0, 1),
              end: Offset.zero,
            ).animate(_slideAnimation),
            child: DraggableScrollableSheet(
              initialChildSize: 0.8,
              minChildSize: 0.6,
              maxChildSize: 0.9,
              builder: (context, scrollController) {
                return Container(
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
                  ),
                  child: Column(
                    children: [
                      // Handle bar
                      Container(
                        margin: const EdgeInsets.only(top: 12),
                        width: 40,
                        height: 4,
                        decoration: BoxDecoration(
                          color: Colors.grey[300],
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                      
                      // Header
                      Padding(
                        padding: const EdgeInsets.all(24),
                        child: Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: AppColors.primary.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Icon(
                                Icons.notifications_rounded,
                                color: AppColors.primary,
                                size: 24,
                              ),
                            ),
                            const SizedBox(width: 16),
                            const Expanded(
                              child: Text(
                                'Notification Settings',
                                style: TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textPrimary,
                                ),
                              ),
                            ),
                            IconButton(
                              onPressed: _closeModal,
                              icon: const Icon(Icons.close_rounded),
                              style: IconButton.styleFrom(
                                backgroundColor: Colors.grey[100],
                                foregroundColor: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      // Settings Content
                      Expanded(
                        child: SingleChildScrollView(
                          controller: scrollController,
                          padding: const EdgeInsets.symmetric(horizontal: 24),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              _buildSectionHeader('Financial Notifications'),
                              _buildNotificationTile(
                                'Transaction Alerts',
                                'Get notified when new transactions are added',
                                Icons.receipt_rounded,
                                _transactionNotifications,
                                (value) => setState(() => _transactionNotifications = value),
                              ),
                              _buildNotificationTile(
                                'Budget Alerts',
                                'Receive alerts when approaching budget limits',
                                Icons.account_balance_wallet_rounded,
                                _budgetAlerts,
                                (value) => setState(() => _budgetAlerts = value),
                              ),
                              _buildNotificationTile(
                                'Recurring Reminders',
                                'Reminders for recurring transactions',
                                Icons.repeat_rounded,
                                _recurringReminders,
                                (value) => setState(() => _recurringReminders = value),
                              ),
                              
                              const SizedBox(height: 32),
                              
                              _buildSectionHeader('Reports & Insights'),
                              _buildNotificationTile(
                                'Weekly Reports',
                                'Weekly spending and income summaries',
                                Icons.bar_chart_rounded,
                                _weeklyReports,
                                (value) => setState(() => _weeklyReports = value),
                              ),
                              _buildNotificationTile(
                                'Monthly Reports',
                                'Detailed monthly financial reports',
                                Icons.analytics_rounded,
                                _monthlyReports,
                                (value) => setState(() => _monthlyReports = value),
                              ),
                              
                              const SizedBox(height: 32),
                              
                              _buildSectionHeader('Security & Account'),
                              _buildNotificationTile(
                                'Security Alerts',
                                'Important security and account notifications',
                                Icons.security_rounded,
                                _securityAlerts,
                                (value) => setState(() => _securityAlerts = value),
                                isImportant: true,
                              ),
                              _buildNotificationTile(
                                'Promotional Notifications',
                                'Updates about new features and offers',
                                Icons.campaign_rounded,
                                _promotionalNotifications,
                                (value) => setState(() => _promotionalNotifications = value),
                              ),
                              
                              const SizedBox(height: 32),
                              
                              _buildActionButtons(),
                              const SizedBox(height: 24),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: Colors.grey[800],
        ),
      ),
    );
  }

  Widget _buildNotificationTile(
    String title,
    String subtitle,
    IconData icon,
    bool value,
    ValueChanged<bool> onChanged, {
    bool isImportant = false,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: isImportant 
            ? Border.all(color: AppColors.primary.withOpacity(0.3))
            : null,
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: isImportant 
                  ? AppColors.primary.withOpacity(0.1)
                  : Colors.grey[200],
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              icon,
              color: isImportant ? AppColors.primary : Colors.grey[600],
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: (newValue) {
              HapticFeedback.lightImpact();
              onChanged(newValue);
            },
            activeColor: AppColors.primary,
            activeTrackColor: AppColors.primary.withOpacity(0.3),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _resetToDefaults,
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.grey[600],
              side: BorderSide(color: Colors.grey[300]!),
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text('Reset to Defaults'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          flex: 2,
          child: ElevatedButton(
            onPressed: _saveSettings,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 0,
            ),
            child: const Text(
              'Save Settings',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
        ),
      ],
    );
  }

  void _resetToDefaults() {
    setState(() {
      _transactionNotifications = true;
      _budgetAlerts = true;
      _recurringReminders = true;
      _weeklyReports = false;
      _monthlyReports = true;
      _securityAlerts = true;
      _promotionalNotifications = false;
    });
    
    HapticFeedback.lightImpact();
    Get.snackbar(
      'Reset',
      'Notification settings reset to defaults',
      backgroundColor: AppColors.primary,
      colorText: Colors.white,
    );
  }

  void _saveSettings() async {
    try {
      // Save settings to NotificationService
      // This would typically save to storage
      
      Get.snackbar(
        'Success',
        'Notification settings saved successfully',
        backgroundColor: AppColors.success,
        colorText: Colors.white,
      );
      
      _closeModal();
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to save settings: ${e.toString()}',
        backgroundColor: AppColors.error,
        colorText: Colors.white,
      );
    }
  }

  void _closeModal() {
    _animationController.reverse().then((_) {
      Navigator.of(context).pop();
    });
  }
}
