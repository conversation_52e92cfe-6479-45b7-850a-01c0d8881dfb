import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/services/theme_service.dart';
import '../../../core/services/account_service.dart';
import '../../common/widgets/common_app_bar.dart';
import '../../common/widgets/account_drawer.dart';

class PurchasesScreen extends StatefulWidget {
  const PurchasesScreen({super.key});

  @override
  State<PurchasesScreen> createState() => _PurchasesScreenState();
}

class _PurchasesScreenState extends State<PurchasesScreen> {
  String _selectedPeriod = 'This Month';
  final List<String> _periods = ['Today', 'This Week', 'This Month', 'This Year', 'All Time'];

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final themeService = ThemeService.to;
      final accountService = AccountService.to;
      
      return Scaffold(
        backgroundColor: themeService.backgroundColor,
        appBar: CommonAppBar(
          title: 'Purchases',
          showAccountInfo: true,
          actions: [
            IconButton(
              onPressed: () {
                _showFilterOptions(context);
              },
              icon: const Icon(Icons.filter_list_rounded),
            ),
            IconButton(
              onPressed: () {
                _showPurchaseReport(context);
              },
              icon: const Icon(Icons.analytics_rounded),
            ),
          ],
        ),
        endDrawer: const AccountDrawer(),
        body: RefreshIndicator(
          onRefresh: _refreshData,
          color: themeService.primaryColor,
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Period Selector
                _buildPeriodSelector(themeService),
                
                const SizedBox(height: 16),
                
                // Purchase Summary Cards
                _buildPurchaseSummary(themeService),
                
                const SizedBox(height: 20),
                
                // Purchase Categories
                _buildPurchaseCategories(themeService),
                
                const SizedBox(height: 20),
                
                // Recent Purchases
                _buildRecentPurchases(themeService),
                
                const SizedBox(height: 100), // Space for bottom nav
              ],
            ),
          ),
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: () {
            _showNewPurchaseDialog(context);
          },
          backgroundColor: Colors.orange,
          child: const Icon(Icons.shopping_cart_rounded, color: Colors.white),
        ),
      );
    });
  }

  Widget _buildPeriodSelector(ThemeService themeService) {
    return Container(
      height: 50,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _periods.length,
        itemBuilder: (context, index) {
          final period = _periods[index];
          final isSelected = period == _selectedPeriod;
          
          return Container(
            margin: const EdgeInsets.only(right: 12),
            child: FilterChip(
              label: Text(period),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _selectedPeriod = period;
                });
              },
              backgroundColor: themeService.surfaceColor,
              selectedColor: Colors.orange.withOpacity(0.2),
              labelStyle: TextStyle(
                color: isSelected ? Colors.orange : themeService.textSecondaryColor,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildPurchaseSummary(ThemeService themeService) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildSummaryCard(
                  title: 'Total Purchases',
                  amount: '\$8,240.00',
                  subtitle: 'for $_selectedPeriod',
                  color: Colors.orange,
                  icon: Icons.shopping_cart_rounded,
                  themeService: themeService,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildSummaryCard(
                  title: 'Orders',
                  amount: '89',
                  subtitle: 'total orders',
                  color: Colors.blue,
                  icon: Icons.receipt_long_rounded,
                  themeService: themeService,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildSummaryCard(
                  title: 'Average Order',
                  amount: '\$92.58',
                  subtitle: 'per order',
                  color: Colors.purple,
                  icon: Icons.analytics_rounded,
                  themeService: themeService,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildSummaryCard(
                  title: 'Pending Orders',
                  amount: '12',
                  subtitle: 'awaiting delivery',
                  color: Colors.red,
                  icon: Icons.pending_rounded,
                  themeService: themeService,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard({
    required String title,
    required String amount,
    required String subtitle,
    required Color color,
    required IconData icon,
    required ThemeService themeService,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: themeService.surfaceColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const Spacer(),
              Icon(Icons.more_vert_rounded, color: themeService.textSecondaryColor, size: 16),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: TextStyle(
              color: themeService.textSecondaryColor,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            amount,
            style: TextStyle(
              color: themeService.textPrimaryColor,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            subtitle,
            style: TextStyle(
              color: themeService.textSecondaryColor,
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPurchaseCategories(ThemeService themeService) {
    final categories = [
      {'name': 'Raw Materials', 'amount': '\$3,200.00', 'percentage': 39, 'color': Colors.brown},
      {'name': 'Equipment', 'amount': '\$2,100.00', 'percentage': 25, 'color': Colors.blue},
      {'name': 'Office Supplies', 'amount': '\$1,540.00', 'percentage': 19, 'color': Colors.green},
      {'name': 'Software & Tools', 'amount': '\$1,400.00', 'percentage': 17, 'color': Colors.purple},
    ];

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Purchase Categories',
            style: TextStyle(
              color: themeService.textPrimaryColor,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          ...categories.map((category) => _buildCategoryItem(category, themeService)),
        ],
      ),
    );
  }

  Widget _buildCategoryItem(Map<String, dynamic> category, ThemeService themeService) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: themeService.surfaceColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: category['color'].withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              _getCategoryIcon(category['name']),
              color: category['color'],
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  category['name'],
                  style: TextStyle(
                    color: themeService.textPrimaryColor,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                LinearProgressIndicator(
                  value: category['percentage'] / 100,
                  backgroundColor: Colors.grey[200],
                  valueColor: AlwaysStoppedAnimation<Color>(category['color']),
                ),
              ],
            ),
          ),
          const SizedBox(width: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                category['amount'],
                style: TextStyle(
                  color: themeService.textPrimaryColor,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '${category['percentage']}%',
                style: TextStyle(
                  color: themeService.textSecondaryColor,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  IconData _getCategoryIcon(String categoryName) {
    switch (categoryName) {
      case 'Raw Materials':
        return Icons.inventory_rounded;
      case 'Equipment':
        return Icons.build_rounded;
      case 'Office Supplies':
        return Icons.business_center_rounded;
      case 'Software & Tools':
        return Icons.computer_rounded;
      default:
        return Icons.category_rounded;
    }
  }

  Widget _buildRecentPurchases(ThemeService themeService) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Recent Purchases',
                style: TextStyle(
                  color: themeService.textPrimaryColor,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              TextButton(
                onPressed: () {
                  // Navigate to all purchases
                },
                child: Text(
                  'View All',
                  style: TextStyle(color: themeService.primaryColor),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          // Placeholder for recent purchases
          Container(
            padding: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              color: themeService.surfaceColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Center(
              child: Column(
                children: [
                  Icon(
                    Icons.shopping_cart_rounded,
                    size: 48,
                    color: themeService.textSecondaryColor,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'No recent purchases',
                    style: TextStyle(
                      color: themeService.textSecondaryColor,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showFilterOptions(BuildContext context) {
    // TODO: Implement filter options
  }

  void _showPurchaseReport(BuildContext context) {
    // TODO: Navigate to purchase analytics
  }

  void _showNewPurchaseDialog(BuildContext context) {
    // TODO: Implement new purchase dialog
  }

  Future<void> _refreshData() async {
    // TODO: Implement data refresh
    await Future.delayed(const Duration(seconds: 1));
  }
}
