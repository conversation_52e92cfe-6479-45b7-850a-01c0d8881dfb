import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/services/account_service.dart';
import '../../../core/services/subscription_service.dart';
import '../../../core/models/account_type.dart';

class QuickActionButtons extends StatelessWidget {
  const QuickActionButtons({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final accountService = AccountService.to;
      final currentAccount = accountService.currentAccount;

      if (currentAccount == null) {
        return const SizedBox.shrink();
      }

      final isPersonalAccount = currentAccount.accountType == 'personal';
      final isBusinessAccount = currentAccount.accountType == 'business';

      return Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Actions',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 16),
            
            // Personal Account Actions
            if (isPersonalAccount) ...[
              Row(
                children: [
                  Expanded(
                    child: _QuickActionButton(
                      icon: Icons.add_rounded,
                      label: 'Add Income',
                      color: AppColors.income,
                      onTap: () => _addTransaction(context, isIncome: true),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _QuickActionButton(
                      icon: Icons.remove_rounded,
                      label: 'Add Expense',
                      color: AppColors.expense,
                      onTap: () => _addTransaction(context, isIncome: false),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: _QuickActionButton(
                      icon: Icons.manage_accounts_outlined,
                      label: 'Manage Transactions',
                      color: AppColors.primary,
                      onTap: () => _manageTransactions(context),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _QuickActionButton(
                      icon: Icons.analytics_outlined,
                      label: 'View Reports',
                      color: AppColors.secondary,
                      onTap: () => _viewReports(context),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: _QuickActionButton(
                      icon: Icons.account_balance_wallet_outlined,
                      label: 'Set Budget',
                      color: AppColors.accent,
                      onTap: () => _setBudget(context),
                      isPremium: true,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _QuickActionButton(
                      icon: Icons.search_rounded,
                      label: 'Search',
                      color: AppColors.textSecondary,
                      onTap: () => _openSearch(context),
                    ),
                  ),
                ],
              ),
            ],
            
            // Business Account Actions
            if (isBusinessAccount) ...[
              Row(
                children: [
                  Expanded(
                    child: _QuickActionButton(
                      icon: Icons.inventory_2_outlined,
                      label: 'Add Product',
                      color: AppColors.primary,
                      onTap: () => _addProduct(context),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _QuickActionButton(
                      icon: Icons.receipt_long_outlined,
                      label: 'Create Invoice',
                      color: AppColors.secondary,
                      onTap: () => _createInvoice(context),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: _QuickActionButton(
                      icon: Icons.trending_up_rounded,
                      label: 'Sales Report',
                      color: AppColors.income,
                      onTap: () => _viewSalesReport(context),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _QuickActionButton(
                      icon: Icons.inventory_outlined,
                      label: 'Stock Alert',
                      color: AppColors.warning,
                      onTap: () => _viewStockAlerts(context),
                    ),
                  ),
                ],
              ),
            ],
            
            const SizedBox(height: 12),
            
            // Common Actions
            Row(
              children: [
                Expanded(
                  child: _QuickActionButton(
                    icon: Icons.backup_outlined,
                    label: 'Backup',
                    color: AppColors.info,
                    onTap: () => _backupData(context),
                    isPremium: true,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _QuickActionButton(
                    icon: Icons.sync_rounded,
                    label: 'Sync Data',
                    color: AppColors.success,
                    onTap: () => _syncData(context),
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    });
  }

  void _addTransaction(BuildContext context, {required bool isIncome}) {
    if (!SubscriptionService.to.canAddTransaction()) {
      SubscriptionService.to.showFeatureLockedDialog(PremiumFeature.unlimitedTransactions);
      return;
    }

    Get.toNamed('/add-transaction', arguments: {'isIncome': isIncome});
  }

  void _manageTransactions(BuildContext context) {
    Get.toNamed('/transaction-management');
  }

  void _viewReports(BuildContext context) {
    if (!SubscriptionService.to.isFeatureAvailable(PremiumFeature.advancedAnalytics)) {
      SubscriptionService.to.showFeatureLockedDialog(PremiumFeature.advancedAnalytics);
      return;
    }
    
    Get.toNamed('/reports');
  }

  void _setBudget(BuildContext context) {
    if (!SubscriptionService.to.isFeatureAvailable(PremiumFeature.budgetTracking)) {
      SubscriptionService.to.showFeatureLockedDialog(PremiumFeature.budgetTracking);
      return;
    }
    
    Get.toNamed('/budget');
  }

  void _addProduct(BuildContext context) {
    if (!SubscriptionService.to.isFeatureAvailable(PremiumFeature.inventoryManagement)) {
      SubscriptionService.to.showFeatureLockedDialog(PremiumFeature.inventoryManagement);
      return;
    }
    
    Get.toNamed('/add-product');
  }

  void _createInvoice(BuildContext context) {
    Get.toNamed('/create-invoice');
  }

  void _viewSalesReport(BuildContext context) {
    if (!SubscriptionService.to.isFeatureAvailable(PremiumFeature.advancedAnalytics)) {
      SubscriptionService.to.showFeatureLockedDialog(PremiumFeature.advancedAnalytics);
      return;
    }
    
    Get.toNamed('/sales-report');
  }

  void _viewStockAlerts(BuildContext context) {
    if (!SubscriptionService.to.isFeatureAvailable(PremiumFeature.inventoryManagement)) {
      SubscriptionService.to.showFeatureLockedDialog(PremiumFeature.inventoryManagement);
      return;
    }
    
    Get.toNamed('/stock-alerts');
  }

  void _openSearch(BuildContext context) {
    Get.toNamed('/search');
  }

  void _backupData(BuildContext context) {
    if (!SubscriptionService.to.isFeatureAvailable(PremiumFeature.cloudSync)) {
      SubscriptionService.to.showFeatureLockedDialog(PremiumFeature.cloudSync);
      return;
    }

    // Implement backup functionality
    Get.snackbar(
      'Backup',
      'Data backup started...',
      backgroundColor: AppColors.info,
      colorText: AppColors.white,
    );
  }

  void _syncData(BuildContext context) {
    // Sync data with Appwrite
    Get.snackbar(
      'Sync',
      'Syncing data with cloud...',
      backgroundColor: AppColors.success,
      colorText: AppColors.white,
    );
  }
}

class _QuickActionButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final Color color;
  final VoidCallback onTap;
  final bool isPremium;

  const _QuickActionButton({
    required this.icon,
    required this.label,
    required this.color,
    required this.onTap,
    this.isPremium = false,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: color.withOpacity(0.2),
            ),
          ),
          child: Column(
            children: [
              Stack(
                children: [
                  Icon(
                    icon,
                    color: color,
                    size: 28,
                  ),
                  if (isPremium && !SubscriptionService.to.isPremium)
                    Positioned(
                      top: -2,
                      right: -2,
                      child: Container(
                        padding: const EdgeInsets.all(2),
                        decoration: const BoxDecoration(
                          color: AppColors.accent,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.star,
                          color: AppColors.white,
                          size: 12,
                        ),
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                label,
                style: Theme.of(context).textTheme.labelMedium?.copyWith(
                  color: color,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
