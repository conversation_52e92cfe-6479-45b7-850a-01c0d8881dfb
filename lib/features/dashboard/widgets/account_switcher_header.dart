import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/services/account_service.dart';
import '../../../core/services/auth_service.dart';
import '../../../core/services/subscription_service.dart';
import '../../../core/models/account_type.dart';
import '../../../core/routes/app_routes.dart';

class AccountSwitcherHeader extends StatelessWidget {
  const AccountSwitcherHeader({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final accountService = AccountService.to;
      final subscriptionService = SubscriptionService.to;
      final currentAccount = accountService.currentAccount;

      if (currentAccount == null) {
        return const SizedBox.shrink();
      }

      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppColors.surface,
          boxShadow: [
            BoxShadow(
              color: AppColors.black.withOpacity(0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // Account Avatar
            CircleAvatar(
              radius: 24,
              backgroundColor: _getAccountTypeColor(currentAccount.accountType),
              backgroundImage: currentAccount.profileImageUrl != null
                  ? NetworkImage(currentAccount.profileImageUrl!)
                  : null,
              child: currentAccount.profileImageUrl == null
                  ? Icon(
                      _getAccountTypeIcon(currentAccount.accountType),
                      color: AppColors.white,
                      size: 24,
                    )
                  : null,
            ),
            
            const SizedBox(width: 12),
            
            // Account Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Flexible(
                        child: Text(
                          currentAccount.name,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: AppColors.textPrimary,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: _getAccountTypeColor(currentAccount.accountType).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: _getAccountTypeColor(currentAccount.accountType).withOpacity(0.3),
                          ),
                        ),
                        child: Text(
                          AccountType.fromString(currentAccount.accountType).displayName,
                          style: Theme.of(context).textTheme.labelSmall?.copyWith(
                            color: _getAccountTypeColor(currentAccount.accountType),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 2),
                  Row(
                    children: [
                      Flexible(
                        child: Text(
                          currentAccount.email,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppColors.textSecondary,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 1),
                        decoration: BoxDecoration(
                          color: subscriptionService.isPremium 
                              ? AppColors.accent.withOpacity(0.1)
                              : AppColors.grey.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          subscriptionService.premiumStatusText,
                          style: Theme.of(context).textTheme.labelSmall?.copyWith(
                            color: subscriptionService.isPremium 
                                ? AppColors.accent
                                : AppColors.textSecondary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            // Account Switcher Button
            if (accountService.allAccounts.length > 1)
              IconButton(
                onPressed: () => _showAccountSwitcher(context),
                icon: const Icon(
                  Icons.swap_horiz_rounded,
                  color: AppColors.primary,
                ),
                tooltip: 'Switch Account',
              ),
            
            // Settings Button
            IconButton(
              onPressed: () => _showAccountMenu(context),
              icon: const Icon(
                Icons.more_vert_rounded,
                color: AppColors.textSecondary,
              ),
              tooltip: 'Account Settings',
            ),
          ],
        ),
      );
    });
  }

  Color _getAccountTypeColor(String accountType) {
    switch (accountType.toLowerCase()) {
      case 'business':
        return AppColors.secondary;
      case 'personal':
      default:
        return AppColors.primary;
    }
  }

  IconData _getAccountTypeIcon(String accountType) {
    switch (accountType.toLowerCase()) {
      case 'business':
        return Icons.business_rounded;
      case 'personal':
      default:
        return Icons.person_rounded;
    }
  }

  void _showAccountSwitcher(BuildContext context) {
    final accountService = AccountService.to;
    
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Switch Account',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            ...accountService.allAccounts.map((account) => ListTile(
              leading: CircleAvatar(
                backgroundColor: _getAccountTypeColor(account.accountType),
                backgroundImage: account.profileImageUrl != null
                    ? NetworkImage(account.profileImageUrl!)
                    : null,
                child: account.profileImageUrl == null
                    ? Icon(
                        _getAccountTypeIcon(account.accountType),
                        color: AppColors.white,
                        size: 20,
                      )
                    : null,
              ),
              title: Text(account.name),
              subtitle: Text(account.email),
              trailing: account.isActive
                  ? const Icon(Icons.check_circle, color: AppColors.success)
                  : null,
              onTap: account.isActive
                  ? null
                  : () async {
                      Navigator.pop(context);
                      await accountService.switchAccount(account.id);
                    },
            )),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () {
                  Navigator.pop(context);
                  // Navigate to add account screen
                  Get.toNamed('/add-account');
                },
                icon: const Icon(Icons.add_rounded),
                label: const Text('Add New Account'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showAccountMenu(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.person_outline_rounded),
              title: const Text('Account Settings'),
              onTap: () {
                Navigator.pop(context);
                Get.toNamed('/account-settings');
              },
            ),
            ListTile(
              leading: const Icon(Icons.star_outline_rounded),
              title: const Text('Upgrade to Premium'),
              onTap: () {
                Navigator.pop(context);
                SubscriptionService.to.showUpgradeDialog();
              },
            ),
            ListTile(
              leading: const Icon(Icons.sync_rounded),
              title: const Text('Sync Data'),
              onTap: () {
                Navigator.pop(context);
                // Implement sync functionality
              },
            ),
            ListTile(
              leading: const Icon(Icons.download_rounded),
              title: const Text('Export Data'),
              onTap: () {
                Navigator.pop(context);
                if (SubscriptionService.to.isFeatureAvailable(PremiumFeature.dataExport)) {
                  // Implement export functionality
                } else {
                  SubscriptionService.to.showFeatureLockedDialog(PremiumFeature.dataExport);
                }
              },
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.logout_rounded, color: AppColors.error),
              title: const Text('Sign Out', style: TextStyle(color: AppColors.error)),
              onTap: () {
                Navigator.pop(context);
                _showSignOutDialog(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showSignOutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sign Out'),
        content: const Text('Are you sure you want to sign out? Your local data will remain on this device.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              // Implement sign out logic
              await AuthService.to.signOut();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
            ),
            child: const Text('Sign Out'),
          ),
        ],
      ),
    );
  }
}
