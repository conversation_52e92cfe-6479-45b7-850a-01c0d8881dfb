import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/theme/design_system.dart';
import '../../../core/models/transaction.dart';
import '../../transactions/widgets/add_transaction_modal.dart';
import '../../../core/theme/typography.dart';

class ExpandableFab extends StatefulWidget {
  const ExpandableFab({super.key});

  @override
  State<ExpandableFab> createState() => _ExpandableFabState();
}

class _ExpandableFabState extends State<ExpandableFab>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _rotationController;
  late Animation<double> _expandAnimation;
  late Animation<double> _rotationAnimation;
  late Animation<double> _fadeAnimation;

  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _expandAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOutBack,
    );

    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.75, // 3/4 rotation (270 degrees)
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.easeInOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.8, curve: Curves.easeOut),
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    _rotationController.dispose();
    super.dispose();
  }

  void _toggleExpansion() {
    setState(() {
      _isExpanded = !_isExpanded;
    });

    if (_isExpanded) {
      _animationController.forward();
      _rotationController.forward();
    } else {
      _animationController.reverse();
      _rotationController.reverse();
    }
  }

  void _onOptionTap(String type) {
    _toggleExpansion();

    // Show the new modal instead of navigating
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AddTransactionModal(
        initialType: type == 'income'
            ? TransactionType.income
            : TransactionType.expense,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 200,
      height: 200,
      child: Stack(
        alignment: Alignment.bottomRight,
        children: [
          // Background overlay when expanded
          AnimatedBuilder(
            animation: _fadeAnimation,
            builder: (context, child) {
              return _isExpanded
                  ? GestureDetector(
                      onTap: _toggleExpansion,
                      child: Container(
                        width: double.infinity,
                        height: double.infinity,
                        color: Colors.black.withOpacity(0.1 * _fadeAnimation.value),
                      ),
                    )
                  : const SizedBox.shrink();
            },
          ),

          // Income option
          AnimatedBuilder(
            animation: _expandAnimation,
            builder: (context, child) {
              return Transform.translate(
                offset: Offset(
                  -80 * _expandAnimation.value,
                  -140 * _expandAnimation.value,
                ),
                child: Transform.scale(
                  scale: _expandAnimation.value,
                  child: Opacity(
                    opacity: _fadeAnimation.value,
                    child: _buildOptionButton(
                      icon: Icons.trending_up_rounded,
                      label: 'Income',
                      color: DesignSystem.primaryTeal,
                      onTap: () => _onOptionTap('income'),
                    ),
                  ),
                ),
              );
            },
          ),

          // Expense option
          AnimatedBuilder(
            animation: _expandAnimation,
            builder: (context, child) {
              return Transform.translate(
                offset: Offset(
                  -140 * _expandAnimation.value,
                  -80 * _expandAnimation.value,
                ),
                child: Transform.scale(
                  scale: _expandAnimation.value,
                  child: Opacity(
                    opacity: _fadeAnimation.value,
                    child: _buildOptionButton(
                      icon: Icons.trending_down_rounded,
                      label: 'Expense',
                      color: DesignSystem.secondaryCoral,
                      onTap: () => _onOptionTap('expense'),
                    ),
                  ),
                ),
              );
            },
          ),

          // Main FAB
          AnimatedBuilder(
            animation: _rotationAnimation,
            builder: (context, child) {
              return Transform.rotate(
                angle: _rotationAnimation.value * 2 * 3.14159,
                child: Container(
                  width: 64,
                  height: 64,
                  decoration: BoxDecoration(
                    gradient: _isExpanded
                        ? LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              DesignSystem.secondaryCoral,
                              DesignSystem.secondaryCoral.withOpacity(0.8),
                            ],
                          )
                        : DesignSystem.primaryGradient,
                    borderRadius: BorderRadius.circular(32),
                    boxShadow: [
                      BoxShadow(
                        color: (_isExpanded 
                            ? DesignSystem.secondaryCoral 
                            : DesignSystem.primaryTeal).withOpacity(0.3),
                        blurRadius: 12,
                        offset: const Offset(0, 6),
                      ),
                    ],
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      borderRadius: BorderRadius.circular(32),
                      onTap: _toggleExpansion,
                      child: Icon(
                        _isExpanded ? Icons.close_rounded : Icons.add_rounded,
                        color: DesignSystem.white,
                        size: 28,
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildOptionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 56,
          height: 56,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(28),
            boxShadow: [
              BoxShadow(
                color: color.withOpacity(0.3),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(28),
              onTap: onTap,
              child: Icon(
                icon,
                color: DesignSystem.white,
                size: 24,
              ),
            ),
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: DesignSystem.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: DesignSystem.shadowMedium,
          ),
          child: Text(
            label,
            style: AppTypography.labelMedium.copyWith(
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }
}
