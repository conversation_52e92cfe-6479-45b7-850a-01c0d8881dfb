import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../core/theme/design_system.dart';
import '../../../core/theme/typography.dart';
import '../../../core/services/personal_transaction_service.dart';
import '../../../core/models/transaction.dart';

class SpendingHabitsDonutChart extends StatefulWidget {
  const SpendingHabitsDonutChart({super.key});

  @override
  State<SpendingHabitsDonutChart> createState() => _SpendingHabitsDonutChartState();
}

class _SpendingHabitsDonutChartState extends State<SpendingHabitsDonutChart>
    with TickerProviderStateMixin {
  int _touchedIndex = -1;
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOutCubic,
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Container(
        padding: EdgeInsets.all(DesignSystem.spacingLarge),
        decoration: BoxDecoration(
          color: DesignSystem.white,
          borderRadius: BorderRadius.circular(DesignSystem.radiusLarge),
          boxShadow: DesignSystem.shadowMedium,
          border: Border.all(
            color: DesignSystem.textSecondary.withOpacity(0.1),
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            SizedBox(height: DesignSystem.spacingLarge),
            _buildChart(),
            SizedBox(height: DesignSystem.spacingLarge),
            _buildLegend(),
          ],
        ),
      );
    });
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Spending Habits',
              style: AppTypography.titleLarge.copyWith(
                fontWeight: FontWeight.bold,
                color: DesignSystem.textPrimary,
              ),
            ),
            SizedBox(height: DesignSystem.spacingXSmall),
            Text(
              'Category Distribution',
              style: AppTypography.bodyMedium.copyWith(
                color: DesignSystem.textSecondary,
              ),
            ),
          ],
        ),
        Container(
          padding: EdgeInsets.symmetric(
            horizontal: DesignSystem.spacingMedium,
            vertical: DesignSystem.spacingSmall,
          ),
          decoration: BoxDecoration(
            color: DesignSystem.backgroundPrimary,
            borderRadius: BorderRadius.circular(DesignSystem.radiusLarge),
            border: Border.all(
              color: DesignSystem.textSecondary.withOpacity(0.1),
              width: 1,
            ),
          ),
          child: Text(
            'This Month',
            style: AppTypography.labelMedium.copyWith(
              color: DesignSystem.textSecondary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildChart() {
    final chartData = _getChartData();
    final totalExpenses = chartData.fold<double>(0, (sum, item) => sum + item.value);

    return SizedBox(
      height: 220,
      child: Stack(
        children: [
          AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return PieChart(
                PieChartData(
                  pieTouchData: PieTouchData(
                    touchCallback: (FlTouchEvent event, pieTouchResponse) {
                      setState(() {
                        if (!event.isInterestedForInteractions ||
                            pieTouchResponse == null ||
                            pieTouchResponse.touchedSection == null) {
                          _touchedIndex = -1;
                          return;
                        }
                        _touchedIndex = pieTouchResponse.touchedSection!.touchedSectionIndex;
                      });
                    },
                  ),
                  borderData: FlBorderData(show: false),
                  sectionsSpace: 2,
                  centerSpaceRadius: 60,
                  sections: _buildPieChartSections(chartData, totalExpenses),
                ),
              );
            },
          ),
          // Center content
          Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Total',
                  style: AppTypography.labelMedium.copyWith(
                    color: DesignSystem.textSecondary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: DesignSystem.spacingXSmall),
                Text(
                  '\$${_formatAmount(totalExpenses)}',
                  style: AppTypography.titleLarge.copyWith(
                    fontWeight: FontWeight.bold,
                    color: DesignSystem.textPrimary,
                  ),
                ),
                if (_touchedIndex >= 0 && _touchedIndex < chartData.length) ...[
                  SizedBox(height: DesignSystem.spacingXSmall),
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: DesignSystem.spacingSmall,
                      vertical: DesignSystem.spacingXSmall,
                    ),
                    decoration: BoxDecoration(
                      color: chartData[_touchedIndex].color.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(DesignSystem.radiusSmall),
                    ),
                    child: Text(
                      chartData[_touchedIndex].category.displayName,
                      style: AppTypography.labelSmall.copyWith(
                        color: chartData[_touchedIndex].color,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  List<PieChartSectionData> _buildPieChartSections(
    List<CategoryData> chartData,
    double totalExpenses,
  ) {
    return chartData.asMap().entries.map((entry) {
      final index = entry.key;
      final data = entry.value;
      final isTouched = index == _touchedIndex;
      final percentage = (data.value / totalExpenses) * 100;
      
      return PieChartSectionData(
        color: data.color,
        value: data.value * _animation.value,
        title: '${percentage.toStringAsFixed(1)}%',
        radius: isTouched ? 45 : 40,
        titleStyle: AppTypography.labelSmall.copyWith(
          fontWeight: FontWeight.bold,
          color: DesignSystem.white,
        ),
        titlePositionPercentageOffset: 0.6,
        borderSide: BorderSide(
          color: DesignSystem.white,
          width: 2,
        ),
      );
    }).toList();
  }

  Widget _buildLegend() {
    final chartData = _getChartData();
    final totalExpenses = chartData.fold<double>(0, (sum, item) => sum + item.value);

    return Column(
      children: chartData.asMap().entries.map((entry) {
        final index = entry.key;
        final data = entry.value;
        final percentage = (data.value / totalExpenses) * 100;
        final isSelected = index == _touchedIndex;

        return GestureDetector(
          onTap: () {
            setState(() {
              _touchedIndex = index == _touchedIndex ? -1 : index;
            });
          },
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            margin: EdgeInsets.only(bottom: DesignSystem.spacingSmall),
            padding: EdgeInsets.all(DesignSystem.spacingMedium),
            decoration: BoxDecoration(
              color: isSelected 
                  ? data.color.withOpacity(0.1) 
                  : DesignSystem.backgroundPrimary,
              borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
              border: Border.all(
                color: isSelected 
                    ? data.color.withOpacity(0.3) 
                    : DesignSystem.textSecondary.withOpacity(0.1),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Container(
                  width: 16,
                  height: 16,
                  decoration: BoxDecoration(
                    color: data.color,
                    borderRadius: BorderRadius.circular(DesignSystem.radiusSmall),
                  ),
                  child: Icon(
                    data.category.icon,
                    size: 10,
                    color: DesignSystem.white,
                  ),
                ),
                SizedBox(width: DesignSystem.spacingMedium),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        data.category.displayName,
                        style: AppTypography.bodyMedium.copyWith(
                          fontWeight: FontWeight.w600,
                          color: DesignSystem.textPrimary,
                        ),
                      ),
                      Text(
                        '${percentage.toStringAsFixed(1)}% of spending',
                        style: AppTypography.labelSmall.copyWith(
                          color: DesignSystem.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                Text(
                  '\$${_formatAmount(data.value)}',
                  style: AppTypography.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: data.color,
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  List<CategoryData> _getChartData() {
    final transactionService = PersonalTransactionService.to;
    final transactions = transactionService.transactions;

    // Get current month transactions
    final now = DateTime.now();
    final monthStart = DateTime(now.year, now.month, 1);
    final monthEnd = DateTime(now.year, now.month + 1, 1);

    final monthTransactions = transactions.where((t) =>
      t.type == 'expense' &&
      t.date.isAfter(monthStart) &&
      t.date.isBefore(monthEnd)
    ).toList();

    // Group by category and calculate totals
    final Map<String, double> categoryTotals = {};
    for (final transaction in monthTransactions) {
      categoryTotals[transaction.category] =
          (categoryTotals[transaction.category] ?? 0) + transaction.amount;
    }

    // Convert to CategoryData list and sort by amount
    final categoryDataList = categoryTotals.entries.map((entry) {
      final categoryEnum = TransactionCategory.values.firstWhere(
        (cat) => cat.name == entry.key,
        orElse: () => TransactionCategory.other_expense,
      );

      return CategoryData(
        category: categoryEnum,
        value: entry.value,
        color: _getCategoryColor(categoryEnum),
      );
    }).toList();

    // Sort by value (highest first) and take top 6
    categoryDataList.sort((a, b) => b.value.compareTo(a.value));

    if (categoryDataList.length > 6) {
      final topCategories = categoryDataList.take(5).toList();
      final othersTotal = categoryDataList.skip(5).fold<double>(
        0, (sum, item) => sum + item.value
      );

      if (othersTotal > 0) {
        topCategories.add(CategoryData(
          category: TransactionCategory.other_expense,
          value: othersTotal,
          color: DesignSystem.textSecondary,
        ));
      }

      return topCategories;
    }

    return categoryDataList;
  }

  Color _getCategoryColor(TransactionCategory category) {
    switch (category) {
      case TransactionCategory.food:
        return const Color(0xFFFF6B6B); // Coral Red
      case TransactionCategory.transportation:
        return const Color(0xFF4ECDC4); // Teal
      case TransactionCategory.housing:
        return const Color(0xFF45B7D1); // Sky Blue
      case TransactionCategory.utilities:
        return const Color(0xFFFFA726); // Orange
      case TransactionCategory.healthcare:
        return const Color(0xFFEF5350); // Red
      case TransactionCategory.entertainment:
        return const Color(0xFFAB47BC); // Purple
      case TransactionCategory.shopping:
        return const Color(0xFFEC407A); // Pink
      case TransactionCategory.education:
        return const Color(0xFF5C6BC0); // Indigo
      case TransactionCategory.insurance:
        return const Color(0xFF26A69A); // Teal Green
      case TransactionCategory.debt:
        return const Color(0xFFFF7043); // Deep Orange
      case TransactionCategory.savings:
        return const Color(0xFF66BB6A); // Green
      case TransactionCategory.charity:
        return const Color(0xFF42A5F5); // Light Blue
      case TransactionCategory.travel:
        return const Color(0xFF26C6DA); // Cyan
      case TransactionCategory.business_expense:
        return const Color(0xFF78909C); // Blue Grey
      case TransactionCategory.other_expense:
        return DesignSystem.textSecondary;
      default:
        return DesignSystem.primaryTeal;
    }
  }

  String _formatAmount(double amount) {
    if (amount >= 1000000) {
      return '${(amount / 1000000).toStringAsFixed(1)}M';
    } else if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(1)}K';
    } else {
      return amount.toStringAsFixed(0);
    }
  }
}

class CategoryData {
  final TransactionCategory category;
  final double value;
  final Color color;

  CategoryData({
    required this.category,
    required this.value,
    required this.color,
  });
}
