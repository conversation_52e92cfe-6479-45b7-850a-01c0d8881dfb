import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../core/theme/design_system.dart';
import '../../../core/theme/typography.dart';
import '../../../core/services/personal_transaction_service.dart';
import '../../../core/utils/chart_utils.dart';
import '../../../core/database/database.dart';

enum ChartPeriod { monthly, quarterly, yearly }

class InteractiveNetFlowChart extends StatefulWidget {
  const InteractiveNetFlowChart({super.key});

  @override
  State<InteractiveNetFlowChart> createState() => _InteractiveNetFlowChartState();
}

class _InteractiveNetFlowChartState extends State<InteractiveNetFlowChart>
    with TickerProviderStateMixin {
  ChartPeriod _selectedPeriod = ChartPeriod.monthly;
  int _touchedIndex = -1;
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOutCubic,
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Container(
        padding: EdgeInsets.all(DesignSystem.spacingLarge),
        decoration: BoxDecoration(
          color: DesignSystem.white,
          borderRadius: BorderRadius.circular(DesignSystem.radiusLarge),
          boxShadow: DesignSystem.shadowMedium,
          border: Border.all(
            color: DesignSystem.textSecondary.withOpacity(0.1),
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            SizedBox(height: DesignSystem.spacingLarge),
            _buildPeriodToggle(),
            SizedBox(height: DesignSystem.spacingLarge),
            _buildChart(),
            SizedBox(height: DesignSystem.spacingMedium),
            _buildLegend(),
          ],
        ),
      );
    });
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Net Flow Analysis',
              style: AppTypography.titleLarge.copyWith(
                fontWeight: FontWeight.bold,
                color: DesignSystem.textPrimary,
              ),
            ),
            SizedBox(height: DesignSystem.spacingXSmall),
            Text(
              'Income vs Expenses Trend',
              style: AppTypography.bodyMedium.copyWith(
                color: DesignSystem.textSecondary,
              ),
            ),
          ],
        ),
        Container(
          padding: EdgeInsets.symmetric(
            horizontal: DesignSystem.spacingMedium,
            vertical: DesignSystem.spacingSmall,
          ),
          decoration: BoxDecoration(
            gradient: DesignSystem.primaryGradient,
            borderRadius: BorderRadius.circular(DesignSystem.radiusLarge),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.trending_up_rounded,
                size: 16,
                color: DesignSystem.white,
              ),
              SizedBox(width: DesignSystem.spacingXSmall),
              Text(
                _getNetFlowText(),
                style: AppTypography.labelMedium.copyWith(
                  color: DesignSystem.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPeriodToggle() {
    return Container(
      padding: EdgeInsets.all(DesignSystem.spacingXSmall),
      decoration: BoxDecoration(
        color: DesignSystem.backgroundPrimary,
        borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
        border: Border.all(
          color: DesignSystem.textSecondary.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: ChartPeriod.values.map((period) {
          final isSelected = _selectedPeriod == period;
          return GestureDetector(
            onTap: () {
              setState(() {
                _selectedPeriod = period;
              });
              _animationController.reset();
              _animationController.forward();
            },
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              padding: EdgeInsets.symmetric(
                horizontal: DesignSystem.spacingMedium,
                vertical: DesignSystem.spacingSmall,
              ),
              decoration: BoxDecoration(
                color: isSelected ? DesignSystem.white : Colors.transparent,
                borderRadius: BorderRadius.circular(DesignSystem.radiusSmall),
                boxShadow: isSelected ? DesignSystem.shadowLow : null,
              ),
              child: Text(
                _getPeriodLabel(period),
                style: AppTypography.labelMedium.copyWith(
                  color: isSelected 
                      ? DesignSystem.primaryTeal 
                      : DesignSystem.textSecondary,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildChart() {
    final chartData = _getChartData();
    
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return SizedBox(
          height: 200,
          child: LineChart(
            LineChartData(
              gridData: FlGridData(
                show: true,
                drawVerticalLine: false,
                horizontalInterval: _getGridInterval(),
                getDrawingHorizontalLine: (value) {
                  return FlLine(
                    color: DesignSystem.textSecondary.withOpacity(0.1),
                    strokeWidth: 1,
                  );
                },
              ),
              titlesData: FlTitlesData(
                show: true,
                rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                bottomTitles: AxisTitles(
                  sideTitles: SideTitles(
                    showTitles: true,
                    reservedSize: 30,
                    interval: 1,
                    getTitlesWidget: (value, meta) {
                      return _buildBottomTitle(value.toInt());
                    },
                  ),
                ),
                leftTitles: AxisTitles(
                  sideTitles: SideTitles(
                    showTitles: true,
                    interval: _getGridInterval(),
                    reservedSize: 50,
                    getTitlesWidget: (value, meta) {
                      return _buildLeftTitle(value);
                    },
                  ),
                ),
              ),
              borderData: FlBorderData(show: false),
              minX: 0,
              maxX: (chartData['income']?.length ?? 1).toDouble() - 1,
              minY: 0,
              maxY: _getMaxY(),
              lineBarsData: [
                // Income line
                LineChartBarData(
                  spots: _animateSpots(chartData['income'] ?? []),
                  isCurved: true,
                  gradient: LinearGradient(
                    colors: [
                      DesignSystem.success,
                      DesignSystem.success.withOpacity(0.8),
                    ],
                  ),
                  barWidth: 3,
                  isStrokeCapRound: true,
                  dotData: FlDotData(
                    show: true,
                    getDotPainter: (spot, percent, barData, index) {
                      return FlDotCirclePainter(
                        radius: _touchedIndex == index ? 6 : 4,
                        color: DesignSystem.success,
                        strokeWidth: 2,
                        strokeColor: DesignSystem.white,
                      );
                    },
                  ),
                  belowBarData: BarAreaData(
                    show: true,
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        DesignSystem.success.withOpacity(0.3),
                        DesignSystem.success.withOpacity(0.1),
                      ],
                    ),
                  ),
                ),
                // Expense line
                LineChartBarData(
                  spots: _animateSpots(chartData['expense'] ?? []),
                  isCurved: true,
                  gradient: LinearGradient(
                    colors: [
                      DesignSystem.secondaryCoral,
                      DesignSystem.secondaryCoral.withOpacity(0.8),
                    ],
                  ),
                  barWidth: 3,
                  isStrokeCapRound: true,
                  dotData: FlDotData(
                    show: true,
                    getDotPainter: (spot, percent, barData, index) {
                      return FlDotCirclePainter(
                        radius: _touchedIndex == index ? 6 : 4,
                        color: DesignSystem.secondaryCoral,
                        strokeWidth: 2,
                        strokeColor: DesignSystem.white,
                      );
                    },
                  ),
                  belowBarData: BarAreaData(
                    show: true,
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        DesignSystem.secondaryCoral.withOpacity(0.3),
                        DesignSystem.secondaryCoral.withOpacity(0.1),
                      ],
                    ),
                  ),
                ),
              ],
              lineTouchData: LineTouchData(
                enabled: true,
                touchCallback: (FlTouchEvent event, LineTouchResponse? touchResponse) {
                  setState(() {
                    if (touchResponse != null && touchResponse.lineBarSpots != null) {
                      _touchedIndex = touchResponse.lineBarSpots!.first.spotIndex;
                    } else {
                      _touchedIndex = -1;
                    }
                  });
                },
                touchTooltipData: LineTouchTooltipData(
                  getTooltipColor: (touchedSpot) => DesignSystem.textPrimary.withOpacity(0.9),
                  tooltipRoundedRadius: DesignSystem.radiusSmall,
                  tooltipPadding: EdgeInsets.all(DesignSystem.spacingSmall),
                  getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
                    return touchedBarSpots.map((barSpot) {
                      final isIncome = barSpot.barIndex == 0;
                      return LineTooltipItem(
                        '${isIncome ? 'Income' : 'Expense'}\n\$${_formatAmount(barSpot.y)}',
                        AppTypography.labelSmall.copyWith(
                          color: DesignSystem.white,
                          fontWeight: FontWeight.w600,
                        ),
                      );
                    }).toList();
                  },
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildLegend() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildLegendItem(
          color: DesignSystem.success,
          label: 'Income',
          icon: Icons.trending_up_rounded,
        ),
        SizedBox(width: DesignSystem.spacingLarge),
        _buildLegendItem(
          color: DesignSystem.secondaryCoral,
          label: 'Expenses',
          icon: Icons.trending_down_rounded,
        ),
      ],
    );
  }

  Widget _buildLegendItem({
    required Color color,
    required String label,
    required IconData icon,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: EdgeInsets.all(DesignSystem.spacingXSmall),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(DesignSystem.radiusSmall),
          ),
          child: Icon(
            icon,
            size: 16,
            color: color,
          ),
        ),
        SizedBox(width: DesignSystem.spacingSmall),
        Text(
          label,
          style: AppTypography.bodyMedium.copyWith(
            color: DesignSystem.textSecondary,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildBottomTitle(int value) {
    final labels = _getBottomLabels();
    if (value >= 0 && value < labels.length) {
      return Padding(
        padding: EdgeInsets.only(top: DesignSystem.spacingSmall),
        child: Text(
          labels[value],
          style: AppTypography.labelSmall.copyWith(
            color: DesignSystem.textSecondary,
            fontWeight: FontWeight.w500,
          ),
        ),
      );
    }
    return const SizedBox.shrink();
  }

  Widget _buildLeftTitle(double value) {
    return Text(
      _formatAmount(value),
      style: AppTypography.labelSmall.copyWith(
        color: DesignSystem.textSecondary,
        fontWeight: FontWeight.w500,
      ),
    );
  }

  List<FlSpot> _animateSpots(List<FlSpot> spots) {
    return spots.map((spot) {
      return FlSpot(
        spot.x,
        spot.y * _animation.value,
      );
    }).toList();
  }

  Map<String, List<FlSpot>> _getChartData() {
    final transactionService = PersonalTransactionService.to;
    final transactions = transactionService.transactions;

    switch (_selectedPeriod) {
      case ChartPeriod.monthly:
        return _getMonthlyData(transactions);
      case ChartPeriod.quarterly:
        return _getQuarterlyData(transactions);
      case ChartPeriod.yearly:
        return _getYearlyData(transactions);
    }
  }

  Map<String, List<FlSpot>> _getMonthlyData(List<PersonalTransaction> transactions) {
    final now = DateTime.now();
    final incomeSpots = <FlSpot>[];
    final expenseSpots = <FlSpot>[];

    for (int i = 0; i < 7; i++) {
      final date = now.subtract(Duration(days: 6 - i));
      final dayStart = DateTime(date.year, date.month, date.day);
      final dayEnd = dayStart.add(const Duration(days: 1));

      final dayTransactions = transactions.where((t) =>
        t.date.isAfter(dayStart) && t.date.isBefore(dayEnd)
      ).toList();

      final income = dayTransactions
          .where((t) => t.type == 'income')
          .fold(0.0, (sum, t) => sum + t.amount);

      final expense = dayTransactions
          .where((t) => t.type == 'expense')
          .fold(0.0, (sum, t) => sum + t.amount);

      incomeSpots.add(FlSpot(i.toDouble(), income));
      expenseSpots.add(FlSpot(i.toDouble(), expense));
    }

    return {
      'income': incomeSpots,
      'expense': expenseSpots,
    };
  }

  Map<String, List<FlSpot>> _getQuarterlyData(List<PersonalTransaction> transactions) {
    final now = DateTime.now();
    final incomeSpots = <FlSpot>[];
    final expenseSpots = <FlSpot>[];

    for (int i = 0; i < 12; i++) {
      final month = DateTime(now.year, now.month - (11 - i), 1);
      final monthStart = DateTime(month.year, month.month, 1);
      final monthEnd = DateTime(month.year, month.month + 1, 1);

      final monthTransactions = transactions.where((t) =>
        t.date.isAfter(monthStart) && t.date.isBefore(monthEnd)
      ).toList();

      final income = monthTransactions
          .where((t) => t.type == 'income')
          .fold(0.0, (sum, t) => sum + t.amount);

      final expense = monthTransactions
          .where((t) => t.type == 'expense')
          .fold(0.0, (sum, t) => sum + t.amount);

      incomeSpots.add(FlSpot(i.toDouble(), income));
      expenseSpots.add(FlSpot(i.toDouble(), expense));
    }

    return {
      'income': incomeSpots,
      'expense': expenseSpots,
    };
  }

  Map<String, List<FlSpot>> _getYearlyData(List<PersonalTransaction> transactions) {
    final now = DateTime.now();
    final incomeSpots = <FlSpot>[];
    final expenseSpots = <FlSpot>[];

    for (int i = 0; i < 5; i++) {
      final year = now.year - (4 - i);
      final yearStart = DateTime(year, 1, 1);
      final yearEnd = DateTime(year + 1, 1, 1);

      final yearTransactions = transactions.where((t) =>
        t.date.isAfter(yearStart) && t.date.isBefore(yearEnd)
      ).toList();

      final income = yearTransactions
          .where((t) => t.type == 'income')
          .fold(0.0, (sum, t) => sum + t.amount);

      final expense = yearTransactions
          .where((t) => t.type == 'expense')
          .fold(0.0, (sum, t) => sum + t.amount);

      incomeSpots.add(FlSpot(i.toDouble(), income));
      expenseSpots.add(FlSpot(i.toDouble(), expense));
    }

    return {
      'income': incomeSpots,
      'expense': expenseSpots,
    };
  }

  List<String> _getBottomLabels() {
    switch (_selectedPeriod) {
      case ChartPeriod.monthly:
        final now = DateTime.now();
        return List.generate(7, (index) {
          final date = now.subtract(Duration(days: 6 - index));
          return '${date.day}/${date.month}';
        });
      case ChartPeriod.quarterly:
        final now = DateTime.now();
        return List.generate(12, (index) {
          final month = DateTime(now.year, now.month - (11 - index), 1);
          const monthNames = ['', 'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                             'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
          return monthNames[month.month];
        });
      case ChartPeriod.yearly:
        final now = DateTime.now();
        return List.generate(5, (index) {
          final year = now.year - (4 - index);
          return year.toString();
        });
    }
  }

  String _getPeriodLabel(ChartPeriod period) {
    switch (period) {
      case ChartPeriod.monthly:
        return 'Weekly';
      case ChartPeriod.quarterly:
        return 'Monthly';
      case ChartPeriod.yearly:
        return 'Yearly';
    }
  }

  String _getNetFlowText() {
    final transactionService = PersonalTransactionService.to;
    final netFlow = transactionService.totalIncome - transactionService.totalExpenses;
    final isPositive = netFlow >= 0;
    return '${isPositive ? '+' : ''}\$${_formatAmount(netFlow.abs())}';
  }

  double _getMaxY() {
    final chartData = _getChartData();
    final allSpots = [
      ...(chartData['income'] ?? []),
      ...(chartData['expense'] ?? []),
    ];

    if (allSpots.isEmpty) return 1000;

    final maxValue = allSpots.map((spot) => spot.y).reduce((a, b) => a > b ? a : b);
    return ChartUtils.calculateOptimalMaxY(maxValue);
  }

  double _getGridInterval() {
    final chartData = _getChartData();
    final allSpots = [
      ...(chartData['income'] ?? []),
      ...(chartData['expense'] ?? []),
    ];

    if (allSpots.isEmpty) return 100;

    final maxValue = allSpots.map((spot) => spot.y).reduce((a, b) => a > b ? a : b);
    return ChartUtils.calculateOptimalInterval(maxValue);
  }

  String _formatAmount(double amount) {
    return ChartUtils.formatChartAmount(amount);
  }
}
