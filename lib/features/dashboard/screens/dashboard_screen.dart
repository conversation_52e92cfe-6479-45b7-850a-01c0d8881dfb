import 'package:flutter/material.dart';
import 'package:animate_do/animate_do.dart';
import 'package:get/get.dart';
import '../../../core/theme/design_system.dart';
import '../../../core/theme/typography.dart';
import '../../../core/constants/app_strings.dart';
import '../../../core/services/account_service.dart';
import '../../../core/services/personal_transaction_service.dart';
import '../../../core/services/subscription_service.dart';
import '../../../core/models/transaction.dart';
import '../../../core/models/account_type.dart';
import '../../common/widgets/common_app_bar.dart';
import '../../common/widgets/account_drawer.dart';
import '../widgets/financial_summary_card.dart';
import '../widgets/recent_transactions_card.dart';
import '../widgets/quick_stats_card.dart';
import '../widgets/interactive_net_flow_chart.dart';
import '../widgets/spending_habits_donut_chart.dart';
import '../widgets/budget_progress_mini_cards.dart';
import '../widgets/spending_tracker_card.dart';
import '../widgets/expandable_fab.dart';
import '../widgets/quick_action_buttons.dart';
import '../widgets/business_growth_projections_card.dart';
import '../../budget/widgets/budget_overview_card.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  final ScrollController _scrollController = ScrollController();
  bool _showLoadingTimeout = false;

  @override
  void initState() {
    super.initState();
    // Show timeout message after 10 seconds
    Future.delayed(const Duration(seconds: 10), () {
      if (mounted) {
        setState(() {
          _showLoadingTimeout = true;
        });
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DesignSystem.backgroundPrimary,
      appBar: const CommonAppBar(
        title: 'Dashboard',
        showAccountInfo: true,
      ),
      endDrawer: const AccountDrawer(),
      floatingActionButton: const Hero(
        tag: "dashboard_expandable_fab",
        child: ExpandableFab(),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
      body: Obx(() {
        // Check if services are loading
        final transactionService = PersonalTransactionService.to;
        final isLoading = transactionService.isLoading;

        if (isLoading) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const CircularProgressIndicator(
                  color: DesignSystem.primaryTeal,
                ),
                const SizedBox(height: DesignSystem.spacingMedium),
                Text(
                  _showLoadingTimeout
                      ? 'Taking longer than expected...'
                      : 'Loading your financial data...',
                  style: AppTypography.bodyMedium.copyWith(
                    color: DesignSystem.textSecondary,
                  ),
                ),
                if (_showLoadingTimeout) ...[
                  const SizedBox(height: DesignSystem.spacingMedium),
                  ElevatedButton(
                    onPressed: () {
                      // Force refresh
                      setState(() {
                        _showLoadingTimeout = false;
                      });
                      _refreshData();
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: _refreshData,
          color: DesignSystem.primaryTeal,
          child: SingleChildScrollView(
            controller: _scrollController,
            physics: const AlwaysScrollableScrollPhysics(),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Total Balance Card - Moved to top
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: DesignSystem.spacingMedium),
                  child: FadeInDown(
                    duration: const Duration(milliseconds: 600),
                    child: const FinancialSummaryCard(),
                  ),
                ),

                const SizedBox(height: DesignSystem.spacingLarge),

                // Quick actions are now available via the floating action button

                // Quick Stats Row
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: DesignSystem.spacingMedium),
                  child: FadeInRight(
                    duration: const Duration(milliseconds: 600),
                    delay: const Duration(milliseconds: 400),
                    child: Obx(() {
                      final transactionService = PersonalTransactionService.to;
                      return Row(
                        children: [
                          Expanded(
                            child: QuickStatsCard(
                              title: 'This Month',
                              subtitle: 'Income',
                              amount: transactionService.totalIncome,
                              icon: Icons.trending_up_rounded,
                              color: DesignSystem.success,
                            ),
                          ),
                          const SizedBox(width: DesignSystem.spacingMedium),
                          Expanded(
                            child: QuickStatsCard(
                              title: 'This Month',
                              subtitle: 'Expenses',
                              amount: transactionService.totalExpenses,
                              icon: Icons.trending_down_rounded,
                              color: DesignSystem.secondaryCoral,
                            ),
                          ),
                        ],
                      );
                    }),
                  ),
                ),

                const SizedBox(height: DesignSystem.spacingLarge),

                // Interactive Net Flow Chart
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: DesignSystem.spacingMedium),
                  child: FadeInUp(
                    duration: const Duration(milliseconds: 600),
                    delay: const Duration(milliseconds: 600),
                    child: const InteractiveNetFlowChart(),
                  ),
                ),

                const SizedBox(height: DesignSystem.spacingLarge),

                // Spending Habits Donut Chart
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: DesignSystem.spacingMedium),
                  child: FadeInUp(
                    duration: const Duration(milliseconds: 600),
                    delay: const Duration(milliseconds: 800),
                    child: const SpendingHabitsDonutChart(),
                  ),
                ),

                const SizedBox(height: DesignSystem.spacingLarge),

                // Account-specific content
                Obx(() {
                  final accountService = AccountService.to;
                  final isBusinessAccount = accountService.currentAccountType == AccountType.business;

                  if (isBusinessAccount) {
                    // Business Account Content
                    return Column(
                      children: [
                        // Business Growth Projections Card
                        FadeInUp(
                          duration: const Duration(milliseconds: 600),
                          delay: const Duration(milliseconds: 900),
                          child: const BusinessGrowthProjectionsCard(),
                        ),
                        const SizedBox(height: DesignSystem.spacingLarge),
                      ],
                    );
                  } else {
                    // Personal Account Content
                    return Column(
                      children: [
                        // Budget Overview Card
                        FadeInUp(
                          duration: const Duration(milliseconds: 600),
                          delay: const Duration(milliseconds: 900),
                          child: const BudgetOverviewCard(),
                        ),
                        const SizedBox(height: DesignSystem.spacingLarge),

                        // Spending Tracker Card
                        FadeInUp(
                          duration: const Duration(milliseconds: 600),
                          delay: const Duration(milliseconds: 1000),
                          child: const SpendingTrackerCard(),
                        ),
                        const SizedBox(height: DesignSystem.spacingLarge),

                        // Budget Progress Mini-Cards
                        FadeInUp(
                          duration: const Duration(milliseconds: 600),
                          delay: const Duration(milliseconds: 1100),
                          child: const BudgetProgressMiniCards(),
                        ),
                        const SizedBox(height: DesignSystem.spacingLarge),
                      ],
                    );
                  }
                }),

                const SizedBox(height: DesignSystem.spacingLarge),

                // Recent Transactions
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: DesignSystem.spacingMedium),
                  child: FadeInUp(
                    duration: const Duration(milliseconds: 600),
                    delay: const Duration(milliseconds: 800),
                    child: const RecentTransactionsCard(),
                  ),
                ),

                const SizedBox(height: DesignSystem.spacingXXLarge * 2), // Space for bottom navigation
              ],
            ),
          ),
        );
      }),
    );
  }

  Future<void> _refreshData() async {
    try {
      await PersonalTransactionService.to.refreshData();
    } catch (e) {
      print('Error refreshing data: $e');
    }
  }
}
