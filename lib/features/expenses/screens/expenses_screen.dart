import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/services/theme_service.dart';
import '../../../core/services/account_service.dart';
import '../../../core/services/personal_transaction_service.dart';
import '../../../core/models/transaction.dart';
import '../../../core/database/database.dart';
import '../../common/widgets/common_app_bar.dart';
import '../../common/widgets/account_drawer.dart';
import '../../../shared/widgets/persistent_date_filter.dart';

class ExpensesScreen extends StatefulWidget {
  const ExpensesScreen({super.key});

  @override
  State<ExpensesScreen> createState() => _ExpensesScreenState();
}

class _ExpensesScreenState extends State<ExpensesScreen> {
  String _selectedPeriod = 'This Month';
  DateTime? _selectedDate;
  DateTimeRange? _selectedDateRange;
  final List<String> _periods = ['Today', 'This Week', 'This Month', 'This Year', 'All Time'];

  List<PersonalTransaction> _filteredTransactions = [];
  Map<String, double> _categoryTotals = {};
  double _totalExpenses = 0.0;
  double _averageExpense = 0.0;

  @override
  void initState() {
    super.initState();
    _loadExpenseData();
  }

  void _loadExpenseData() {
    final transactionService = PersonalTransactionService.to;
    final allTransactions = transactionService.transactions;

    // Filter expense transactions
    final expenseTransactions = allTransactions
        .where((t) => t.type == 'expense')
        .toList();

    // Filter by selected period
    _filteredTransactions = _filterTransactionsByPeriod(expenseTransactions);

    // Calculate totals and analytics
    _calculateAnalytics();

    setState(() {});
  }

  List<PersonalTransaction> _filterTransactionsByPeriod(List<PersonalTransaction> transactions) {
    final now = DateTime.now();
    DateTime startDate;

    switch (_selectedPeriod) {
      case 'Today':
        startDate = DateTime(now.year, now.month, now.day);
        break;
      case 'This Week':
        startDate = now.subtract(Duration(days: now.weekday - 1));
        startDate = DateTime(startDate.year, startDate.month, startDate.day);
        break;
      case 'This Month':
        startDate = DateTime(now.year, now.month, 1);
        break;
      case 'This Year':
        startDate = DateTime(now.year, 1, 1);
        break;
      case 'All Time':
        return transactions;
      default:
        startDate = DateTime(now.year, now.month, 1);
    }

    return transactions.where((t) => t.date.isAfter(startDate) || t.date.isAtSameMomentAs(startDate)).toList();
  }

  void _calculateAnalytics() {
    _totalExpenses = _filteredTransactions.fold(0.0, (sum, t) => sum + t.amount);

    // Calculate category totals
    _categoryTotals.clear();
    for (final transaction in _filteredTransactions) {
      final categoryName = _getCategoryDisplayName(transaction.category);
      _categoryTotals[categoryName] = (_categoryTotals[categoryName] ?? 0.0) + transaction.amount;
    }

    // Calculate average expense
    if (_filteredTransactions.isNotEmpty) {
      switch (_selectedPeriod) {
        case 'Today':
          _averageExpense = _totalExpenses;
          break;
        case 'This Week':
          _averageExpense = _totalExpenses / 7;
          break;
        case 'This Month':
          final daysInMonth = DateTime.now().day;
          _averageExpense = _totalExpenses / daysInMonth;
          break;
        case 'This Year':
          final dayOfYear = DateTime.now().difference(DateTime(DateTime.now().year, 1, 1)).inDays + 1;
          _averageExpense = _totalExpenses / dayOfYear;
          break;
        default:
          _averageExpense = _totalExpenses / (_filteredTransactions.length > 0 ? _filteredTransactions.length : 1);
      }
    } else {
      _averageExpense = 0.0;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final themeService = ThemeService.to;
      final accountService = AccountService.to;
      
      return Scaffold(
        backgroundColor: themeService.backgroundColor,
        appBar: CommonAppBar(
          title: 'Expenses',
          showAccountInfo: true,
          actions: [
            IconButton(
              onPressed: () {
                _showFilterOptions(context);
              },
              icon: const Icon(Icons.filter_list_rounded),
            ),
            IconButton(
              onPressed: () {
                _showSearchDialog(context);
              },
              icon: const Icon(Icons.search_rounded),
            ),
          ],
        ),
        endDrawer: const AccountDrawer(),
        body: RefreshIndicator(
          onRefresh: _refreshData,
          color: themeService.primaryColor,
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Persistent Date Filter
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: PersistentDateFilter(
                    selectedDate: _selectedDate,
                    selectedDateRange: _selectedDateRange,
                    onDateSelected: (date) {
                      setState(() {
                        _selectedDate = date;
                        _selectedDateRange = null;
                        _selectedPeriod = 'Custom';
                      });
                      _refreshData();
                    },
                    onDateRangeSelected: (range) {
                      setState(() {
                        _selectedDateRange = range;
                        _selectedDate = null;
                        _selectedPeriod = 'Custom';
                      });
                      _refreshData();
                    },
                    emptyStateMessage: _filteredTransactions.isEmpty
                        ? 'No expense records found for the selected date'
                        : null,
                  ),
                ),

                // Period Selector
                _buildPeriodSelector(themeService),

                const SizedBox(height: 16),
                
                // Expense Summary Cards
                _buildExpenseSummary(themeService),
                
                const SizedBox(height: 20),
                
                // Expense Categories
                _buildExpenseCategories(themeService),
                
                const SizedBox(height: 20),
                
                // Recent Expense Transactions
                _buildRecentExpenseTransactions(themeService),
                
                const SizedBox(height: 100), // Space for bottom nav
              ],
            ),
          ),
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: () {
            Get.toNamed('/add-transaction', arguments: {'isIncome': false});
          },
          backgroundColor: Colors.red,
          child: const Icon(Icons.remove_rounded, color: Colors.white),
        ),
      );
    });
  }

  Widget _buildPeriodSelector(ThemeService themeService) {
    return Container(
      height: 50,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _periods.length,
        itemBuilder: (context, index) {
          final period = _periods[index];
          final isSelected = period == _selectedPeriod;
          
          return Container(
            margin: const EdgeInsets.only(right: 12),
            child: FilterChip(
              label: Text(period),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _selectedPeriod = period;
                  _loadExpenseData();
                });
              },
              backgroundColor: themeService.surfaceColor,
              selectedColor: Colors.red.withOpacity(0.2),
              labelStyle: TextStyle(
                color: isSelected ? Colors.red : themeService.textSecondaryColor,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildExpenseSummary(ThemeService themeService) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Expanded(
            child: _buildSummaryCard(
              title: 'Total Expenses',
              amount: '\$${_totalExpenses.toStringAsFixed(2)}',
              subtitle: 'for $_selectedPeriod',
              color: Colors.red,
              icon: Icons.trending_down_rounded,
              themeService: themeService,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildSummaryCard(
              title: 'Average Expense',
              amount: '\$${_averageExpense.toStringAsFixed(2)}',
              subtitle: _getAverageSubtitle(),
              color: Colors.orange,
              icon: Icons.analytics_rounded,
              themeService: themeService,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard({
    required String title,
    required String amount,
    required String subtitle,
    required Color color,
    required IconData icon,
    required ThemeService themeService,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: themeService.surfaceColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const Spacer(),
              Icon(Icons.more_vert_rounded, color: themeService.textSecondaryColor, size: 16),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: TextStyle(
              color: themeService.textSecondaryColor,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            amount,
            style: TextStyle(
              color: themeService.textPrimaryColor,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            subtitle,
            style: TextStyle(
              color: themeService.textSecondaryColor,
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExpenseCategories(ThemeService themeService) {
    final categories = [
      {'name': 'Food & Dining', 'amount': '\$1,200.00', 'percentage': 38, 'color': Colors.red},
      {'name': 'Transportation', 'amount': '\$680.00', 'percentage': 21, 'color': Colors.blue},
      {'name': 'Shopping', 'amount': '\$520.00', 'percentage': 16, 'color': Colors.purple},
      {'name': 'Bills & Utilities', 'amount': '\$480.00', 'percentage': 15, 'color': Colors.orange},
      {'name': 'Entertainment', 'amount': '\$300.00', 'percentage': 10, 'color': Colors.green},
    ];

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Expense Categories',
            style: TextStyle(
              color: themeService.textPrimaryColor,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          ...categories.map((category) => _buildCategoryItem(category, themeService)),
        ],
      ),
    );
  }

  Widget _buildCategoryItem(Map<String, dynamic> category, ThemeService themeService) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: themeService.surfaceColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: category['color'].withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              _getCategoryIcon(category['name']),
              color: category['color'],
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  category['name'],
                  style: TextStyle(
                    color: themeService.textPrimaryColor,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                LinearProgressIndicator(
                  value: category['percentage'] / 100,
                  backgroundColor: Colors.grey[200],
                  valueColor: AlwaysStoppedAnimation<Color>(category['color']),
                ),
              ],
            ),
          ),
          const SizedBox(width: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                category['amount'],
                style: TextStyle(
                  color: themeService.textPrimaryColor,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '${category['percentage']}%',
                style: TextStyle(
                  color: themeService.textSecondaryColor,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  IconData _getCategoryIcon(String categoryName) {
    switch (categoryName) {
      case 'Food & Dining':
        return Icons.restaurant_rounded;
      case 'Transportation':
        return Icons.directions_car_rounded;
      case 'Shopping':
        return Icons.shopping_bag_rounded;
      case 'Bills & Utilities':
        return Icons.receipt_rounded;
      case 'Entertainment':
        return Icons.movie_rounded;
      default:
        return Icons.category_rounded;
    }
  }

  Widget _buildRecentExpenseTransactions(ThemeService themeService) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Recent Expenses',
                style: TextStyle(
                  color: themeService.textPrimaryColor,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              TextButton(
                onPressed: () {
                  // Navigate to all expense transactions
                },
                child: Text(
                  'View All',
                  style: TextStyle(color: themeService.primaryColor),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          // Placeholder for recent transactions
          Container(
            padding: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              color: themeService.surfaceColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Center(
              child: Column(
                children: [
                  Icon(
                    Icons.receipt_long_rounded,
                    size: 48,
                    color: themeService.textSecondaryColor,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'No recent expense transactions',
                    style: TextStyle(
                      color: themeService.textSecondaryColor,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showFilterOptions(BuildContext context) {
    // TODO: Implement filter options
  }

  void _showSearchDialog(BuildContext context) {
    // TODO: Implement search functionality
  }

  Future<void> _refreshData() async {
    // TODO: Implement data refresh
    await Future.delayed(const Duration(seconds: 1));
  }

  String _getAverageSubtitle() {
    switch (_selectedPeriod) {
      case 'Today':
        return 'today';
      case 'This Week':
        return 'per day this week';
      case 'This Month':
        return 'per day this month';
      case 'This Year':
        return 'per day this year';
      default:
        return 'per transaction';
    }
  }

  // Helper function to get display name from category string
  String _getCategoryDisplayName(String categoryString) {
    try {
      final category = TransactionCategory.values.firstWhere(
        (e) => e.name == categoryString,
        orElse: () => TransactionCategory.other_expense,
      );
      return category.displayName;
    } catch (e) {
      // Fallback for unknown categories
      return categoryString.replaceAll('_', ' ').split(' ').map((word) =>
          word[0].toUpperCase() + word.substring(1)).join(' ');
    }
  }
}
