import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/models/confidence_review.dart';
import '../../../core/models/transaction.dart';

class ConfidenceReviewCard extends StatelessWidget {
  final ConfidenceReview review;
  final Function(ReviewAction, String?, TransactionType?, double?, String?)? onReview;
  final bool showActions;

  const ConfidenceReviewCard({
    super.key,
    required this.review,
    this.onReview,
    this.showActions = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(
          color: _getConfidenceColor(review.confidenceLevel).withOpacity(0.3),
          width: 2,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          _buildTransactionDetails(),
          if (showActions && onReview != null) _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _getConfidenceColor(review.confidenceLevel).withOpacity(0.1),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: _getConfidenceColor(review.confidenceLevel),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              _getConfidenceIcon(review.confidenceLevel),
              color: Colors.white,
              size: 16,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Confidence: ${_getConfidenceLevelText(review.confidenceLevel)}',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: _getConfidenceColor(review.confidenceLevel),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Created: ${_formatDate(review.createdAt)}',
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getStatusColor(review.status).withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              review.status.name.toUpperCase(),
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.w600,
                color: _getStatusColor(review.status),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionDetails() {
    final transaction = review.originalTransaction;
    final smsTransaction = review.originalSmsTransaction;
    
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (transaction != null) ...[
            _buildDetailRow('Title', transaction.title),
            _buildDetailRow('Amount', '\$${transaction.amount.toStringAsFixed(2)}'),
            _buildDetailRow('Type', transaction.type.name.toUpperCase()),
            _buildDetailRow('Category', transaction.category.name.toUpperCase()),
            _buildDetailRow('Date', _formatDate(transaction.date)),
          ] else if (smsTransaction != null) ...[
            _buildDetailRow('SMS Content', smsTransaction.rawMessage),
            _buildDetailRow('Amount', '\$${smsTransaction.amount?.toStringAsFixed(2)}'),
            _buildDetailRow('Type', smsTransaction.transactionType?.toUpperCase() ?? 'Unknown'),
            _buildDetailRow('Category', smsTransaction.category?.toUpperCase() ?? 'Unknown'),
            _buildDetailRow('Date', _formatDate(smsTransaction.receivedAt)),
          ],
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.05),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColors.primary.withOpacity(0.2)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'AI Classification:',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: AppColors.primary,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Type: ${review.classification.suggestedType.name.toUpperCase() ?? 'Unknown'}',
                  style: const TextStyle(fontSize: 12),
                ),
                Text(
                  'Category: ${review.classification.category.toUpperCase() ?? 'Unknown'}',
                  style: const TextStyle(fontSize: 12),
                ),
                Text(
                  'Confidence: ${(review.classification.confidence * 100).toStringAsFixed(1)}%',
                  style: const TextStyle(fontSize: 12),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: AppColors.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => onReview?.call(ReviewAction.accept, null, null, null, null),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.success,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              icon: const Icon(Icons.check, size: 16),
              label: const Text('Accept', style: TextStyle(fontSize: 12)),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => onReview?.call(ReviewAction.reject, null, null, null, null),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.error,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              icon: const Icon(Icons.close, size: 16),
              label: const Text('Reject', style: TextStyle(fontSize: 12)),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => _showModifyDialog(),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.warning,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              icon: const Icon(Icons.edit, size: 16),
              label: const Text('Modify', style: TextStyle(fontSize: 12)),
            ),
          ),
        ],
      ),
    );
  }

  void _showModifyDialog() {
    // Implementation for modify dialog would go here
    Get.snackbar(
      'Modify Transaction',
      'Modify dialog not implemented yet',
      backgroundColor: AppColors.warning,
      colorText: Colors.white,
    );
  }

  Color _getConfidenceColor(ConfidenceLevel level) {
    switch (level) {
      case ConfidenceLevel.veryHigh:
        return AppColors.success;
      case ConfidenceLevel.high:
        return Colors.lightGreen;
      case ConfidenceLevel.medium:
        return AppColors.warning;
      case ConfidenceLevel.low:
        return Colors.orange;
      case ConfidenceLevel.veryLow:
        return AppColors.error;
    }
  }

  IconData _getConfidenceIcon(ConfidenceLevel level) {
    switch (level) {
      case ConfidenceLevel.veryHigh:
        return Icons.verified;
      case ConfidenceLevel.high:
        return Icons.check_circle;
      case ConfidenceLevel.medium:
        return Icons.help;
      case ConfidenceLevel.low:
        return Icons.warning;
      case ConfidenceLevel.veryLow:
        return Icons.error;
    }
  }

  String _getConfidenceLevelText(ConfidenceLevel level) {
    switch (level) {
      case ConfidenceLevel.veryHigh:
        return 'Very High';
      case ConfidenceLevel.high:
        return 'High';
      case ConfidenceLevel.medium:
        return 'Medium';
      case ConfidenceLevel.low:
        return 'Low';
      case ConfidenceLevel.veryLow:
        return 'Very Low';
    }
  }

  Color _getStatusColor(ReviewStatus status) {
    switch (status) {
      case ReviewStatus.pending:
        return AppColors.warning;
      case ReviewStatus.reviewed:
        return AppColors.success;
      case ReviewStatus.autoAccepted:
        return AppColors.primary;
      case ReviewStatus.autoRejected:
        return AppColors.error;
      case ReviewStatus.expired:
        return AppColors.textSecondary;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }
}
