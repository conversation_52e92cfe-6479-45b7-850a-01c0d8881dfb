import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/models/confidence_review.dart';
import '../../../core/models/transaction.dart';
import '../../../core/services/transaction_confidence_service.dart';
import '../widgets/confidence_review_card.dart';
import '../widgets/confidence_statistics_card.dart';
import '../widgets/review_filter_modal.dart';
import '../widgets/review_filter_modal.dart';

class ConfidenceReviewScreen extends StatefulWidget {
  const ConfidenceReviewScreen({super.key});

  @override
  State<ConfidenceReviewScreen> createState() => _ConfidenceReviewScreenState();
}

class _ConfidenceReviewScreenState extends State<ConfidenceReviewScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  
  final TransactionConfidenceService _confidenceService = Get.find();
  
  final RxList<ConfidenceReview> _filteredPendingReviews = <ConfidenceReview>[].obs;
  final RxList<ConfidenceReview> _filteredHistoryReviews = <ConfidenceReview>[].obs;
  final RxString _searchQuery = ''.obs;
  final RxList<ConfidenceLevel> _selectedConfidenceLevels = <ConfidenceLevel>[].obs;
  final RxList<ReviewStatus> _selectedStatuses = <ReviewStatus>[].obs;
  final RxBool _showOnlyUrgent = false.obs;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadReviews();
    
    // Listen to changes in confidence service
    ever(_confidenceService.pendingReviews as RxInterface<Object?>, (_) => _applyFilters());
    ever(_confidenceService.reviewHistory as RxInterface<Object?>, (_) => _applyFilters());
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadReviews() {
    _applyFilters();
  }

  void _applyFilters() {
    // Filter pending reviews
    var pendingReviews = _confidenceService.pendingReviews.toList();
    
    if (_searchQuery.value.isNotEmpty) {
      pendingReviews = pendingReviews.where((review) =>
          review.displayDescription.toLowerCase().contains(_searchQuery.value.toLowerCase()) ||
          review.displayCategory.toLowerCase().contains(_searchQuery.value.toLowerCase())
      ).toList();
    }
    
    if (_selectedConfidenceLevels.isNotEmpty) {
      pendingReviews = pendingReviews.where((review) =>
          _selectedConfidenceLevels.contains(review.confidenceLevel)
      ).toList();
    }
    
    if (_showOnlyUrgent.value) {
      pendingReviews = pendingReviews.where((review) => review.isUrgent).toList();
    }
    
    // Sort by priority
    pendingReviews.sort((a, b) => a.priority.compareTo(b.priority));
    
    _filteredPendingReviews.assignAll(pendingReviews);
    
    // Filter history reviews
    var historyReviews = _confidenceService.reviewHistory.toList();
    
    if (_searchQuery.value.isNotEmpty) {
      historyReviews = historyReviews.where((review) =>
          review.displayDescription.toLowerCase().contains(_searchQuery.value.toLowerCase()) ||
          review.displayCategory.toLowerCase().contains(_searchQuery.value.toLowerCase())
      ).toList();
    }
    
    if (_selectedConfidenceLevels.isNotEmpty) {
      historyReviews = historyReviews.where((review) =>
          _selectedConfidenceLevels.contains(review.confidenceLevel)
      ).toList();
    }
    
    if (_selectedStatuses.isNotEmpty) {
      historyReviews = historyReviews.where((review) =>
          _selectedStatuses.contains(review.status)
      ).toList();
    }
    
    // Sort by review date (newest first)
    historyReviews.sort((a, b) => (b.reviewedAt ?? b.createdAt).compareTo(a.reviewedAt ?? a.createdAt));
    
    _filteredHistoryReviews.assignAll(historyReviews);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'Transaction Reviews',
          style: TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.white,
        foregroundColor: AppColors.textPrimary,
        elevation: 0,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(1),
          child: Container(
            height: 1,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppColors.primary.withOpacity(0.3),
                  AppColors.secondary.withOpacity(0.3),
                ],
              ),
            ),
          ),
        ),
        actions: [
          IconButton(
            onPressed: _showFilterModal,
            icon: const Icon(Icons.filter_list_rounded),
            tooltip: 'Filter Reviews',
          ),
          IconButton(
            onPressed: _showStatistics,
            icon: const Icon(Icons.analytics_rounded),
            tooltip: 'Statistics',
          ),
        ],
      ),
      body: Column(
        children: [
          // Search bar
          Container(
            color: Colors.white,
            padding: const EdgeInsets.all(16),
            child: TextField(
              onChanged: (value) {
                _searchQuery.value = value;
                _applyFilters();
              },
              decoration: InputDecoration(
                hintText: 'Search reviews...',
                prefixIcon: const Icon(Icons.search_rounded),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: AppColors.primary),
                ),
                filled: true,
                fillColor: Colors.grey[50],
              ),
            ),
          ),
          
          // Tab bar
          Container(
            color: Colors.white,
            child: TabBar(
              controller: _tabController,
              labelColor: AppColors.primary,
              unselectedLabelColor: Colors.grey[600],
              indicatorColor: AppColors.primary,
              tabs: [
                Tab(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.pending_actions_rounded, size: 20),
                      const SizedBox(width: 8),
                      Obx(() => Text('Pending (${_filteredPendingReviews.length})')),
                    ],
                  ),
                ),
                Tab(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.history_rounded, size: 20),
                      const SizedBox(width: 8),
                      Obx(() => Text('History (${_filteredHistoryReviews.length})')),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          // Content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildPendingReviewsTab(),
                _buildHistoryTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: Obx(() {
        if (_filteredPendingReviews.isEmpty) return const SizedBox.shrink();
        
        return FloatingActionButton.extended(
          onPressed: _showBulkActions,
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          heroTag: "confidence_review_fab",
          icon: const Icon(Icons.checklist_rounded),
          label: const Text('Bulk Actions'),
        );
      }),
    );
  }

  Widget _buildPendingReviewsTab() {
    return Obx(() {
      if (_filteredPendingReviews.isEmpty) {
        return _buildEmptyState(
          icon: Icons.task_alt_rounded,
          title: 'No Pending Reviews',
          subtitle: 'All transactions have been reviewed or auto-processed.',
        );
      }
      
      return AnimationLimiter(
        child: ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: _filteredPendingReviews.length,
          itemBuilder: (context, index) {
            final review = _filteredPendingReviews[index];
            return AnimationConfiguration.staggeredList(
              position: index,
              duration: const Duration(milliseconds: 375),
              child: SlideAnimation(
                verticalOffset: 50.0,
                child: FadeInAnimation(
                  child: ConfidenceReviewCard(
                    review: review,
                    onReview: (action, category, type, amount, notes) => _reviewTransaction(
                      review.id,
                      action,
                      category,
                      type,
                      amount,
                      notes,
                    ),
                    showActions: true,
                  ),
                ),
              ),
            );
          },
        ),
      );
    });
  }

  Widget _buildHistoryTab() {
    return Obx(() {
      if (_filteredHistoryReviews.isEmpty) {
        return _buildEmptyState(
          icon: Icons.history_rounded,
          title: 'No Review History',
          subtitle: 'Completed reviews will appear here.',
        );
      }
      
      return AnimationLimiter(
        child: ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: _filteredHistoryReviews.length,
          itemBuilder: (context, index) {
            final review = _filteredHistoryReviews[index];
            return AnimationConfiguration.staggeredList(
              position: index,
              duration: const Duration(milliseconds: 375),
              child: SlideAnimation(
                verticalOffset: 50.0,
                child: FadeInAnimation(
                  child: ConfidenceReviewCard(
                    review: review,
                    onReview: null, // No actions for history
                    showActions: false,
                  ),
                ),
              ),
            );
          },
        ),
      );
    });
  }

  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              icon,
              size: 64,
              color: AppColors.primary,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            title,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _reviewTransaction(
    String reviewId,
    ReviewAction action,
    String? category,
    TransactionType? type,
    double? amount,
    String? notes,
  ) async {
    HapticFeedback.mediumImpact();
    
    await _confidenceService.reviewTransaction(
      reviewId: reviewId,
      action: action,
      correctedCategory: category,
      correctedType: type,
      correctedAmount: amount,
      notes: notes,
    );
    
    _showReviewSuccessMessage(action);
  }

  void _showReviewSuccessMessage(ReviewAction action) {
    String message;
    switch (action) {
      case ReviewAction.accept:
        message = 'Transaction accepted and added';
        break;
      case ReviewAction.reject:
        message = 'Transaction rejected';
        break;
      case ReviewAction.modify:
        message = 'Transaction modified and added';
        break;
      case ReviewAction.skip:
        message = 'Transaction skipped';
        break;
    }
    
    Get.snackbar(
      'Review Complete',
      message,
      backgroundColor: AppColors.success,
      colorText: Colors.white,
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  void _showFilterModal() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => ReviewFilterModal(
        selectedConfidenceLevels: _selectedConfidenceLevels,
        selectedStatuses: _selectedStatuses,
        showOnlyUrgent: _showOnlyUrgent.value,
        onApplyFilters: (confidenceLevels, statuses, urgentOnly) {
          _selectedConfidenceLevels.assignAll(confidenceLevels);
          _selectedStatuses.assignAll(statuses);
          _showOnlyUrgent.value = urgentOnly;
          _applyFilters();
        },
      ),
    );
  }

  void _showStatistics() {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: ConfidenceStatisticsCard(
          statistics: _confidenceService.getConfidenceStatistics(),
        ),
      ),
    );
  }

  void _showBulkActions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Bulk Actions',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _bulkAction(ReviewAction.accept),
                    icon: const Icon(Icons.check_rounded),
                    label: const Text('Accept All'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.success,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _bulkAction(ReviewAction.reject),
                    icon: const Icon(Icons.close_rounded),
                    label: const Text('Reject All'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.error,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _bulkAction(ReviewAction action) async {
    Navigator.pop(context); // Close modal
    
    final reviewIds = _filteredPendingReviews.map((r) => r.id).toList();
    
    await _confidenceService.bulkReviewAction(
      reviewIds: reviewIds,
      action: action,
    );
    
    Get.snackbar(
      'Bulk Action Complete',
      '${reviewIds.length} reviews ${action.name}ed',
      backgroundColor: AppColors.success,
      colorText: Colors.white,
      snackPosition: SnackPosition.BOTTOM,
    );
  }
}
