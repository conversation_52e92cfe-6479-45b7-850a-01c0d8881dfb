import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/services/sms_permissions_service.dart';
import '../../../core/services/sms_background_service.dart';
import '../widgets/sms_permission_card.dart';
import '../widgets/sms_processing_settings_card.dart';
import '../widgets/sms_statistics_card.dart';
// import '../widgets/sms_processing_settings_card.dart';
// import '../widgets/sms_statistics_card.dart';

class SmsSettingsScreen extends StatefulWidget {
  const SmsSettingsScreen({super.key});

  @override
  State<SmsSettingsScreen> createState() => _SmsSettingsScreenState();
}

class _SmsSettingsScreenState extends State<SmsSettingsScreen> {
  final SmsPermissionsService _permissionsService = Get.find();
  final SmsBackgroundService _backgroundService = Get.find();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'SMS Processing Settings',
          style: TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.white,
        foregroundColor: AppColors.textPrimary,
        elevation: 0,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(1),
          child: Container(
            height: 1,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppColors.primary.withOpacity(0.3),
                  AppColors.secondary.withOpacity(0.3),
                ],
              ),
            ),
          ),
        ),
        actions: [
          IconButton(
            onPressed: _showSmsHelp,
            icon: const Icon(Icons.help_outline_rounded),
            tooltip: 'Help',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Permission status card
            Obx(() => SmsPermissionCard(
              permissionStatus: _permissionsService.permissionStatus,
              onRequestPermission: _permissionsService.requestSmsPermissions,
              onOpenSettings: () => _permissionsService.requestSmsPermissions(),
            )),
            
            const SizedBox(height: 16),
            
            // Background processing settings
            Obx(() => SmsProcessingSettingsCard(
              isEnabled: _permissionsService.isBackgroundProcessingEnabled,
              autoProcessNewSms: _permissionsService.autoProcessNewSms,
              processOnlyFinancialSms: _permissionsService.processOnlyFinancialSms,
              maxMessagesToProcess: _permissionsService.maxMessagesToProcess,
              processingIntervalMinutes: _permissionsService.processingIntervalMinutes,
              onToggleBackgroundProcessing: () => _toggleBackgroundProcessing(!_permissionsService.isBackgroundProcessingEnabled),
              onUpdateSettings: _updateProcessingSettings,
              onManualProcess: _permissionsService.manualProcessSms,
            )),
            
            const SizedBox(height: 16),
            
            // Statistics card
            Obx(() => SmsStatisticsCard(
              permissionStats: _permissionsService.getProcessingStats(),
              backgroundStats: _backgroundService.getBackgroundStats(),
              onResetStats: _resetStatistics,
            )),
            
            const SizedBox(height: 16),
            
            // Advanced settings
            _buildAdvancedSettings(),
            
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildAdvancedSettings() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [AppColors.primary, AppColors.secondary],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.settings_rounded,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                const Expanded(
                  child: Text(
                    'Advanced Settings',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                _buildAdvancedSettingItem(
                  icon: Icons.refresh_rounded,
                  title: 'Reset All Data',
                  subtitle: 'Clear all SMS processing data and statistics',
                  onTap: _showResetDataDialog,
                  isDestructive: true,
                ),
                const Divider(height: 32),
                _buildAdvancedSettingItem(
                  icon: Icons.bug_report_rounded,
                  title: 'Debug Information',
                  subtitle: 'View detailed SMS processing logs',
                  onTap: _showDebugInfo,
                ),
                const Divider(height: 32),
                _buildAdvancedSettingItem(
                  icon: Icons.download_rounded,
                  title: 'Export Settings',
                  subtitle: 'Export SMS processing configuration',
                  onTap: _exportSettings,
                ),
                const Divider(height: 32),
                _buildAdvancedSettingItem(
                  icon: Icons.upload_rounded,
                  title: 'Import Settings',
                  subtitle: 'Import SMS processing configuration',
                  onTap: _importSettings,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAdvancedSettingItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return InkWell(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap();
      },
      borderRadius: BorderRadius.circular(12),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isDestructive 
                    ? AppColors.error.withOpacity(0.1)
                    : AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: isDestructive ? AppColors.error : AppColors.primary,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: isDestructive ? AppColors.error : AppColors.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.chevron_right_rounded,
              color: Colors.grey[400],
            ),
          ],
        ),
      ),
    );
  }

  void _toggleBackgroundProcessing(bool enabled) async {
    HapticFeedback.mediumImpact();
    
    if (enabled) {
      await _permissionsService.enableBackgroundProcessing();
      await _backgroundService.startPeriodicSmsProcessing();
    } else {
      _permissionsService.disableBackgroundProcessing();
      await _backgroundService.stopPeriodicSmsProcessing();
    }
  }

  void _updateProcessingSettings({
    bool? autoProcessNewSms,
    bool? processOnlyFinancialSms,
    int? maxMessagesToProcess,
    int? processingIntervalMinutes,
  }) {
    _permissionsService.updateProcessingSettings(
      autoProcessNewSms: autoProcessNewSms,
      processOnlyFinancialSms: processOnlyFinancialSms,
      maxMessagesToProcess: maxMessagesToProcess,
      processingIntervalMinutes: processingIntervalMinutes,
    );
    
    // Restart background processing with new settings
    if (_permissionsService.isBackgroundProcessingEnabled) {
      _backgroundService.stopPeriodicSmsProcessing().then((_) {
        _backgroundService.startPeriodicSmsProcessing(
          frequency: Duration(minutes: processingIntervalMinutes ?? 30),
        );
      });
    }
  }

  void _resetStatistics() {
    HapticFeedback.mediumImpact();
    _permissionsService.resetProcessingStats();
    _backgroundService.resetBackgroundStats();
    
    Get.snackbar(
      'Statistics Reset',
      'All SMS processing statistics have been cleared',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  void _showSmsHelp() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('SMS Processing Help'),
        content: const SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'How SMS Processing Works',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
              SizedBox(height: 8),
              Text(
                '• Automatically reads SMS messages from financial services\n'
                '• Extracts transaction information using AI patterns\n'
                '• Classifies transactions into appropriate categories\n'
                '• Runs in the background to process new messages',
              ),
              SizedBox(height: 16),
              Text(
                'Privacy & Security',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
              SizedBox(height: 8),
              Text(
                '• Only processes messages from known financial senders\n'
                '• All data is stored locally on your device\n'
                '• No SMS content is sent to external servers\n'
                '• You can disable processing at any time',
              ),
              SizedBox(height: 16),
              Text(
                'Permissions Required',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
              SizedBox(height: 8),
              Text(
                '• SMS Read Permission: To access SMS messages\n'
                '• Background Processing: To process messages automatically\n'
                '• Storage Permission: To save processed transactions',
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }

  void _showResetDataDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset All Data'),
        content: const Text(
          'This will permanently delete all SMS processing data, statistics, '
          'and learned patterns. This action cannot be undone.\n\n'
          'Are you sure you want to continue?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _resetAllData();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
            ),
            child: const Text('Reset All'),
          ),
        ],
      ),
    );
  }

  void _resetAllData() {
    HapticFeedback.heavyImpact();
    
    // Reset all services
    _permissionsService.resetProcessingStats();
    _backgroundService.resetBackgroundStats();
    
    // Stop background processing
    _permissionsService.disableBackgroundProcessing();
    _backgroundService.cancelAllBackgroundTasks();
    
    Get.snackbar(
      'Data Reset Complete',
      'All SMS processing data has been cleared',
      backgroundColor: AppColors.error,
      colorText: Colors.white,
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  void _showDebugInfo() {
    final permissionStats = _permissionsService.getProcessingStats();
    final backgroundStats = _backgroundService.getBackgroundStats();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Debug Information'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Permission Service',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              ...permissionStats.entries.map(
                (entry) => Text('${entry.key}: ${entry.value}'),
              ),
              const SizedBox(height: 16),
              const Text(
                'Background Service',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              ...backgroundStats.entries.map(
                (entry) => Text('${entry.key}: ${entry.value}'),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _exportSettings() {
    // TODO: Implement settings export
    Get.snackbar(
      'Export Settings',
      'Settings export functionality will be available in a future update',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  void _importSettings() {
    // TODO: Implement settings import
    Get.snackbar(
      'Import Settings',
      'Settings import functionality will be available in a future update',
      snackPosition: SnackPosition.BOTTOM,
    );
  }
}
