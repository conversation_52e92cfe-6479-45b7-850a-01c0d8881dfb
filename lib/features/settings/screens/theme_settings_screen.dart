import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/services/theme_service.dart';
import '../../../core/models/country_flag.dart';
import '../../common/widgets/common_app_bar.dart';
import '../widgets/flag_theme_card.dart';

class ThemeSettingsScreen extends StatelessWidget {
  const ThemeSettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        title: 'Theme Settings',
        showAccountInfo: true,
        actions: [
          IconButton(
            onPressed: () => ThemeService.to.resetToDefault(),
            icon: const Icon(Icons.refresh_rounded),
            tooltip: 'Reset to Default',
          ),
        ],
      ),
      body: Obx(() {
        final themeService = ThemeService.to;
        
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Current Theme Preview
              _buildCurrentThemeSection(themeService),
              
              const SizedBox(height: 32),
              
              // Dark Mode Toggle
              _buildDarkModeSection(themeService),
              
              const SizedBox(height: 32),
              
              // Flag Selection
              _buildFlagSelectionSection(themeService),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildCurrentThemeSection(ThemeService themeService) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Current Theme',
          style: Get.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
            color: themeService.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: themeService.primaryGradient,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: themeService.primaryColor.withOpacity(0.3),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    themeService.flagEmoji,
                    style: const TextStyle(fontSize: 32),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    themeService.flagName,
                    style: Get.textTheme.titleLarge?.copyWith(
                      color: themeService.getContrastingTextColor(themeService.primaryColor),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildColorPreview('Primary', themeService.primaryColor),
                  _buildColorPreview('Secondary', themeService.secondaryColor),
                  _buildColorPreview('Accent', themeService.accentColor),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildColorPreview(String label, Color color) {
    return Column(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
            border: Border.all(color: Colors.white, width: 2),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: Get.textTheme.labelSmall?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildDarkModeSection(ThemeService themeService) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: themeService.surfaceColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: themeService.borderColor),
      ),
      child: Row(
        children: [
          Icon(
            themeService.isDarkMode ? Icons.dark_mode_rounded : Icons.light_mode_rounded,
            color: themeService.primaryColor,
            size: 28,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Dark Mode',
                  style: Get.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: themeService.textPrimaryColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  themeService.isDarkMode ? 'Dark theme enabled' : 'Light theme enabled',
                  style: Get.textTheme.bodyMedium?.copyWith(
                    color: themeService.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: themeService.isDarkMode,
            onChanged: (_) => themeService.toggleDarkMode(),
          ),
        ],
      ),
    );
  }

  Widget _buildFlagSelectionSection(ThemeService themeService) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Choose Flag Theme',
          style: Get.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
            color: themeService.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Select a country flag to customize your app colors',
          style: Get.textTheme.bodyMedium?.copyWith(
            color: themeService.textSecondaryColor,
          ),
        ),
        const SizedBox(height: 16),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            childAspectRatio: 1.2,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
          ),
          itemCount: themeService.availableFlags.length,
          itemBuilder: (context, index) {
            final flag = themeService.availableFlags[index];
            final isSelected = themeService.selectedFlag.code == flag.code;
            
            return FlagThemeCard(
              flag: flag,
              isSelected: isSelected,
              onTap: () => themeService.changeFlag(flag),
            );
          },
        ),
      ],
    );
  }
}
