import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/services/sms_permissions_service.dart';

class SmsPermissionCard extends StatelessWidget {
  final SmsPermissionStatus permissionStatus;
  final Future<bool> Function() onRequestPermission;
  final VoidCallback onOpenSettings;

  const SmsPermissionCard({
    super.key,
    required this.permissionStatus,
    required this.onRequestPermission,
    required this.onOpenSettings,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildHeader(),
          _buildContent(),
          _buildActions(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    Color headerColor;
    IconData headerIcon;
    String headerTitle;

    switch (permissionStatus) {
      case SmsPermissionStatus.granted:
        headerColor = AppColors.success;
        headerIcon = Icons.check_circle_rounded;
        headerTitle = 'SMS Permission Granted';
        break;
      case SmsPermissionStatus.denied:
        headerColor = Colors.orange;
        headerIcon = Icons.warning_rounded;
        headerTitle = 'SMS Permission Required';
        break;
      case SmsPermissionStatus.permanentlyDenied:
        headerColor = AppColors.error;
        headerIcon = Icons.block_rounded;
        headerTitle = 'SMS Permission Denied';
        break;
      default:
        headerColor = Colors.grey;
        headerIcon = Icons.help_outline_rounded;
        headerTitle = 'SMS Permission Status';
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: headerColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              headerIcon,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              headerTitle,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
          _buildStatusBadge(),
        ],
      ),
    );
  }

  Widget _buildStatusBadge() {
    String statusText;
    Color badgeColor;

    switch (permissionStatus) {
      case SmsPermissionStatus.granted:
        statusText = 'GRANTED';
        badgeColor = Colors.white;
        break;
      case SmsPermissionStatus.denied:
        statusText = 'DENIED';
        badgeColor = Colors.white;
        break;
      case SmsPermissionStatus.permanentlyDenied:
        statusText = 'BLOCKED';
        badgeColor = Colors.white;
        break;
      default:
        statusText = 'UNKNOWN';
        badgeColor = Colors.white;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.2),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        statusText,
        style: TextStyle(
          color: badgeColor,
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
      ),
    );
  }

  Widget _buildContent() {
    String description;
    List<String> features;

    switch (permissionStatus) {
      case SmsPermissionStatus.granted:
        description = 'SMS processing is enabled and ready to automatically extract transaction information from your messages.';
        features = [
          'Automatic transaction detection',
          'Real-time SMS processing',
          'Background message analysis',
          'Secure local processing',
        ];
        break;
      case SmsPermissionStatus.denied:
        description = 'SMS permission is required to automatically process transaction messages from banks and mobile money services.';
        features = [
          'Read SMS messages from financial services',
          'Extract transaction amounts and details',
          'Automatically categorize transactions',
          'Process messages in the background',
        ];
        break;
      case SmsPermissionStatus.permanentlyDenied:
        description = 'SMS permission has been permanently denied. Please enable it in app settings to use automatic transaction processing.';
        features = [
          'Open device settings',
          'Navigate to app permissions',
          'Enable SMS permission',
          'Return to app to continue',
        ];
        break;
      default:
        description = 'SMS permission status is unknown. Please check your device settings.';
        features = [];
    }

    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            description,
            style: const TextStyle(
              fontSize: 16,
              color: AppColors.textSecondary,
              height: 1.5,
            ),
          ),
          if (features.isNotEmpty) ...[
            const SizedBox(height: 16),
            const Text(
              'Features:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 8),
            ...features.map((feature) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  Container(
                    width: 6,
                    height: 6,
                    decoration: BoxDecoration(
                      color: AppColors.primary,
                      borderRadius: BorderRadius.circular(3),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      feature,
                      style: const TextStyle(
                        fontSize: 14,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ),
                ],
              ),
            )),
          ],
        ],
      ),
    );
  }

  Widget _buildActions() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          if (permissionStatus == SmsPermissionStatus.granted) ...[
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppColors.success.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: AppColors.success.withOpacity(0.3)),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.check_circle_rounded,
                      color: AppColors.success,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      'Permission Granted',
                      style: TextStyle(
                        color: AppColors.success,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ] else if (permissionStatus == SmsPermissionStatus.permanentlyDenied) ...[
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () {
                  HapticFeedback.mediumImpact();
                  onOpenSettings();
                },
                icon: const Icon(Icons.settings_rounded),
                label: const Text('Open Settings'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
          ] else ...[
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () async {
                  HapticFeedback.mediumImpact();
                  await onRequestPermission();
                },
                icon: const Icon(Icons.security_rounded),
                label: const Text('Grant Permission'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
