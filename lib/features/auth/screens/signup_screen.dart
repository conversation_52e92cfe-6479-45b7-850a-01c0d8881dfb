import 'package:flutter/material.dart';
import 'package:animate_do/animate_do.dart';
import 'package:get/get.dart';
import '../../../core/theme/design_system.dart';
import '../../../core/theme/typography.dart';
import '../../../core/services/auth_service.dart';
import '../../../core/routes/app_routes.dart';
import '../../../shared/widgets/sophisticated_text_field.dart';
import '../../../shared/widgets/sophisticated_social_button.dart';
import '../../../shared/widgets/gradient_button.dart';

class SignUpScreen extends StatefulWidget {
  const SignUpScreen({super.key});

  @override
  State<SignUpScreen> createState() => _SignUpScreenState();
}

class _SignUpScreenState extends State<SignUpScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;
  bool _isLoading = false;

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  String? _validateName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Full name is required';
    }
    if (value.length < 2) {
      return 'Name must be at least 2 characters';
    }
    return null;
  }

  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
      return 'Please enter a valid email';
    }
    return null;
  }

  String? _validatePhone(String? value) {
    if (value == null || value.isEmpty) {
      return 'Phone number is required';
    }
    if (value.length < 10) {
      return 'Please enter a valid phone number';
    }
    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }
    if (value.length < 8) {
      return 'Password must be at least 8 characters';
    }
    if (!RegExp(r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)').hasMatch(value)) {
      return 'Password must contain uppercase, lowercase, and number';
    }
    return null;
  }

  String? _validateConfirmPassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please confirm your password';
    }
    if (value != _passwordController.text) {
      return 'Passwords do not match';
    }
    return null;
  }

  Future<void> _signUp() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      try {
        final authService = AuthService.to;
        final result = await authService.signUpWithEmail(
          email: _emailController.text.trim(),
          password: _passwordController.text,
          name: _nameController.text.trim(),
        );

        if (result.isSuccess) {
          // Show success message
          Get.snackbar(
            'Welcome to Rekodi!',
            result.message,
            backgroundColor: DesignSystem.success,
            colorText: DesignSystem.textOnPrimary,
            duration: DesignSystem.animationSlow,
            borderRadius: DesignSystem.radiusMedium,
            margin: const EdgeInsets.all(DesignSystem.spaceM),
          );

          // Navigate to dashboard
          Get.offAllNamed(AppRoutes.dashboard);
        } else {
          // Show error message
          Get.snackbar(
            'Registration Failed',
            result.message,
            backgroundColor: DesignSystem.error,
            colorText: DesignSystem.textOnPrimary,
            duration: DesignSystem.animationSlow,
            borderRadius: DesignSystem.radiusMedium,
            margin: const EdgeInsets.all(DesignSystem.spaceM),
          );
        }
      } catch (e) {
        Get.snackbar(
          'Error',
          'An unexpected error occurred. Please try again.',
          backgroundColor: DesignSystem.error,
          colorText: DesignSystem.textOnPrimary,
          duration: DesignSystem.animationSlow,
          borderRadius: DesignSystem.radiusMedium,
          margin: const EdgeInsets.all(DesignSystem.spaceM),
        );
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  Future<void> _signUpWithOAuth(String provider) async {
    try {
      setState(() {
        _isLoading = true;
      });

      final authService = AuthService.to;
      final result = await authService.signInWithOAuth(provider);

      if (result.isSuccess) {
        Get.snackbar(
          'Success',
          result.message,
          backgroundColor: DesignSystem.success,
          colorText: Colors.white,
          duration: const Duration(seconds: 2),
        );

        Get.offAllNamed(AppRoutes.dashboard);
      } else {
        Get.snackbar(
          'Error',
          result.message,
          backgroundColor: DesignSystem.error,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
        );
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        'OAuth sign-up failed. Please try again.',
        backgroundColor: DesignSystem.error,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DesignSystem.neutralBackground,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(DesignSystem.spaceL),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Back button
                FadeInLeft(
                  duration: DesignSystem.animationMedium,
                  child: Container(
                    margin: const EdgeInsets.only(bottom: DesignSystem.spaceL),
                    child: IconButton(
                      onPressed: () => Get.back(),
                      icon: Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: DesignSystem.cardBackground,
                          borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
                          boxShadow: DesignSystem.shadowLow,
                        ),
                        child: const Icon(
                          Icons.arrow_back_ios_rounded,
                          color: DesignSystem.textPrimary,
                          size: 20,
                        ),
                      ),
                    ),
                  ),
                ),

                // Header
                FadeInDown(
                  duration: DesignSystem.animationMedium,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // App logo/icon
                      Container(
                        width: 60,
                        height: 60,
                        decoration: BoxDecoration(
                          gradient: DesignSystem.primaryGradient,
                          borderRadius: BorderRadius.circular(DesignSystem.radiusLarge),
                          boxShadow: DesignSystem.shadowMedium,
                        ),
                        child: const Icon(
                          Icons.person_add_rounded,
                          color: DesignSystem.textOnPrimary,
                          size: 30,
                        ),
                      ),

                      const SizedBox(height: DesignSystem.spaceL),

                      // Create account text
                      ShaderMask(
                        shaderCallback: (bounds) => DesignSystem.primaryGradient.createShader(bounds),
                        child: Text(
                          'Create Account',
                          style: AppTypography.displayLarge.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      ),

                      const SizedBox(height: DesignSystem.spaceS),

                      Text(
                        'Join us and start your financial journey today',
                        style: AppTypography.bodyLarge.copyWith(
                          color: DesignSystem.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: DesignSystem.spaceXL),

                // Full Name Field
                FadeInLeft(
                  duration: DesignSystem.animationMedium,
                  delay: const Duration(milliseconds: 200),
                  child: SophisticatedTextField(
                    controller: _nameController,
                    label: 'Full Name',
                    hint: 'Enter your full name',
                    prefixIcon: Icons.person_outline_rounded,
                    textCapitalization: TextCapitalization.words,
                    validator: _validateName,
                  ),
                ),

                const SizedBox(height: DesignSystem.spaceL),

                // Email Field
                FadeInLeft(
                  duration: DesignSystem.animationMedium,
                  delay: const Duration(milliseconds: 400),
                  child: SophisticatedTextField(
                    controller: _emailController,
                    label: 'Email Address',
                    hint: 'Enter your email',
                    keyboardType: TextInputType.emailAddress,
                    prefixIcon: Icons.email_outlined,
                    validator: _validateEmail,
                  ),
                ),
                
                const SizedBox(height: DesignSystem.spaceL),

                // Phone Field
                FadeInLeft(
                  duration: DesignSystem.animationMedium,
                  delay: const Duration(milliseconds: 600),
                  child: SophisticatedTextField(
                    controller: _phoneController,
                    label: 'Phone Number',
                    hint: 'Enter your phone number',
                    keyboardType: TextInputType.phone,
                    prefixIcon: Icons.phone_outlined,
                    validator: _validatePhone,
                  ),
                ),

                const SizedBox(height: DesignSystem.spaceL),

                // Password Field
                FadeInLeft(
                  duration: DesignSystem.animationMedium,
                  delay: const Duration(milliseconds: 800),
                  child: SophisticatedPasswordField(
                    controller: _passwordController,
                    label: 'Password',
                    hint: 'Create a strong password',
                    validator: _validatePassword,
                    helperText: 'Must contain uppercase, lowercase, and number',
                  ),
                ),
                
                const SizedBox(height: DesignSystem.spaceL),

                // Confirm Password Field
                FadeInLeft(
                  duration: DesignSystem.animationMedium,
                  delay: const Duration(milliseconds: 1000),
                  child: SophisticatedPasswordField(
                    controller: _confirmPasswordController,
                    label: 'Confirm Password',
                    hint: 'Re-enter your password',
                    validator: _validateConfirmPassword,
                  ),
                ),

                const SizedBox(height: DesignSystem.spaceXL),

                // Sign Up Button
                FadeInUp(
                  duration: DesignSystem.animationMedium,
                  delay: const Duration(milliseconds: 1200),
                  child: GradientButton(
                    text: 'Create Account',
                    onPressed: _isLoading ? null : _signUp,
                    isLoading: _isLoading,
                    height: 56,
                    textStyle: AppTypography.buttonLarge,
                  ),
                ),

                const SizedBox(height: DesignSystem.spaceXL),

                // Divider
                FadeInUp(
                  duration: DesignSystem.animationMedium,
                  delay: const Duration(milliseconds: 1400),
                  child: const SocialDivider(text: 'OR SIGN UP WITH'),
                ),

                const SizedBox(height: DesignSystem.spaceXL),

                // Social Login Buttons
                FadeInUp(
                  duration: DesignSystem.animationMedium,
                  delay: const Duration(milliseconds: 1600),
                  child: Row(
                    children: [
                      Expanded(
                        child: SophisticatedSocialButton(
                          provider: 'google',
                          label: 'Google',
                          onPressed: () => _signUpWithOAuth('google'),
                          isLoading: _isLoading,
                        ),
                      ),
                      const SizedBox(width: DesignSystem.spaceM),
                      Expanded(
                        child: SophisticatedSocialButton(
                          provider: 'apple',
                          label: 'Apple',
                          onPressed: () => _signUpWithOAuth('apple'),
                          isLoading: _isLoading,
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: DesignSystem.spaceXL),

                // Sign In Link
                FadeInUp(
                  duration: DesignSystem.animationMedium,
                  delay: const Duration(milliseconds: 1800),
                  child: Center(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'Already have an account? ',
                          style: AppTypography.bodyMedium.copyWith(
                            color: DesignSystem.textSecondary,
                          ),
                        ),
                        TextButton(
                          onPressed: () => Get.back(),
                          style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(
                              horizontal: DesignSystem.spaceS,
                              vertical: DesignSystem.spaceXS,
                            ),
                          ),
                          child: ShaderMask(
                            shaderCallback: (bounds) => DesignSystem.primaryGradient.createShader(bounds),
                            child: Text(
                              'Sign In',
                              style: AppTypography.bodyMedium.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: DesignSystem.spaceL),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
