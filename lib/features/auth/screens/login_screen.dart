import 'package:flutter/material.dart';
import 'package:animate_do/animate_do.dart';
import 'package:get/get.dart';
import '../../../core/theme/design_system.dart';
import '../../../core/theme/typography.dart';
import '../../../core/services/auth_service.dart';
import '../../../core/routes/app_routes.dart';
import '../../../shared/widgets/sophisticated_text_field.dart';
import '../../../shared/widgets/sophisticated_social_button.dart';
import '../../../shared/widgets/gradient_button.dart';
import 'signup_screen.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isPasswordVisible = false;
  bool _rememberMe = false;
  bool _isLoading = false;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
      return 'Please enter a valid email';
    }
    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }
    if (value.length < 6) {
      return 'Password must be at least 6 characters';
    }
    return null;
  }

  Future<void> _signIn() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      try {
        final authService = AuthService.to;
        final result = await authService.signInWithEmail(
          email: _emailController.text.trim(),
          password: _passwordController.text,
        );

        if (result.isSuccess) {
          // Show success message
          Get.snackbar(
            'Welcome Back!',
            result.message,
            backgroundColor: DesignSystem.success,
            colorText: DesignSystem.textOnPrimary,
            duration: DesignSystem.animationSlow,
            borderRadius: DesignSystem.radiusMedium,
            margin: const EdgeInsets.all(DesignSystem.spaceM),
          );

          // Navigate to dashboard
          Get.offAllNamed(AppRoutes.dashboard);
        } else {
          // Show error message
          Get.snackbar(
            'Login Failed',
            result.message,
            backgroundColor: DesignSystem.error,
            colorText: DesignSystem.textOnPrimary,
            duration: DesignSystem.animationSlow,
            borderRadius: DesignSystem.radiusMedium,
            margin: const EdgeInsets.all(DesignSystem.spaceM),
          );
        }
      } catch (e) {
        Get.snackbar(
          'Error',
          'An unexpected error occurred. Please try again.',
          backgroundColor: DesignSystem.error,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
        );
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  void _navigateToSignUp() {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const SignUpScreen()),
    );
  }

  Future<void> _signInWithOAuth(String provider) async {
    try {
      setState(() {
        _isLoading = true;
      });

      final authService = AuthService.to;
      final result = await authService.signInWithOAuth(provider);

      if (result.isSuccess) {
        Get.snackbar(
          'Success',
          result.message,
          backgroundColor: DesignSystem.success,
          colorText: Colors.white,
          duration: const Duration(seconds: 2),
        );

        Get.offAllNamed(AppRoutes.dashboard);
      } else {
        Get.snackbar(
          'Error',
          result.message,
          backgroundColor: DesignSystem.error,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
        );
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        'OAuth sign-in failed. Please try again.',
        backgroundColor: DesignSystem.error,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DesignSystem.neutralBackground,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(DesignSystem.spaceL),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: DesignSystem.spaceXL),

                // Header with sophisticated styling
                FadeInDown(
                  duration: DesignSystem.animationMedium,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // App logo/icon
                      Container(
                        width: 60,
                        height: 60,
                        decoration: BoxDecoration(
                          gradient: DesignSystem.primaryGradient,
                          borderRadius: BorderRadius.circular(DesignSystem.radiusLarge),
                          boxShadow: DesignSystem.shadowMedium,
                        ),
                        child: const Icon(
                          Icons.account_balance_wallet_rounded,
                          color: DesignSystem.textOnPrimary,
                          size: 30,
                        ),
                      ),

                      const SizedBox(height: DesignSystem.spaceL),

                      // Welcome text
                      ShaderMask(
                        shaderCallback: (bounds) => DesignSystem.primaryGradient.createShader(bounds),
                        child: Text(
                          'Welcome Back',
                          style: AppTypography.displayLarge.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      ),

                      const SizedBox(height: DesignSystem.spaceS),

                      Text(
                        'Sign in to continue your financial journey',
                        style: AppTypography.bodyLarge.copyWith(
                          color: DesignSystem.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: DesignSystem.spaceXL),

                // Email Field
                FadeInLeft(
                  duration: DesignSystem.animationMedium,
                  delay: const Duration(milliseconds: 200),
                  child: SophisticatedTextField(
                    controller: _emailController,
                    label: 'Email Address',
                    hint: 'Enter your email',
                    keyboardType: TextInputType.emailAddress,
                    prefixIcon: Icons.email_outlined,
                    validator: _validateEmail,
                  ),
                ),

                const SizedBox(height: DesignSystem.spaceL),

                // Password Field
                FadeInLeft(
                  duration: DesignSystem.animationMedium,
                  delay: const Duration(milliseconds: 400),
                  child: SophisticatedPasswordField(
                    controller: _passwordController,
                    label: 'Password',
                    hint: 'Enter your password',
                    validator: _validatePassword,
                  ),
                ),
                
                const SizedBox(height: DesignSystem.spaceL),

                // Remember Me & Forgot Password
                FadeInLeft(
                  duration: DesignSystem.animationMedium,
                  delay: const Duration(milliseconds: 600),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Transform.scale(
                            scale: 1.2,
                            child: Checkbox(
                              value: _rememberMe,
                              onChanged: (value) {
                                setState(() {
                                  _rememberMe = value ?? false;
                                });
                              },
                              activeColor: DesignSystem.primaryTeal,
                              checkColor: DesignSystem.textOnPrimary,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(4),
                              ),
                            ),
                          ),
                          const SizedBox(width: DesignSystem.spaceS),
                          Text(
                            'Remember me',
                            style: AppTypography.bodyMedium.copyWith(
                              color: DesignSystem.textSecondary,
                            ),
                          ),
                        ],
                      ),
                      TextButton(
                        onPressed: () {
                          // TODO: Implement forgot password
                        },
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                            horizontal: DesignSystem.spaceM,
                            vertical: DesignSystem.spaceS,
                          ),
                        ),
                        child: ShaderMask(
                          shaderCallback: (bounds) => DesignSystem.primaryGradient.createShader(bounds),
                          child: Text(
                            'Forgot Password?',
                            style: AppTypography.bodyMedium.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: DesignSystem.spaceXL),

                // Sign In Button
                FadeInUp(
                  duration: DesignSystem.animationMedium,
                  delay: const Duration(milliseconds: 800),
                  child: GradientButton(
                    text: 'Sign In',
                    onPressed: _isLoading ? null : _signIn,
                    isLoading: _isLoading,
                    height: 56,
                    textStyle: AppTypography.buttonLarge,
                  ),
                ),
                
                const SizedBox(height: DesignSystem.spaceXL),

                // Divider
                FadeInUp(
                  duration: DesignSystem.animationMedium,
                  delay: const Duration(milliseconds: 1000),
                  child: const SocialDivider(text: 'OR CONTINUE WITH'),
                ),

                const SizedBox(height: DesignSystem.spaceXL),

                // Social Login Buttons
                FadeInUp(
                  duration: DesignSystem.animationMedium,
                  delay: const Duration(milliseconds: 1200),
                  child: Row(
                    children: [
                      Expanded(
                        child: SophisticatedSocialButton(
                          provider: 'google',
                          label: 'Google',
                          onPressed: () => _signInWithOAuth('google'),
                          isLoading: _isLoading,
                        ),
                      ),
                      const SizedBox(width: DesignSystem.spaceM),
                      Expanded(
                        child: SophisticatedSocialButton(
                          provider: 'apple',
                          label: 'Apple',
                          onPressed: () => _signInWithOAuth('apple'),
                          isLoading: _isLoading,
                        ),
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: DesignSystem.spaceXL),

                // Sign Up Link
                FadeInUp(
                  duration: DesignSystem.animationMedium,
                  delay: const Duration(milliseconds: 1400),
                  child: Center(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          "Don't have an account? ",
                          style: AppTypography.bodyMedium.copyWith(
                            color: DesignSystem.textSecondary,
                          ),
                        ),
                        TextButton(
                          onPressed: _navigateToSignUp,
                          style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(
                              horizontal: DesignSystem.spaceS,
                              vertical: DesignSystem.spaceXS,
                            ),
                          ),
                          child: ShaderMask(
                            shaderCallback: (bounds) => DesignSystem.primaryGradient.createShader(bounds),
                            child: Text(
                              'Sign Up',
                              style: AppTypography.bodyMedium.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: DesignSystem.spaceL),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
