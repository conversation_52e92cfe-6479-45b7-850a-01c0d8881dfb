import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/models/business_product.dart';
import '../../../core/services/business_product_service.dart';
import '../../../core/theme/design_system.dart';
import '../../../core/theme/typography.dart';
// import '../widgets/inventory_item_card.dart';
// import '../widgets/stock_adjustment_dialog.dart';

class InventoryScreen extends StatefulWidget {
  const InventoryScreen({Key? key}) : super(key: key);

  @override
  State<InventoryScreen> createState() => _InventoryScreenState();
}

class _InventoryScreenState extends State<InventoryScreen> {
  final BusinessProductService _productService = Get.find();
  final TextEditingController _searchController = TextEditingController();
  
  ProductStatus? _selectedStatus;
  String _sortBy = 'name'; // name, stock, price
  bool _sortAscending = true;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  List<BusinessProduct> get _filteredProducts {
    var products = _productService.filterProducts(
      type: ProductType.product, // Only products have inventory
      status: _selectedStatus,
    );

    final searchTerm = _searchController.text.toLowerCase();
    if (searchTerm.isNotEmpty) {
      products = products.where((product) =>
          product.name.toLowerCase().contains(searchTerm) ||
          (product.description?.toLowerCase().contains(searchTerm) ?? false) ||
          (product.sku?.toLowerCase().contains(searchTerm) ?? false)
      ).toList();
    }

    // Sort products
    products.sort((a, b) {
      int comparison = 0;
      switch (_sortBy) {
        case 'name':
          comparison = a.name.compareTo(b.name);
          break;
        case 'stock':
          comparison = (a.quantity ?? 0).compareTo(b.quantity ?? 0);
          break;
        case 'price':
          comparison = a.price.compareTo(b.price);
          break;
      }
      return _sortAscending ? comparison : -comparison;
    });

    return products;
  }

  List<BusinessProduct> get _lowStockProducts {
    return _productService.filterProducts(type: ProductType.product)
        .where((product) => 
            product.quantity != null && 
            product.minStockLevel != null &&
            product.quantity! <= product.minStockLevel!)
        .toList();
  }

  List<BusinessProduct> get _outOfStockProducts {
    return _productService.filterProducts(type: ProductType.product)
        .where((product) => (product.quantity ?? 0) <= 0)
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DesignSystem.backgroundColor,
      appBar: AppBar(
        title: const Text('Inventory Management'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _showSortOptions,
            icon: const Icon(Icons.sort),
          ),
          IconButton(
            onPressed: _showFilterOptions,
            icon: const Icon(Icons.filter_list),
          ),
        ],
      ),
      body: Column(
        children: [
          // Search Bar
          Container(
            padding: EdgeInsets.all(DesignSystem.spacingMedium),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search products...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: DesignSystem.white,
              ),
              onChanged: (value) => setState(() {}),
            ),
          ),

          // Alerts Section
          _buildAlertsSection(),

          // Filter Chips
          if (_selectedStatus != null)
            Container(
              padding: EdgeInsets.symmetric(horizontal: DesignSystem.spacingMedium),
              height: 40,
              child: ListView(
                scrollDirection: Axis.horizontal,
                children: [
                  FilterChip(
                    label: Text(_selectedStatus!.name.toUpperCase()),
                    onDeleted: () => setState(() => _selectedStatus = null),
                    deleteIcon: const Icon(Icons.close, size: 16),
                    onSelected: (bool value) {},
                  ),
                ],
              ),
            ),

          // Products List
          Expanded(
            child: Obx(() {
              final products = _filteredProducts;
              
              if (products.isEmpty) {
                return _buildEmptyState();
              }
              
              return ListView.builder(
                padding: EdgeInsets.all(DesignSystem.spacingMedium),
                itemCount: products.length,
                itemBuilder: (context, index) {
                  final product = products[index];
                  return Padding(
                    padding: EdgeInsets.only(bottom: DesignSystem.spacingMedium),
                    child: _buildInventoryItemCard(product),
                  );
                },
              );
            }),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          Get.toNamed('/add-product', arguments: {'type': ProductType.product});
        },
        icon: const Icon(Icons.add),
        label: const Text('Add Product'),
        backgroundColor: DesignSystem.primaryTeal,
      ),
    );
  }

  Widget _buildAlertsSection() {
    return Obx(() {
      final lowStock = _lowStockProducts;
      final outOfStock = _outOfStockProducts;
      
      if (lowStock.isEmpty && outOfStock.isEmpty) {
        return const SizedBox.shrink();
      }
      
      return Container(
        margin: EdgeInsets.symmetric(horizontal: DesignSystem.spacingMedium),
        child: Column(
          children: [
            if (outOfStock.isNotEmpty)
              _buildAlertCard(
                title: 'Out of Stock',
                count: outOfStock.length,
                color: DesignSystem.secondaryCoral,
                icon: Icons.warning_rounded,
                onTap: () => _showOutOfStockProducts(),
              ),
            if (lowStock.isNotEmpty)
              SizedBox(height: DesignSystem.spacingSmall),
            if (lowStock.isNotEmpty)
              _buildAlertCard(
                title: 'Low Stock',
                count: lowStock.length,
                color: Colors.orange,
                icon: Icons.info_rounded,
                onTap: () => _showLowStockProducts(),
              ),
            SizedBox(height: DesignSystem.spacingMedium),
          ],
        ),
      );
    });
  }

  Widget _buildAlertCard({
    required String title,
    required int count,
    required Color color,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(DesignSystem.spacingMedium),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Row(
          children: [
            Icon(icon, color: color),
            SizedBox(width: DesignSystem.spacingMedium),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTypography.titleSmall.copyWith(
                      color: color,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    '$count product${count == 1 ? '' : 's'} need attention',
                    style: AppTypography.bodySmall.copyWith(
                      color: DesignSystem.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            Icon(Icons.chevron_right, color: color),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inventory_2_rounded,
            size: 64,
            color: DesignSystem.textSecondary,
          ),
          SizedBox(height: DesignSystem.spacingMedium),
          Text(
            'No products in inventory',
            style: AppTypography.titleMedium.copyWith(
              color: DesignSystem.textSecondary,
            ),
          ),
          SizedBox(height: DesignSystem.spacingSmall),
          Text(
            'Add products to start managing inventory',
            style: AppTypography.bodyMedium.copyWith(
              color: DesignSystem.textSecondary,
            ),
          ),
          SizedBox(height: DesignSystem.spacingLarge),
          ElevatedButton.icon(
            onPressed: () {
              Get.toNamed('/add-product', arguments: {'type': ProductType.product});
            },
            icon: const Icon(Icons.add),
            label: const Text('Add Product'),
            style: ElevatedButton.styleFrom(
              backgroundColor: DesignSystem.primaryTeal,
              foregroundColor: DesignSystem.white,
            ),
          ),
        ],
      ),
    );
  }

  void _showSortOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: EdgeInsets.all(DesignSystem.spacingLarge),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Sort By',
              style: AppTypography.titleMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: DesignSystem.spacingMedium),
            ...['name', 'stock', 'price'].map((option) {
              return ListTile(
                title: Text(option.capitalize!),
                leading: Radio<String>(
                  value: option,
                  groupValue: _sortBy,
                  onChanged: (value) {
                    setState(() {
                      _sortBy = value!;
                    });
                    Navigator.pop(context);
                  },
                ),
                trailing: option == _sortBy
                    ? IconButton(
                        onPressed: () {
                          setState(() {
                            _sortAscending = !_sortAscending;
                          });
                          Navigator.pop(context);
                        },
                        icon: Icon(
                          _sortAscending 
                              ? Icons.arrow_upward 
                              : Icons.arrow_downward,
                        ),
                      )
                    : null,
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  void _showFilterOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: EdgeInsets.all(DesignSystem.spacingLarge),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Filter By Status',
              style: AppTypography.titleMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: DesignSystem.spacingMedium),
            ...ProductStatus.values.map((status) {
              return ListTile(
                title: Text(status.name.capitalize!),
                leading: Radio<ProductStatus?>(
                  value: status,
                  groupValue: _selectedStatus,
                  onChanged: (value) {
                    setState(() {
                      _selectedStatus = value;
                    });
                    Navigator.pop(context);
                  },
                ),
              );
            }).toList(),
            ListTile(
              title: const Text('All'),
              leading: Radio<ProductStatus?>(
                value: null,
                groupValue: _selectedStatus,
                onChanged: (value) {
                  setState(() {
                    _selectedStatus = null;
                  });
                  Navigator.pop(context);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInventoryItemCard(BusinessProduct product) {
    final isLowStock = product.minStockLevel != null &&
        (product.quantity ?? 0) <= product.minStockLevel!;
    final isOutOfStock = (product.quantity ?? 0) <= 0;

    return Container(
      padding: EdgeInsets.all(DesignSystem.spacingMedium),
      decoration: BoxDecoration(
        color: DesignSystem.white,
        borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
        boxShadow: DesignSystem.shadowSmall,
        border: isOutOfStock
            ? Border.all(color: DesignSystem.secondaryCoral.withOpacity(0.3))
            : isLowStock
                ? Border.all(color: Colors.orange.withOpacity(0.3))
                : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      product.name,
                      style: AppTypography.titleMedium.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    if (product.sku != null) ...[
                      SizedBox(height: DesignSystem.spacingSmall),
                      Text(
                        'SKU: ${product.sku}',
                        style: AppTypography.bodySmall.copyWith(
                          color: DesignSystem.textSecondary,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              PopupMenuButton<String>(
                onSelected: (value) {
                  switch (value) {
                    case 'adjust':
                      _showStockAdjustmentDialog(product);
                      break;
                    case 'edit':
                      _editProduct(product);
                      break;
                  }
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'adjust',
                    child: Text('Adjust Stock'),
                  ),
                  const PopupMenuItem(
                    value: 'edit',
                    child: Text('Edit Product'),
                  ),
                ],
              ),
            ],
          ),
          SizedBox(height: DesignSystem.spacingMedium),
          Row(
            children: [
              Expanded(
                child: _buildStockInfo('Current Stock', '${product.quantity ?? 0}',
                    isOutOfStock ? DesignSystem.secondaryCoral :
                    isLowStock ? Colors.orange : DesignSystem.success),
              ),
              if (product.minStockLevel != null)
                Expanded(
                  child: _buildStockInfo('Min Stock', '${product.minStockLevel}',
                      DesignSystem.textSecondary),
                ),
              Expanded(
                child: _buildStockInfo('Price', '\$${product.price.toStringAsFixed(2)}',
                    DesignSystem.primaryTeal),
              ),
            ],
          ),
          if (isOutOfStock || isLowStock) ...[
            SizedBox(height: DesignSystem.spacingMedium),
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: DesignSystem.spacingMedium,
                vertical: DesignSystem.spacingSmall,
              ),
              decoration: BoxDecoration(
                color: (isOutOfStock ? DesignSystem.secondaryCoral : Colors.orange)
                    .withOpacity(0.1),
                borderRadius: BorderRadius.circular(DesignSystem.radiusSmall),
              ),
              child: Row(
                children: [
                  Icon(
                    isOutOfStock ? Icons.warning_rounded : Icons.info_rounded,
                    size: 16,
                    color: isOutOfStock ? DesignSystem.secondaryCoral : Colors.orange,
                  ),
                  SizedBox(width: DesignSystem.spacingSmall),
                  Text(
                    isOutOfStock ? 'Out of Stock' : 'Low Stock Alert',
                    style: AppTypography.bodySmall.copyWith(
                      color: isOutOfStock ? DesignSystem.secondaryCoral : Colors.orange,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStockInfo(String label, String value, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTypography.bodySmall.copyWith(
            color: DesignSystem.textSecondary,
          ),
        ),
        SizedBox(height: DesignSystem.spacingSmall),
        Text(
          value,
          style: AppTypography.titleMedium.copyWith(
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  void _showStockAdjustmentDialog(BusinessProduct product) {
    showDialog(
      context: context,
      builder: (context) => _buildStockAdjustmentDialog(product),
    );
  }

  Widget _buildStockAdjustmentDialog(BusinessProduct product) {
    final adjustmentController = TextEditingController();
    final reasonController = TextEditingController();
    bool isLoading = false;

    return StatefulBuilder(
      builder: (context, setDialogState) {
        return AlertDialog(
          title: Text('Adjust Stock - ${product.name}'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Current Stock: ${product.quantity ?? 0}'),
              SizedBox(height: DesignSystem.spacingMedium),
              TextField(
                controller: adjustmentController,
                keyboardType: const TextInputType.numberWithOptions(signed: true),
                decoration: const InputDecoration(
                  labelText: 'Adjustment (+/-)',
                  hintText: 'e.g., +10 or -5',
                ),
              ),
              SizedBox(height: DesignSystem.spacingMedium),
              TextField(
                controller: reasonController,
                decoration: const InputDecoration(
                  labelText: 'Reason (Optional)',
                  hintText: 'Stock adjustment reason',
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: isLoading ? null : () async {
                final adjustmentText = adjustmentController.text.trim();
                if (adjustmentText.isEmpty) return;

                final adjustment = int.tryParse(adjustmentText);
                if (adjustment == null) return;

                setDialogState(() {
                  isLoading = true;
                });

                try {
                  await _productService.adjustStock(product.id, adjustment);
                  Navigator.pop(context);
                  setState(() {}); // Refresh the list

                  Get.snackbar(
                    'Stock Updated',
                    'Stock adjusted by $adjustment for ${product.name}',
                    backgroundColor: DesignSystem.success,
                    colorText: DesignSystem.white,
                  );
                } catch (e) {
                  Get.snackbar(
                    'Error',
                    'Failed to adjust stock: ${e.toString()}',
                    backgroundColor: DesignSystem.secondaryCoral,
                    colorText: DesignSystem.white,
                  );
                } finally {
                  setDialogState(() {
                    isLoading = false;
                  });
                }
              },
              child: isLoading
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Text('Update'),
            ),
          ],
        );
      },
    );
  }

  void _showOutOfStockProducts() {
    // Navigate to filtered view or show dialog
    setState(() {
      _selectedStatus = ProductStatus.outOfStock;
    });
  }

  void _showLowStockProducts() {
    // Show low stock products in a dialog or filtered view
    Get.dialog(
      AlertDialog(
        title: const Text('Low Stock Products'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: _lowStockProducts.length,
            itemBuilder: (context, index) {
              final product = _lowStockProducts[index];
              return ListTile(
                title: Text(product.name),
                subtitle: Text('Stock: ${product.quantity} / Min: ${product.minStockLevel}'),
                trailing: TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                    _showStockAdjustmentDialog(product);
                  },
                  child: const Text('Adjust'),
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _editProduct(BusinessProduct product) {
    Get.toNamed('/edit-product', arguments: {'product': product});
  }
}
