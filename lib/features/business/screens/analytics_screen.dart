import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/models/business_product.dart';
import '../../../core/services/business_product_service.dart';
import '../../../core/services/currency_service.dart';
import '../../../core/theme/design_system.dart';
import '../../../core/theme/typography.dart';

class AnalyticsScreen extends StatefulWidget {
  const AnalyticsScreen({Key? key}) : super(key: key);

  @override
  State<AnalyticsScreen> createState() => _AnalyticsScreenState();
}

class _AnalyticsScreenState extends State<AnalyticsScreen>
    with TickerProviderStateMixin {
  final BusinessProductService _productService = Get.find();
  late TabController _tabController;
  
  int _selectedDays = 30; // Default to last 30 days
  final List<int> _dayOptions = [7, 30, 90, 365];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DesignSystem.backgroundColor,
      appBar: AppBar(
        title: const Text('Business Analytics'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Hot Selling'),
            Tab(text: 'Revenue'),
            Tab(text: 'Overview'),
          ],
        ),
        actions: [
          PopupMenuButton<int>(
            onSelected: (days) {
              setState(() {
                _selectedDays = days;
              });
            },
            itemBuilder: (context) => _dayOptions.map((days) {
              return PopupMenuItem(
                value: days,
                child: Text('Last $days days'),
              );
            }).toList(),
            child: Padding(
              padding: EdgeInsets.all(DesignSystem.spacingMedium),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Last $_selectedDays days',
                    style: AppTypography.bodySmall,
                  ),
                  const Icon(Icons.arrow_drop_down),
                ],
              ),
            ),
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildHotSellingTab(),
          _buildRevenueTab(),
          _buildOverviewTab(),
        ],
      ),
    );
  }

  Widget _buildHotSellingTab() {
    return Obx(() {
      final hotProducts = _productService.getHotSellingProducts(
        limit: 20,
        days: _selectedDays,
      );

      if (hotProducts.isEmpty) {
        return _buildEmptyState(
          icon: Icons.trending_up,
          title: 'No Sales Data',
          subtitle: 'Start selling products to see analytics',
        );
      }

      return ListView.builder(
        padding: EdgeInsets.all(DesignSystem.spacingMedium),
        itemCount: hotProducts.length,
        itemBuilder: (context, index) {
          final item = hotProducts[index];
          final product = item['product'] as BusinessProduct;
          final quantity = item['totalQuantity'] as int;
          final revenue = item['totalRevenue'] as double;
          final transactionCount = item['transactionCount'] as int;

          return _buildAnalyticsCard(
            rank: index + 1,
            product: product,
            primaryMetric: '$quantity sold',
            secondaryMetric: 'Revenue: ${CurrencyService.to.currentCurrency.value.formatAmount(revenue)}',
            tertiaryMetric: '$transactionCount transactions',
            color: _getRankColor(index),
          );
        },
      );
    });
  }

  Widget _buildRevenueTab() {
    return Obx(() {
      final topRevenue = _productService.getTopRevenueProducts(
        limit: 20,
        days: _selectedDays,
      );

      if (topRevenue.isEmpty) {
        return _buildEmptyState(
          icon: Icons.attach_money,
          title: 'No Revenue Data',
          subtitle: 'Start selling products to see revenue analytics',
        );
      }

      return ListView.builder(
        padding: EdgeInsets.all(DesignSystem.spacingMedium),
        itemCount: topRevenue.length,
        itemBuilder: (context, index) {
          final item = topRevenue[index];
          final product = item['product'] as BusinessProduct;
          final revenue = item['totalRevenue'] as double;
          final quantity = item['totalQuantity'] as int;
          final transactionCount = item['transactionCount'] as int;

          return _buildAnalyticsCard(
            rank: index + 1,
            product: product,
            primaryMetric: CurrencyService.to.currentCurrency.value.formatAmount(revenue),
            secondaryMetric: '$quantity units sold',
            tertiaryMetric: '$transactionCount transactions',
            color: _getRankColor(index),
          );
        },
      );
    });
  }

  Widget _buildOverviewTab() {
    return Obx(() {
      final analytics = _productService.getOverallAnalytics(days: _selectedDays);
      
      return SingleChildScrollView(
        padding: EdgeInsets.all(DesignSystem.spacingMedium),
        child: Column(
          children: [
            _buildOverviewCard(
              title: 'Sales Overview',
              items: [
                _buildOverviewItem('Total Sales', '${analytics['totalSales']}'),
                _buildOverviewItem('Sales Revenue', 
                    CurrencyService.to.currentCurrency.value.formatAmount(analytics['totalSalesRevenue'])),
                _buildOverviewItem('Average Sale', 
                    CurrencyService.to.currentCurrency.value.formatAmount(analytics['averageSaleValue'])),
                _buildOverviewItem('Products Sold', '${analytics['uniqueProductsSold']}'),
              ],
            ),
            SizedBox(height: DesignSystem.spacingMedium),
            _buildOverviewCard(
              title: 'Purchase Overview',
              items: [
                _buildOverviewItem('Total Purchases', '${analytics['totalPurchases']}'),
                _buildOverviewItem('Purchase Cost', 
                    CurrencyService.to.currentCurrency.value.formatAmount(analytics['totalPurchasesCost'])),
                _buildOverviewItem('Average Purchase', 
                    CurrencyService.to.currentCurrency.value.formatAmount(analytics['averagePurchaseValue'])),
                _buildOverviewItem('Products Purchased', '${analytics['uniqueProductsPurchased']}'),
              ],
            ),
            SizedBox(height: DesignSystem.spacingMedium),
            _buildOverviewCard(
              title: 'Profitability',
              items: [
                _buildOverviewItem('Gross Profit', 
                    CurrencyService.to.currentCurrency.value.formatAmount(analytics['grossProfit']),
                    color: analytics['grossProfit'] >= 0 ? DesignSystem.success : DesignSystem.secondaryCoral),
              ],
            ),
          ],
        ),
      );
    });
  }

  Widget _buildAnalyticsCard({
    required int rank,
    required BusinessProduct product,
    required String primaryMetric,
    required String secondaryMetric,
    required String tertiaryMetric,
    required Color color,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: DesignSystem.spacingMedium),
      padding: EdgeInsets.all(DesignSystem.spacingMedium),
      decoration: BoxDecoration(
        color: DesignSystem.white,
        borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
        boxShadow: DesignSystem.shadowSmall,
      ),
      child: Row(
        children: [
          // Rank Badge
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                '$rank',
                style: AppTypography.titleMedium.copyWith(
                  color: DesignSystem.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          SizedBox(width: DesignSystem.spacingMedium),
          
          // Product Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  product.name,
                  style: AppTypography.titleMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: DesignSystem.spacingSmall),
                Text(
                  primaryMetric,
                  style: AppTypography.titleSmall.copyWith(
                    color: color,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  secondaryMetric,
                  style: AppTypography.bodySmall.copyWith(
                    color: DesignSystem.textSecondary,
                  ),
                ),
                Text(
                  tertiaryMetric,
                  style: AppTypography.bodySmall.copyWith(
                    color: DesignSystem.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          
          // Product Type Icon
          Icon(
            product.isProduct 
                ? Icons.inventory_2_rounded
                : Icons.design_services_rounded,
            color: DesignSystem.textSecondary,
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewCard({
    required String title,
    required List<Widget> items,
  }) {
    return Container(
      padding: EdgeInsets.all(DesignSystem.spacingMedium),
      decoration: BoxDecoration(
        color: DesignSystem.white,
        borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
        boxShadow: DesignSystem.shadowSmall,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: AppTypography.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: DesignSystem.spacingMedium),
          ...items,
        ],
      ),
    );
  }

  Widget _buildOverviewItem(String label, String value, {Color? color}) {
    return Padding(
      padding: EdgeInsets.only(bottom: DesignSystem.spacingSmall),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: AppTypography.bodyMedium.copyWith(
              color: DesignSystem.textSecondary,
            ),
          ),
          Text(
            value,
            style: AppTypography.titleSmall.copyWith(
              color: color ?? DesignSystem.textPrimary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: DesignSystem.textSecondary,
          ),
          SizedBox(height: DesignSystem.spacingMedium),
          Text(
            title,
            style: AppTypography.titleMedium.copyWith(
              color: DesignSystem.textSecondary,
            ),
          ),
          SizedBox(height: DesignSystem.spacingSmall),
          Text(
            subtitle,
            style: AppTypography.bodyMedium.copyWith(
              color: DesignSystem.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Color _getRankColor(int index) {
    switch (index) {
      case 0:
        return Colors.amber; // Gold
      case 1:
        return Colors.grey; // Silver
      case 2:
        return Colors.brown; // Bronze
      default:
        return DesignSystem.primaryTeal;
    }
  }
}
