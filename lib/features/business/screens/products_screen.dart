import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/models/business_product.dart';
import '../../../core/services/business_product_service.dart';
import '../../../core/theme/design_system.dart';
import '../widgets/product_card.dart';
import '../widgets/add_product_dialog.dart';

class ProductsScreen extends StatefulWidget {
  const ProductsScreen({Key? key}) : super(key: key);

  @override
  State<ProductsScreen> createState() => _ProductsScreenState();
}

class _ProductsScreenState extends State<ProductsScreen>
    with TickerProviderStateMixin {
  final BusinessProductService _productService = Get.find();
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();

  ProductType _selectedType = ProductType.product;
  ProductStatus? _selectedStatus;
  String? _selectedCategory;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(() {
      setState(() {
        _selectedType = _tabController.index == 0 ? ProductType.product : ProductType.service;
      });
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  List<BusinessProduct> get _filteredProducts {
    var products = _productService.filterProducts(
      type: _selectedType,
      status: _selectedStatus,
      category: _selectedCategory,
    );

    if (_searchController.text.isNotEmpty) {
      products = _productService.searchProducts(_searchController.text)
          .where((p) => p.type == _selectedType)
          .toList();
    }

    return products;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DesignSystem.backgroundColor,
      appBar: AppBar(
        title: const Text('Products & Services'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Products'),
            Tab(text: 'Services'),
          ],
        ),
        actions: [
          IconButton(
            onPressed: _showFilterDialog,
            icon: const Icon(Icons.filter_list),
          ),
        ],
      ),
      body: Column(
        children: [
          // Search Bar
          Container(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search ${_selectedType.name}s...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.white,
              ),
              onChanged: (value) => setState(() {}),
            ),
          ),
          // Filter Chips
          if (_selectedStatus != null || _selectedCategory != null)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              height: 40,
              child: ListView(
                scrollDirection: Axis.horizontal,
                children: [
                  if (_selectedStatus != null)
                    Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: FilterChip(
                        label: Text(_selectedStatus!.name.toUpperCase()),
                        onDeleted: () => setState(() => _selectedStatus = null),
                        deleteIcon: const Icon(Icons.close, size: 16),
                        onSelected: (bool value) {  },
                      ),
                    ),
                  if (_selectedCategory != null)
                    FilterChip(
                      label: Text(_selectedCategory!),
                      onDeleted: () => setState(() => _selectedCategory = null),
                      deleteIcon: const Icon(Icons.close, size: 16),
                      onSelected: (bool value) {  },
                    ),
                ],
              ),
            ),
          // Products List
          Expanded(
            child: Obx(() {
              final products = _filteredProducts;
              
              if (products.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        _selectedType == ProductType.product 
                            ? Icons.inventory_2_outlined 
                            : Icons.design_services_outlined,
                        size: 64,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'No ${_selectedType.name}s found',
                        style: Get.theme.textTheme.titleMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Add your first ${_selectedType.name} to get started',
                        style: Get.theme.textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[500],
                        ),
                      ),
                      const SizedBox(height: 24),
                      ElevatedButton.icon(
                        onPressed: _showAddProductDialog,
                        icon: const Icon(Icons.add),
                        label: Text('Add ${_selectedType.name.capitalize}'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: DesignSystem.primaryColor,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }

              return ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: products.length,
                itemBuilder: (context, index) {
                  final product = products[index];
                  return ProductCard(
                    product: product,
                    onTap: () => _showProductDetails(product),
                    onEdit: () => _showEditProductDialog(product),
                    onDelete: () => _showDeleteConfirmation(product),
                  );
                },
              );
            }),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddProductDialog,
        backgroundColor: DesignSystem.primaryColor,
        heroTag: "products_fab",
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  void _showAddProductDialog() {
    AddProductDialog.show(type: _selectedType);
  }

  void _showEditProductDialog(BusinessProduct product) {
    AddProductDialog.show(product: product);
  }

  void _showProductDetails(BusinessProduct product) {
    Get.toNamed('/product-details', arguments: product);
  }

  void _showDeleteConfirmation(BusinessProduct product) {
    Get.dialog(
      AlertDialog(
        title: const Text('Delete Product'),
        content: Text('Are you sure you want to delete "${product.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              _productService.deleteProduct(product.id);
              Get.back();
              Get.snackbar(
                'Success',
                'Product deleted successfully',
                snackPosition: SnackPosition.BOTTOM,
                backgroundColor: DesignSystem.successColor,
                colorText: Colors.white,
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: DesignSystem.errorColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog() {
    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(24),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Filter ${_selectedType.name.capitalize}s',
                  style: Get.theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                IconButton(
                  onPressed: () => Get.back(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 20),
            // Status Filter
            Text(
              'Status',
              style: Get.theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: ProductStatus.values.map((status) {
                return FilterChip(
                  label: Text(status.name.toUpperCase()),
                  selected: _selectedStatus == status,
                  onSelected: (selected) {
                    setState(() {
                      _selectedStatus = selected ? status : null;
                    });
                    Get.back();
                  },
                );
              }).toList(),
            ),
            const SizedBox(height: 20),
            // Category Filter
            Text(
              'Category',
              style: Get.theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: _productService.getCategories().map((category) {
                return FilterChip(
                  label: Text(category),
                  selected: _selectedCategory == category,
                  onSelected: (selected) {
                    setState(() {
                      _selectedCategory = selected ? category : null;
                    });
                    Get.back();
                  },
                );
              }).toList(),
            ),
            const SizedBox(height: 20),
            // Clear Filters
            SizedBox(
              width: double.infinity,
              child: TextButton(
                onPressed: () {
                  setState(() {
                    _selectedStatus = null;
                    _selectedCategory = null;
                  });
                  Get.back();
                },
                child: const Text('Clear All Filters'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
