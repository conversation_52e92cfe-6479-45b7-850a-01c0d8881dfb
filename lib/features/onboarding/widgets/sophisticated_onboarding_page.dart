import 'package:flutter/material.dart';
import 'package:animate_do/animate_do.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../../../core/theme/design_system.dart';
import '../../../core/theme/typography.dart';
import '../screens/onboarding_screen.dart';

/// Sophisticated Onboarding Page Widget
/// Beautiful page with custom illustrations and elegant animations
class SophisticatedOnboardingPage extends StatelessWidget {
  final SophisticatedOnboardingData data;
  final int pageIndex;

  const SophisticatedOnboardingPage({
    super.key,
    required this.data,
    required this.pageIndex,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: DesignSystem.spaceL),
      child: Column(
        children: [
          const SizedBox(height: DesignSystem.spaceXL),
          
          // Custom Illustration with Animation
          Expanded(
            flex: 3,
            child: AnimationLimiter(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: AnimationConfiguration.toStaggeredList(
                  duration: DesignSystem.animationSlow,
                  childAnimationBuilder: (widget) => SlideAnimation(
                    verticalOffset: 50.0,
                    child: FadeInAnimation(child: widget),
                  ),
                  children: [
                    // Main Illustration
                    Hero(
                      tag: 'onboarding_illustration_$pageIndex',
                      child: Container(
                        width: 240,
                        height: 240,
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            // Background glow effect
                            Container(
                              width: 260,
                              height: 260,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                boxShadow: [
                                  BoxShadow(
                                    color: _getGradientColor(data.gradient).withOpacity(0.2),
                                    blurRadius: 40,
                                    spreadRadius: 10,
                                  ),
                                ],
                              ),
                            ),
                            // Main illustration
                            data.illustration,
                          ],
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: DesignSystem.spaceXL),
                    
                    // Floating elements around illustration
                    _buildFloatingElements(),
                  ],
                ),
              ),
            ),
          ),
          
          // Content Section
          Expanded(
            flex: 2,
            child: Column(
              children: [
                // Subtitle
                FadeInUp(
                  duration: DesignSystem.animationSlow,
                  delay: Duration(milliseconds: 300 + (pageIndex * 100)),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: DesignSystem.spaceM,
                      vertical: DesignSystem.spaceS,
                    ),
                    decoration: BoxDecoration(
                      gradient: data.gradient,
                      borderRadius: BorderRadius.circular(DesignSystem.radiusLarge),
                    ),
                    child: Text(
                      data.subtitle,
                      style: AppTypography.labelMedium.copyWith(
                        color: DesignSystem.textOnPrimary,
                        fontWeight: FontWeight.w600,
                        letterSpacing: 1.2,
                      ),
                    ),
                  ),
                ),
                
                const SizedBox(height: DesignSystem.spaceL),
                
                // Main Title
                FadeInUp(
                  duration: DesignSystem.animationSlow,
                  delay: Duration(milliseconds: 500 + (pageIndex * 100)),
                  child: ShaderMask(
                    shaderCallback: (bounds) => data.gradient.createShader(bounds),
                    child: Text(
                      data.title,
                      textAlign: TextAlign.center,
                      style: AppTypography.displayMedium.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w700,
                        height: 1.2,
                      ),
                    ),
                  ),
                ),
                
                const SizedBox(height: DesignSystem.spaceL),
                
                // Description
                FadeInUp(
                  duration: DesignSystem.animationSlow,
                  delay: Duration(milliseconds: 700 + (pageIndex * 100)),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: DesignSystem.spaceM),
                    child: Text(
                      data.description,
                      textAlign: TextAlign.center,
                      style: AppTypography.bodyLarge.copyWith(
                        color: DesignSystem.textSecondary,
                        height: 1.6,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ),
                ),
                
                const SizedBox(height: DesignSystem.spaceXL),
                
                // Feature highlights
                FadeInUp(
                  duration: DesignSystem.animationSlow,
                  delay: Duration(milliseconds: 900 + (pageIndex * 100)),
                  child: _buildFeatureHighlights(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build floating decorative elements
  Widget _buildFloatingElements() {
    return SizedBox(
      width: 300,
      height: 100,
      child: Stack(
        children: [
          // Floating element 1
          Positioned(
            top: 10,
            left: 20,
            child: FadeInLeft(
              duration: DesignSystem.animationSlow,
              delay: Duration(milliseconds: 1000 + (pageIndex * 100)),
              child: _buildFloatingElement(
                icon: Icons.trending_up_rounded,
                color: _getGradientColor(data.gradient),
                size: 24,
              ),
            ),
          ),
          
          // Floating element 2
          Positioned(
            top: 40,
            right: 30,
            child: FadeInRight(
              duration: DesignSystem.animationSlow,
              delay: Duration(milliseconds: 1200 + (pageIndex * 100)),
              child: _buildFloatingElement(
                icon: Icons.star_rounded,
                color: _getGradientColor(data.gradient).withOpacity(0.7),
                size: 20,
              ),
            ),
          ),
          
          // Floating element 3
          Positioned(
            bottom: 10,
            left: 60,
            child: FadeInUp(
              duration: DesignSystem.animationSlow,
              delay: Duration(milliseconds: 1400 + (pageIndex * 100)),
              child: _buildFloatingElement(
                icon: Icons.auto_awesome_rounded,
                color: _getGradientColor(data.gradient).withOpacity(0.5),
                size: 18,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build individual floating element
  Widget _buildFloatingElement({
    required IconData icon,
    required Color color,
    required double size,
  }) {
    return Container(
      width: size + 16,
      height: size + 16,
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular((size + 16) / 2),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Icon(
        icon,
        size: size,
        color: color,
      ),
    );
  }

  /// Build feature highlights based on page
  Widget _buildFeatureHighlights() {
    List<String> features;
    
    switch (pageIndex) {
      case 0:
        features = ['Real-time tracking', 'Smart categorization', 'Budget insights'];
        break;
      case 1:
        features = ['SMS parsing', 'Auto-detection', 'ML-powered'];
        break;
      case 2:
        features = ['Beautiful charts', 'Spending patterns', 'Custom reports'];
        break;
      case 3:
        features = ['End-to-end encryption', 'Biometric auth', 'Secure sync'];
        break;
      default:
        features = ['Feature 1', 'Feature 2', 'Feature 3'];
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: features.map((feature) => _buildFeatureChip(feature)).toList(),
    );
  }

  /// Build individual feature chip
  Widget _buildFeatureChip(String feature) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: DesignSystem.spaceM,
        vertical: DesignSystem.spaceS,
      ),
      decoration: BoxDecoration(
        color: _getGradientColor(data.gradient).withOpacity(0.1),
        borderRadius: BorderRadius.circular(DesignSystem.radiusLarge),
        border: Border.all(
          color: _getGradientColor(data.gradient).withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Text(
        feature,
        style: AppTypography.labelSmall.copyWith(
          color: _getGradientColor(data.gradient),
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  /// Extract primary color from gradient
  Color _getGradientColor(LinearGradient gradient) {
    return gradient.colors.first;
  }
}
