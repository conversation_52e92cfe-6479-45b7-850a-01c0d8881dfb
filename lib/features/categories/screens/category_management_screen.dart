import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/models/transaction.dart';

import '../widgets/category_card.dart';
import '../widgets/add_category_modal.dart';

class CategoryManagementScreen extends StatefulWidget {
  const CategoryManagementScreen({super.key});

  @override
  State<CategoryManagementScreen> createState() => _CategoryManagementScreenState();
}

class _CategoryManagementScreenState extends State<CategoryManagementScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  
  final List<TransactionCategory> _incomeCategories = [
    TransactionCategory.salary,
    TransactionCategory.freelance,
    TransactionCategory.investment_income,
    TransactionCategory.business,
    TransactionCategory.rental,
    TransactionCategory.bonus,
    TransactionCategory.gift,
    TransactionCategory.other_income,
  ];

  final List<TransactionCategory> _expenseCategories = [
    TransactionCategory.food,
    TransactionCategory.transportation,
    TransactionCategory.housing,
    TransactionCategory.utilities,
    TransactionCategory.healthcare,
    TransactionCategory.entertainment,
    TransactionCategory.shopping,
    TransactionCategory.education,
    TransactionCategory.travel,
    TransactionCategory.insurance,
    TransactionCategory.debt,
    TransactionCategory.savings,
    TransactionCategory.charity,
    TransactionCategory.business_expense,
    TransactionCategory.other_expense,
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'Categories',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        actions: [
          IconButton(
            onPressed: _showAddCategoryModal,
            icon: const Icon(Icons.add_rounded),
            tooltip: 'Add Category',
            style: IconButton.styleFrom(
              backgroundColor: AppColors.primary.withOpacity(0.1),
              foregroundColor: AppColors.primary,
            ),
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: Column(
        children: [
          // Tab Bar
          Container(
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: TabBar(
              controller: _tabController,
              indicator: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                gradient: LinearGradient(
                  colors: [AppColors.primary, AppColors.secondary],
                ),
              ),
              indicatorSize: TabBarIndicatorSize.tab,
              indicatorPadding: const EdgeInsets.all(4),
              labelColor: Colors.white,
              unselectedLabelColor: Colors.grey[600],
              labelStyle: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
              unselectedLabelStyle: const TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 16,
              ),
              tabs: const [
                Tab(
                  icon: Icon(Icons.trending_up_rounded),
                  text: 'Income',
                ),
                Tab(
                  icon: Icon(Icons.trending_down_rounded),
                  text: 'Expense',
                ),
              ],
            ),
          ),
          
          // Tab Content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildCategoryGrid(_incomeCategories, TransactionType.income),
                _buildCategoryGrid(_expenseCategories, TransactionType.expense),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryGrid(List<TransactionCategory> categories, TransactionType type) {
    return AnimationLimiter(
      child: GridView.builder(
        padding: const EdgeInsets.all(16),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 1.2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
        ),
        itemCount: categories.length,
        itemBuilder: (context, index) {
          return AnimationConfiguration.staggeredGrid(
            position: index,
            duration: const Duration(milliseconds: 375),
            columnCount: 2,
            child: SlideAnimation(
              verticalOffset: 50.0,
              child: FadeInAnimation(
                child: CategoryCard(
                  category: categories[index],
                  type: type,
                  onTap: () => _showCategoryDetails(categories[index], type),
                  onEdit: () => _editCategory(categories[index], type),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  void _showAddCategoryModal() {
    HapticFeedback.lightImpact();
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AddCategoryModal(
        type: _tabController.index == 0 
            ? TransactionType.income 
            : TransactionType.expense,
      ),
    );
  }

  void _showCategoryDetails(TransactionCategory category, TransactionType type) {
    HapticFeedback.lightImpact();
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildCategoryDetailsModal(category, type),
    );
  }

  void _editCategory(TransactionCategory category, TransactionType type) {
    HapticFeedback.lightImpact();
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AddCategoryModal(
        type: type,
        editingCategory: category,
      ),
    );
  }

  Widget _buildCategoryDetailsModal(TransactionCategory category, TransactionType type) {
    return DraggableScrollableSheet(
      initialChildSize: 0.6,
      minChildSize: 0.4,
      maxChildSize: 0.8,
      builder: (context, scrollController) {
        return Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                margin: const EdgeInsets.only(top: 12),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              
              // Header
              Padding(
                padding: const EdgeInsets.all(24),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: type == TransactionType.income
                            ? AppColors.success.withOpacity(0.1)
                            : AppColors.error.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Icon(
                        _getCategoryIcon(category),
                        size: 32,
                        color: type == TransactionType.income
                            ? AppColors.success
                            : AppColors.error,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            category.displayName,
                            style: const TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: AppColors.textPrimary,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            '${type.name.capitalize} Category',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      onPressed: () => _editCategory(category, type),
                      icon: const Icon(Icons.edit_rounded),
                      style: IconButton.styleFrom(
                        backgroundColor: Colors.grey[100],
                        foregroundColor: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              
              // Content
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildStatCard(
                        'This Month',
                        '\$1,234.56',
                        Icons.calendar_month_rounded,
                        AppColors.primary,
                      ),
                      const SizedBox(height: 16),
                      _buildStatCard(
                        'Last Month',
                        '\$987.65',
                        Icons.history_rounded,
                        AppColors.secondary,
                      ),
                      const SizedBox(height: 16),
                      _buildStatCard(
                        'Total Transactions',
                        '42',
                        Icons.receipt_long_rounded,
                        AppColors.accent,
                      ),
                      const SizedBox(height: 24),
                      
                      // Action Buttons
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: () {
                                Navigator.pop(context);
                                // Navigate to transactions filtered by this category
                              },
                              icon: const Icon(Icons.list_rounded),
                              label: const Text('View Transactions'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppColors.primary,
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(vertical: 16),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: OutlinedButton.icon(
                              onPressed: () {
                                Navigator.pop(context);
                                // Navigate to add transaction with this category
                              },
                              icon: const Icon(Icons.add_rounded),
                              label: const Text('Add Transaction'),
                              style: OutlinedButton.styleFrom(
                                foregroundColor: AppColors.primary,
                                side: const BorderSide(color: AppColors.primary),
                                padding: const EdgeInsets.symmetric(vertical: 16),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withOpacity(0.2)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              size: 24,
              color: color,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  IconData _getCategoryIcon(TransactionCategory category) {
    switch (category) {
      case TransactionCategory.food:
        return Icons.restaurant_rounded;
      case TransactionCategory.transportation:
        return Icons.directions_car_rounded;
      case TransactionCategory.shopping:
        return Icons.shopping_bag_rounded;
      case TransactionCategory.entertainment:
        return Icons.movie_rounded;
      case TransactionCategory.healthcare:
        return Icons.local_hospital_rounded;
      case TransactionCategory.education:
        return Icons.school_rounded;
      case TransactionCategory.utilities:
        return Icons.receipt_long_rounded;
      case TransactionCategory.housing:
        return Icons.home_rounded;
      case TransactionCategory.travel:
        return Icons.flight_rounded;
      case TransactionCategory.salary:
        return Icons.work_rounded;
      case TransactionCategory.freelance:
        return Icons.laptop_rounded;
      case TransactionCategory.investment_income:
        return Icons.trending_up_rounded;
      case TransactionCategory.business:
        return Icons.business_rounded;
      case TransactionCategory.rental:
        return Icons.apartment_rounded;
      case TransactionCategory.bonus:
        return Icons.card_giftcard_rounded;
      case TransactionCategory.gift:
        return Icons.redeem_rounded;
      case TransactionCategory.insurance:
        return Icons.security_rounded;
      case TransactionCategory.debt:
        return Icons.credit_card_rounded;
      case TransactionCategory.savings:
        return Icons.savings_rounded;
      case TransactionCategory.charity:
        return Icons.volunteer_activism_rounded;
      case TransactionCategory.business_expense:
        return Icons.business_center_rounded;
      case TransactionCategory.other_income:
      case TransactionCategory.other_expense:
        return Icons.category_rounded;
      default:
        return Icons.category_rounded;
    }
  }
}
