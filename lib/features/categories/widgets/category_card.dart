import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/models/transaction.dart';

class CategoryCard extends StatefulWidget {
  final TransactionCategory category;
  final TransactionType type;
  final VoidCallback onTap;
  final VoidCallback onEdit;

  const CategoryCard({
    super.key,
    required this.category,
    required this.type,
    required this.onTap,
    required this.onEdit,
  });

  @override
  State<CategoryCard> createState() => _CategoryCardState();
}

class _CategoryCardState extends State<CategoryCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final color = widget.type == TransactionType.income
        ? AppColors.success
        : AppColors.error;

    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: (_) {
              setState(() => _isPressed = true);
              _animationController.forward();
              HapticFeedback.lightImpact();
            },
            onTapUp: (_) {
              setState(() => _isPressed = false);
              _animationController.reverse();
              widget.onTap();
            },
            onTapCancel: () {
              setState(() => _isPressed = false);
              _animationController.reverse();
            },
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: _isPressed 
                        ? color.withOpacity(0.3)
                        : Colors.black.withOpacity(0.08),
                    blurRadius: _isPressed ? 20 : 10,
                    offset: Offset(0, _isPressed ? 8 : 4),
                  ),
                ],
                border: Border.all(
                  color: _isPressed 
                      ? color.withOpacity(0.3)
                      : Colors.grey.withOpacity(0.1),
                  width: _isPressed ? 2 : 1,
                ),
              ),
              child: Stack(
                children: [
                  // Main Content
                  Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Icon Container
                        Container(
                          width: 60,
                          height: 60,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                color.withOpacity(0.1),
                                color.withOpacity(0.2),
                              ],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: color.withOpacity(0.3),
                              width: 1,
                            ),
                          ),
                          child: Icon(
                            _getCategoryIcon(widget.category),
                            size: 28,
                            color: color,
                          ),
                        ),
                        
                        const SizedBox(height: 16),
                        
                        // Category Name
                        Text(
                          widget.category.displayName,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: AppColors.textPrimary,
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        
                        const SizedBox(height: 8),
                        
                        // Transaction Count
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: color.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            '12 transactions', // This would come from actual data
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: color,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // Edit Button
                  Positioned(
                    top: 8,
                    right: 8,
                    child: GestureDetector(
                      onTap: () {
                        HapticFeedback.lightImpact();
                        widget.onEdit();
                      },
                      child: Container(
                        width: 32,
                        height: 32,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Icon(
                          Icons.edit_rounded,
                          size: 16,
                          color: Colors.grey[600],
                        ),
                      ),
                    ),
                  ),
                  
                  // Type Indicator
                  Positioned(
                    top: 8,
                    left: 8,
                    child: Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: color,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  IconData _getCategoryIcon(TransactionCategory category) {
    switch (category) {
      case TransactionCategory.food:
        return Icons.restaurant_rounded;
      case TransactionCategory.transportation:
        return Icons.directions_car_rounded;
      case TransactionCategory.shopping:
        return Icons.shopping_bag_rounded;
      case TransactionCategory.entertainment:
        return Icons.movie_rounded;
      case TransactionCategory.healthcare:
        return Icons.local_hospital_rounded;
      case TransactionCategory.education:
        return Icons.school_rounded;
      case TransactionCategory.utilities:
        return Icons.receipt_long_rounded;
      case TransactionCategory.housing:
        return Icons.home_rounded;
      case TransactionCategory.travel:
        return Icons.flight_rounded;
      case TransactionCategory.salary:
        return Icons.work_rounded;
      case TransactionCategory.freelance:
        return Icons.laptop_rounded;
      case TransactionCategory.investment_income:
        return Icons.trending_up_rounded;
      case TransactionCategory.business:
        return Icons.business_rounded;
      case TransactionCategory.rental:
        return Icons.apartment_rounded;
      case TransactionCategory.bonus:
        return Icons.card_giftcard_rounded;
      case TransactionCategory.gift:
        return Icons.redeem_rounded;
      case TransactionCategory.insurance:
        return Icons.security_rounded;
      case TransactionCategory.debt:
        return Icons.credit_card_rounded;
      case TransactionCategory.savings:
        return Icons.savings_rounded;
      case TransactionCategory.charity:
        return Icons.volunteer_activism_rounded;
      case TransactionCategory.business_expense:
        return Icons.business_center_rounded;
      case TransactionCategory.other_income:
      case TransactionCategory.other_expense:
        return Icons.category_rounded;
      default:
        return Icons.category_rounded;
    }
  }
}
