import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'dart:math' as math;
import '../../../core/constants/app_colors.dart';
import '../../../core/models/budget.dart';
import '../../../core/models/transaction.dart';
import '../../../core/services/currency_service.dart';
import '../screens/budget_screen.dart';

class EnhancedBudgetCard extends StatefulWidget {
  final Budget budget;
  final VoidCallback onTap;
  final VoidCallback onEdit;

  const EnhancedBudgetCard({
    super.key,
    required this.budget,
    required this.onTap,
    required this.onEdit,
  });

  @override
  State<EnhancedBudgetCard> createState() => _EnhancedBudgetCardState();
}

class _EnhancedBudgetCardState extends State<EnhancedBudgetCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _progressAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.2, curve: Curves.easeInOut),
    ));
    
    final progressPercentage = (widget.budget.spentAmount / widget.budget.budgetAmount).clamp(0.0, 1.0);
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: progressPercentage,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.3, 1.0, curve: Curves.easeOutCubic),
    ));

    // Start the animation when the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _animationController.forward();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final progressPercentage = (widget.budget.spentAmount / widget.budget.budgetAmount).clamp(0.0, 1.0);
    final isOverBudget = widget.budget.spentAmount > widget.budget.budgetAmount;
    final remainingAmount = widget.budget.budgetAmount - widget.budget.spentAmount;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _isPressed ? _scaleAnimation.value : 1.0,
          child: GestureDetector(
            onTapDown: (_) {
              setState(() => _isPressed = true);
              HapticFeedback.lightImpact();
            },
            onTapUp: (_) {
              setState(() => _isPressed = false);
              widget.onTap();
            },
            onTapCancel: () {
              setState(() => _isPressed = false);
            },
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(24),
                boxShadow: [
                  BoxShadow(
                    color: _isPressed 
                        ? (isOverBudget ? AppColors.error : AppColors.primary).withOpacity(0.2)
                        : Colors.black.withOpacity(0.08),
                    blurRadius: _isPressed ? 20 : 12,
                    offset: Offset(0, _isPressed ? 8 : 6),
                  ),
                ],
                border: Border.all(
                  color: _isPressed 
                      ? (isOverBudget ? AppColors.error : AppColors.primary).withOpacity(0.3)
                      : Colors.grey.withOpacity(0.1),
                  width: _isPressed ? 2 : 1,
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: Row(
                  children: [
                    // Circular Progress Indicator
                    SizedBox(
                      width: 80,
                      height: 80,
                      child: Stack(
                        children: [
                          // Background circle
                          SizedBox(
                            width: 80,
                            height: 80,
                            child: CircularProgressIndicator(
                              value: 1.0,
                              strokeWidth: 6,
                              backgroundColor: Colors.grey[200],
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.grey[200]!),
                            ),
                          ),
                          // Animated progress circle
                          SizedBox(
                            width: 80,
                            height: 80,
                            child: CircularProgressIndicator(
                              value: _progressAnimation.value,
                              strokeWidth: 6,
                              backgroundColor: Colors.transparent,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                isOverBudget ? AppColors.error : AppColors.primary,
                              ),
                            ),
                          ),
                          // Center content
                          Positioned.fill(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  _getCategoryIcon(widget.budget.category),
                                  color: isOverBudget ? AppColors.error : AppColors.primary,
                                  size: 24,
                                ),
                                const SizedBox(height: 2),
                                AnimatedBuilder(
                                  animation: _progressAnimation,
                                  builder: (context, child) {
                                    return Text(
                                      '${(_progressAnimation.value * 100).toInt()}%',
                                      style: TextStyle(
                                        fontSize: 12,
                                        fontWeight: FontWeight.bold,
                                        color: isOverBudget ? AppColors.error : AppColors.primary,
                                      ),
                                    );
                                  },
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    const SizedBox(width: 20),
                    
                    // Budget Details
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Header Row
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      widget.budget.name,
                                      style: const TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.w600,
                                        color: AppColors.textPrimary,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      '${widget.budget.period.name.toUpperCase()} Budget',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              GestureDetector(
                                onTap: () {
                                  HapticFeedback.lightImpact();
                                  widget.onEdit();
                                },
                                child: Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: Colors.grey[100],
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Icon(
                                    Icons.more_vert_rounded,
                                    size: 16,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ),
                            ],
                          ),
                          
                          const SizedBox(height: 16),
                          
                          // Amount Row
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Spent',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Obx(() {
                                    final currencyService = CurrencyService.to;
                                    return Text(
                                      currencyService.currentCurrency.value.formatAmount(widget.budget.spentAmount),
                                      style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                        color: isOverBudget ? AppColors.error : AppColors.textPrimary,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    );
                                  }),
                                ],
                              ),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  Text(
                                    'Budget',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Obx(() {
                                    final currencyService = CurrencyService.to;
                                    return Text(
                                      currencyService.currentCurrency.value.formatAmount(widget.budget.budgetAmount),
                                      style: const TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                        color: AppColors.textPrimary,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    );
                                  }),
                                ],
                              ),
                            ],
                          ),
                          
                          const SizedBox(height: 12),
                          
                          // Status Message
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 8,
                            ),
                            decoration: BoxDecoration(
                              color: _getStatusColor(isOverBudget, progressPercentage).withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  _getStatusIcon(isOverBudget, progressPercentage),
                                  size: 14,
                                  color: _getStatusColor(isOverBudget, progressPercentage),
                                ),
                                const SizedBox(width: 6),
                                Text(
                                  _getStatusMessage(isOverBudget, progressPercentage, remainingAmount),
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                    color: _getStatusColor(isOverBudget, progressPercentage),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Color _getStatusColor(bool isOverBudget, double progressPercentage) {
    if (isOverBudget) return AppColors.error;
    if (progressPercentage > 0.8) return AppColors.warning;
    if (progressPercentage > 0.6) return AppColors.primary;
    return AppColors.success;
  }

  IconData _getStatusIcon(bool isOverBudget, double progressPercentage) {
    if (isOverBudget) return Icons.warning_rounded;
    if (progressPercentage > 0.8) return Icons.info_outline_rounded;
    if (progressPercentage > 0.6) return Icons.trending_up_rounded;
    return Icons.check_circle_outline_rounded;
  }

  String _getStatusMessage(bool isOverBudget, double progressPercentage, double remainingAmount) {
    final currencyService = CurrencyService.to;
    if (isOverBudget) {
      return 'Over by ${currencyService.currentCurrency.value.formatAmount(-remainingAmount)}';
    }
    if (progressPercentage > 0.8) {
      return 'Almost there! ${currencyService.currentCurrency.value.formatAmount(remainingAmount)} left';
    }
    if (progressPercentage > 0.6) {
      return 'On track - ${currencyService.currentCurrency.value.formatAmount(remainingAmount)} remaining';
    }
    return 'Great progress! ${currencyService.currentCurrency.value.formatAmount(remainingAmount)} left';
  }

  IconData _getCategoryIcon(TransactionCategory category) {
    switch (category) {
      case TransactionCategory.food:
        return Icons.restaurant_rounded;
      case TransactionCategory.transportation:
        return Icons.directions_car_rounded;
      case TransactionCategory.shopping:
        return Icons.shopping_bag_rounded;
      case TransactionCategory.entertainment:
        return Icons.movie_rounded;
      case TransactionCategory.healthcare:
        return Icons.local_hospital_rounded;
      case TransactionCategory.education:
        return Icons.school_rounded;
      case TransactionCategory.utilities:
        return Icons.receipt_long_rounded;
      case TransactionCategory.housing:
        return Icons.home_rounded;
      case TransactionCategory.travel:
        return Icons.flight_rounded;
      case TransactionCategory.salary:
        return Icons.work_rounded;
      case TransactionCategory.freelance:
        return Icons.laptop_rounded;
      case TransactionCategory.investment_income:
        return Icons.trending_up_rounded;
      case TransactionCategory.business:
        return Icons.business_rounded;
      case TransactionCategory.rental:
        return Icons.apartment_rounded;
      case TransactionCategory.bonus:
        return Icons.card_giftcard_rounded;
      case TransactionCategory.gift:
        return Icons.redeem_rounded;
      case TransactionCategory.insurance:
        return Icons.security_rounded;
      case TransactionCategory.debt:
        return Icons.credit_card_rounded;
      case TransactionCategory.savings:
        return Icons.savings_rounded;
      case TransactionCategory.charity:
        return Icons.volunteer_activism_rounded;
      case TransactionCategory.business_expense:
        return Icons.business_center_rounded;
      case TransactionCategory.other_income:
      case TransactionCategory.other_expense:
        return Icons.category_rounded;
      default:
        return Icons.category_rounded;
    }
  }
}