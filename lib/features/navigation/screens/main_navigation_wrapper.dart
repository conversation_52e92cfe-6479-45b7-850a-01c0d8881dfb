import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/services/account_service.dart';
import '../../../core/services/scanner_service.dart';
import '../../../core/services/barcode_service.dart';
import '../../../core/models/account_type.dart';
import '../../../core/theme/design_system.dart';
import '../../../shared/widgets/sophisticated_bottom_nav.dart';
import '../../../shared/widgets/sophisticated_fab_menu.dart';
import '../../dashboard/screens/dashboard_screen.dart';
import '../../transactions/screens/transactions_screen.dart';
import '../../income/screens/income_screen.dart';
import '../../expenses/screens/expenses_screen.dart';
import '../../sales/screens/sales_screen.dart';
import '../../purchases/screens/purchases_screen.dart';
import '../../inventory/screens/inventory_screen.dart';
import '../../profile/screens/profile_screen.dart';
import '../../analytics/screens/analytics_screen.dart';
import '../../transactions/screens/add_universal_transaction_screen.dart';

class MainNavigationWrapper extends StatefulWidget {
  const MainNavigationWrapper({super.key});

  @override
  State<MainNavigationWrapper> createState() => _MainNavigationWrapperState();
}

class _MainNavigationWrapperState extends State<MainNavigationWrapper> {
  int _currentIndex = 0;

  // Personal account screens
  final List<Widget> _personalScreens = [
    const DashboardScreen(),
    const IncomeScreen(),
    const ExpensesScreen(),
    const ProfileScreen(),
  ];

  // Business account screens (with purchases)
  final List<Widget> _businessScreensWithPurchases = [
    const DashboardScreen(),
    const SalesScreen(),
    const PurchasesScreen(),
    const InventoryScreen(),
  ];

  // Business account screens (without purchases)
  final List<Widget> _businessScreensWithoutPurchases = [
    const DashboardScreen(),
    const SalesScreen(),
    const InventoryScreen(),
    const ProfileScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final accountService = AccountService.to;
      final currentAccount = accountService.currentAccount;
      
      if (currentAccount == null) {
        return const Scaffold(
          body: Center(
            child: CircularProgressIndicator(),
          ),
        );
      }

      final isBusinessAccount = accountService.currentAccountType == AccountType.business;

      // Determine which screens to use based on account type and settings
      List<Widget> screens;
      if (isBusinessAccount) {
        // For now, always show purchases. This can be made configurable later
        screens = _businessScreensWithPurchases;
      } else {
        screens = _personalScreens;
      }

      // Reset index if it's out of bounds for the current account type
      if (_currentIndex >= screens.length) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          setState(() {
            _currentIndex = 0;
          });
        });
      }

      return Scaffold(
        backgroundColor: DesignSystem.neutralBackground,
        body: IndexedStack(
          index: _currentIndex,
          children: screens,
        ),
        bottomNavigationBar: SophisticatedBottomNav(
          currentIndex: _currentIndex,
          onTap: (index) {
            setState(() {
              _currentIndex = index;
            });
          },
          onFabPressed: () => isBusinessAccount
              ? _showBusinessFabOptions(context)
              : _showPersonalFabOptions(context),
          items: isBusinessAccount
              ? _getBusinessNavItems()
              : _getPersonalNavItems(),
        ),
        extendBody: true,
      );
    });
  }

  List<SophisticatedNavItem> _getPersonalNavItems() {
    return const [
      SophisticatedNavItem(
        icon: Icons.home_outlined,
        activeIcon: Icons.home_rounded,
        label: 'Home',
      ),
      SophisticatedNavItem(
        icon: Icons.trending_up_outlined,
        activeIcon: Icons.trending_up_rounded,
        label: 'Income',
      ),
      SophisticatedNavItem(
        icon: Icons.trending_down_outlined,
        activeIcon: Icons.trending_down_rounded,
        label: 'Expenses',
      ),
      SophisticatedNavItem(
        icon: Icons.person_outline_rounded,
        activeIcon: Icons.person_rounded,
        label: 'Profile',
      ),
    ];
  }

  List<SophisticatedNavItem> _getBusinessNavItems() {
    return const [
      SophisticatedNavItem(
        icon: Icons.home_outlined,
        activeIcon: Icons.home_rounded,
        label: 'Home',
      ),
      SophisticatedNavItem(
        icon: Icons.point_of_sale_outlined,
        activeIcon: Icons.point_of_sale_rounded,
        label: 'Sales',
      ),
      SophisticatedNavItem(
        icon: Icons.shopping_cart_outlined,
        activeIcon: Icons.shopping_cart_rounded,
        label: 'Purchases',
      ),
      SophisticatedNavItem(
        icon: Icons.inventory_2_outlined,
        activeIcon: Icons.inventory_2_rounded,
        label: 'Inventory',
      ),
    ];
  }

  void _showPersonalFabOptions(BuildContext context) {
    showSophisticatedFabMenu(
      context,
      items: [
        FabMenuItem(
          icon: Icons.add_circle_rounded,
          label: 'Add Income',
          subtitle: 'Record earnings',
          color: DesignSystem.success,
          gradient: DesignSystem.incomeGradient,
          onTap: () {
            Get.toNamed('/add-transaction', arguments: {'isIncome': true});
          },
        ),
        FabMenuItem(
          icon: Icons.remove_circle_rounded,
          label: 'Add Expense',
          subtitle: 'Track spending',
          color: DesignSystem.error,
          gradient: DesignSystem.expenseGradient,
          onTap: () {
            Get.toNamed('/add-transaction', arguments: {'isIncome': false});
          },
        ),
        FabMenuItem(
          icon: Icons.qr_code_scanner_rounded,
          label: 'Scan Receipt',
          subtitle: 'Auto-extract data',
          color: DesignSystem.primarySkyBlue,
          onTap: () {
            ScannerService.to.showScannerOptions();
          },
        ),
        FabMenuItem(
          icon: Icons.analytics_rounded,
          label: 'Quick Report',
          subtitle: 'View insights',
          color: DesignSystem.warning,
          onTap: () {
            Get.toNamed('/analytics');
          },
        ),
      ],
    );
  }

  void _showBusinessFabOptions(BuildContext context) {
    showSophisticatedFabMenu(
      context,
      items: [
        FabMenuItem(
          icon: Icons.point_of_sale_rounded,
          label: 'New Sale',
          subtitle: 'Record revenue',
          color: DesignSystem.success,
          gradient: DesignSystem.incomeGradient,
          onTap: () {
            Get.toNamed('/add-transaction', arguments: {'isIncome': true, 'isBusiness': true});
          },
        ),
        FabMenuItem(
          icon: Icons.shopping_cart_rounded,
          label: 'New Purchase',
          subtitle: 'Track expenses',
          color: DesignSystem.warning,
          onTap: () {
            Get.toNamed('/add-transaction', arguments: {'isIncome': false, 'isBusiness': true});
          },
        ),
        FabMenuItem(
          icon: Icons.inventory_2_rounded,
          label: 'Add Product',
          subtitle: 'Manage inventory',
          color: DesignSystem.primarySkyBlue,
          onTap: () {
            Get.snackbar(
              'Feature Available',
              'Product management is available in the Inventory section',
              backgroundColor: DesignSystem.primarySkyBlue,
              colorText: Colors.white,
              duration: const Duration(seconds: 3),
            );
          },
        ),
        FabMenuItem(
          icon: Icons.qr_code_scanner_rounded,
          label: 'Scan Barcode',
          subtitle: 'Quick lookup',
          color: DesignSystem.secondaryCoral,
          onTap: () async {
            final barcode = await BarcodeService.to.showBarcodeScanner();
            if (barcode != null) {
              Get.snackbar('Barcode Scanned', 'Code: ${barcode.rawValue}');
            }
          },
        ),
        FabMenuItem(
          icon: Icons.receipt_long_rounded,
          label: 'Scan Receipt',
          subtitle: 'Auto-extract data',
          color: DesignSystem.primaryTeal,
          onTap: () {
            ScannerService.to.showScannerOptions();
          },
        ),
        FabMenuItem(
          icon: Icons.analytics_rounded,
          label: 'Quick Report',
          subtitle: 'Business insights',
          color: DesignSystem.primaryTeal,
          onTap: () {
            // TODO: Navigate to analytics
            Get.snackbar('Coming Soon', 'Business analytics will be implemented');
          },
        ),
      ],
    );
  }



  // Helper method to determine if purchases should be shown
  // This can be expanded to check account settings
  bool _shouldShowPurchases() {
    // For now, always show purchases for business accounts
    // In the future, this can be controlled by account settings
    return true;
  }
}
