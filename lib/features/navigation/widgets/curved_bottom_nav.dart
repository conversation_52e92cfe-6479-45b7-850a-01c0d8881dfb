import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/services/theme_service.dart';

class CurvedBottomNav extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;
  final VoidCallback onAddTransaction;
  final VoidCallback onScanReceipt;

  const CurvedBottomNav({
    super.key,
    required this.currentIndex,
    required this.onTap,
    required this.onAddTransaction,
    required this.onScanReceipt,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final themeService = ThemeService.to;
      
      return Stack(
        alignment: Alignment.topCenter,
        children: [
          // Custom Bottom Navigation Bar
          Container(
            height: 80,
            decoration: BoxDecoration(
              color: themeService.surfaceColor,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: ClipPath(
              clipper: _BottomNavClipper(),
              child: Container(
                color: themeService.surfaceColor,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    // Dashboard
                    _buildNavItem(
                      icon: Icons.dashboard_rounded,
                      label: 'Dashboard',
                      index: 0,
                      isSelected: currentIndex == 0,
                      themeService: themeService,
                    ),
                    
                    // Transactions
                    _buildNavItem(
                      icon: Icons.receipt_long_rounded,
                      label: 'Transactions',
                      index: 1,
                      isSelected: currentIndex == 1,
                      themeService: themeService,
                    ),
                    
                    // Spacer for FAB
                    const SizedBox(width: 60),
                    
                    // Analytics
                    _buildNavItem(
                      icon: Icons.analytics_rounded,
                      label: 'Analytics',
                      index: 2,
                      isSelected: currentIndex == 2,
                      themeService: themeService,
                    ),
                    
                    // Settings
                    _buildNavItem(
                      icon: Icons.settings_rounded,
                      label: 'Settings',
                      index: 3,
                      isSelected: currentIndex == 3,
                      themeService: themeService,
                    ),
                  ],
                ),
              ),
            ),
          ),
          
          // Floating Action Button with Scanner
          Positioned(
            top: -10,
            child: _buildFABWithScanner(themeService),
          ),
        ],
      );
    });
  }

  Widget _buildNavItem({
    required IconData icon,
    required String label,
    required int index,
    required bool isSelected,
    required ThemeService themeService,
  }) {
    return GestureDetector(
      onTap: () => onTap(index),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: isSelected 
                    ? themeService.primaryColor.withOpacity(0.1)
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: isSelected 
                    ? themeService.primaryColor
                    : themeService.textSecondaryColor,
                size: 24,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: Get.textTheme.labelSmall?.copyWith(
                color: isSelected 
                    ? themeService.primaryColor
                    : themeService.textSecondaryColor,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFABWithScanner(ThemeService themeService) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Scanner Button
        Container(
          margin: const EdgeInsets.only(right: 8),
          child: FloatingActionButton.small(
            onPressed: onScanReceipt,
            backgroundColor: themeService.secondaryColor,
            foregroundColor: themeService.getContrastingTextColor(themeService.secondaryColor),
            heroTag: "scanner",
            child: const Icon(Icons.qr_code_scanner_rounded, size: 20),
          ),
        ),
        
        // Main FAB
        FloatingActionButton(
          onPressed: onAddTransaction,
          backgroundColor: themeService.primaryColor,
          foregroundColor: themeService.getContrastingTextColor(themeService.primaryColor),
          heroTag: "add_transaction",
          child: const Icon(Icons.add_rounded, size: 28),
        ),
      ],
    );
  }
}

class _BottomNavClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    final path = Path();
    
    // Start from bottom left
    path.lineTo(0, size.height);
    
    // Bottom edge
    path.lineTo(size.width, size.height);
    
    // Right edge
    path.lineTo(size.width, 0);
    
    // Top edge with curve for FAB
    final centerX = size.width / 2;
    final curveWidth = 80.0;
    final curveHeight = 20.0;
    
    // Line to start of curve
    path.lineTo(centerX + curveWidth / 2, 0);
    
    // Curve for FAB
    path.quadraticBezierTo(
      centerX + curveWidth / 4, curveHeight,
      centerX, curveHeight,
    );
    path.quadraticBezierTo(
      centerX - curveWidth / 4, curveHeight,
      centerX - curveWidth / 2, 0,
    );
    
    // Complete the path
    path.lineTo(0, 0);
    path.close();
    
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}
