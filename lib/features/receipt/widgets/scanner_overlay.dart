import 'package:flutter/material.dart';
import '../../../core/constants/app_colors.dart';

class ScannerOverlay extends StatefulWidget {
  const ScannerOverlay({super.key});

  @override
  State<ScannerOverlay> createState() => _ScannerOverlayState();
}

class _ScannerOverlayState extends State<ScannerOverlay>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _setupAnimation();
  }

  void _setupAnimation() {
    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final scanAreaSize = size.width * 0.8;
    final scanAreaHeight = scanAreaSize * 1.2; // Receipt aspect ratio

    return Stack(
      children: [
        // Dark overlay
        Container(
          color: Colors.black.withOpacity(0.5),
        ),
        
        // Clear scanning area
        Center(
          child: Container(
            width: scanAreaSize,
            height: scanAreaHeight,
            decoration: BoxDecoration(
              color: Colors.transparent,
              borderRadius: BorderRadius.circular(16),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: Container(
                color: Colors.transparent,
              ),
            ),
          ),
        ),

        // Corner brackets
        Center(
          child: SizedBox(
            width: scanAreaSize,
            height: scanAreaHeight,
            child: Stack(
              children: [
                // Top-left corner
                Positioned(
                  top: 0,
                  left: 0,
                  child: _buildCornerBracket(
                    topLeft: true,
                  ),
                ),
                
                // Top-right corner
                Positioned(
                  top: 0,
                  right: 0,
                  child: _buildCornerBracket(
                    topRight: true,
                  ),
                ),
                
                // Bottom-left corner
                Positioned(
                  bottom: 0,
                  left: 0,
                  child: _buildCornerBracket(
                    bottomLeft: true,
                  ),
                ),
                
                // Bottom-right corner
                Positioned(
                  bottom: 0,
                  right: 0,
                  child: _buildCornerBracket(
                    bottomRight: true,
                  ),
                ),
              ],
            ),
          ),
        ),

        // Scanning line animation
        Center(
          child: SizedBox(
            width: scanAreaSize,
            height: scanAreaHeight,
            child: AnimatedBuilder(
              animation: _animation,
              builder: (context, child) {
                return Stack(
                  children: [
                    Positioned(
                      top: _animation.value * (scanAreaHeight - 4),
                      left: 0,
                      right: 0,
                      child: Container(
                        height: 4,
                        decoration: BoxDecoration(
                          gradient: const LinearGradient(
                            colors: [
                              Colors.transparent,
                              AppColors.primary,
                              Colors.transparent,
                            ],
                          ),
                          borderRadius: BorderRadius.circular(2),
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.primary.withOpacity(0.5),
                              blurRadius: 8,
                              spreadRadius: 2,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ),

        // Instructions
        Positioned(
          top: MediaQuery.of(context).padding.top + 100,
          left: 24,
          right: 24,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.7),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Column(
              children: [
                Icon(
                  Icons.receipt_long,
                  color: Colors.white,
                  size: 32,
                ),
                SizedBox(height: 8),
                Text(
                  'Receipt Scanner',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  'Position the receipt within the frame for best results',
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),

        // Tips
        Positioned(
          bottom: 200,
          left: 24,
          right: 24,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.7),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Column(
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.lightbulb_outline,
                      color: Colors.amber,
                      size: 20,
                    ),
                    SizedBox(width: 8),
                    Text(
                      'Tips for better scanning:',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 8),
                Text(
                  '• Ensure good lighting\n• Keep receipt flat and straight\n• Include the entire receipt\n• Avoid shadows and glare',
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: 12,
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCornerBracket({
    bool topLeft = false,
    bool topRight = false,
    bool bottomLeft = false,
    bool bottomRight = false,
  }) {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        border: Border(
          top: (topLeft || topRight) 
              ? const BorderSide(color: AppColors.primary, width: 4)
              : BorderSide.none,
          bottom: (bottomLeft || bottomRight)
              ? const BorderSide(color: AppColors.primary, width: 4)
              : BorderSide.none,
          left: (topLeft || bottomLeft)
              ? const BorderSide(color: AppColors.primary, width: 4)
              : BorderSide.none,
          right: (topRight || bottomRight)
              ? const BorderSide(color: AppColors.primary, width: 4)
              : BorderSide.none,
        ),
        borderRadius: BorderRadius.only(
          topLeft: topLeft ? const Radius.circular(16) : Radius.zero,
          topRight: topRight ? const Radius.circular(16) : Radius.zero,
          bottomLeft: bottomLeft ? const Radius.circular(16) : Radius.zero,
          bottomRight: bottomRight ? const Radius.circular(16) : Radius.zero,
        ),
      ),
    );
  }
}
