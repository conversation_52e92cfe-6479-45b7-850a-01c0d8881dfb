import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

import 'core/constants/app_strings.dart';
import 'core/services/appwrite_service.dart';
import 'core/services/auth_service.dart';
import 'core/services/database_service.dart';
import 'core/services/account_service.dart';
import 'core/services/personal_transaction_service.dart';
import 'core/services/subscription_service.dart';
import 'core/services/theme_service.dart';
import 'core/services/scanner_service.dart';
import 'core/services/data_management_service.dart';
import 'core/services/recurring_transaction_service.dart';
import 'core/services/sync_service.dart';
import 'core/services/file_upload_service.dart';
import 'core/services/error_handling_service.dart';
import 'core/services/security_service.dart';
import 'core/services/api_key_service.dart';
import 'core/services/currency_service.dart';
import 'core/services/biometric_service.dart';
import 'core/services/data_export_service.dart';
import 'core/services/notification_service.dart';
import 'core/services/budget_service.dart';
import 'core/services/local_storage_service.dart';
import 'core/services/data_sync_service.dart';
import 'core/services/sms_parser_service.dart';
import 'core/services/transaction_classifier_service.dart';
import 'core/services/transaction_charges_service.dart';
import 'core/services/receipt_scanner_service.dart';
import 'core/services/sms_permissions_service.dart';
import 'core/services/sms_background_service.dart';
import 'core/services/transaction_confidence_service.dart';
import 'core/services/savings_service.dart';
import 'core/routes/app_routes.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize GetStorage
  await GetStorage.init();

  // Set system UI overlay style
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.white,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );

  // Initialize services
  await _initializeServices();

  runApp(const RekodiApp());
}

Future<void> _initializeServices() async {
  // Initialize error handling service first
  Get.put(ErrorHandlingService(), permanent: true);

  // Initialize security service
  Get.put(SecurityService(), permanent: true);

  // Initialize API key service
  Get.put(ApiKeyService(), permanent: true);

  // Initialize database service first (singleton)
  Get.put(DatabaseService(), permanent: true);

  // Initialize Appwrite service
  Get.put(AppwriteService(), permanent: true);

  // Initialize authentication service
  Get.put(AuthService(), permanent: true);

  // Initialize theme service
  Get.put(ThemeService(), permanent: true);

  // Initialize account service and wait for it to complete
  Get.put(AccountService(), permanent: true);
  await Get.find<AccountService>().onInit();

  // Initialize services that depend on AccountService
  Get.put(PersonalTransactionService(), permanent: true);
  await Get.find<PersonalTransactionService>().onInit();

  // Initialize savings service
  Get.put(SavingsService(), permanent: true);
  await Get.find<SavingsService>().onInit();

  // Initialize remaining services
  Get.put(LocalStorageService(), permanent: true);
  await Get.find<LocalStorageService>().onInit();

  Get.put(DataSyncService(), permanent: true);

  Get.put(SubscriptionService(), permanent: true);
  Get.put(ScannerService(), permanent: true);
  Get.put(ReceiptScannerService(), permanent: true);
  Get.put(DataManagementService(), permanent: true);
  Get.put(RecurringTransactionService(), permanent: true);
  Get.put(BudgetService(), permanent: true);
  Get.put(SmsParserService(), permanent: true);
  Get.put(TransactionChargesService(), permanent: true);
  Get.put(TransactionClassifierService(), permanent: true);
  Get.put(SmsPermissionsService(), permanent: true);
  Get.put(SmsBackgroundService(), permanent: true);
  Get.put(TransactionConfidenceService(), permanent: true);
  Get.put(SyncService(), permanent: true);
  Get.put(FileUploadService(), permanent: true);
  Get.put(CurrencyService(), permanent: true);
  Get.put(BiometricService(), permanent: true);
  Get.put(DataExportService(), permanent: true);
  Get.put(NotificationService(), permanent: true);
}

class RekodiApp extends StatelessWidget {
  const RekodiApp({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final themeService = Get.find<ThemeService>();

      return GetMaterialApp(
        title: AppStrings.appName,
        debugShowCheckedModeBanner: false,
        theme: themeService.currentTheme,
        initialRoute: AppRoutes.splash,
        getPages: AppRoutes.routes,
      );
    });
  }
}
